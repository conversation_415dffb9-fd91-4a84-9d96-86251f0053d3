{"name": "price-server", "private": true, "version": "1.0.0", "scripts": {"deploy:prod": "sls deploy --stage prod", "format": "prettier --write \"src/**/*.ts\"", "serve": "node --max-old-space-size=8192 node_modules/serverless/bin/serverless offline start", "test": "jest", "test-coin": "LOCAL_TEST=true ts-node --transpile-only src/test.ts ", "test:watch": "jest --watch", "refill": "tableName='prod-coins-table' AWS_REGION='eu-central-1' ts-node --transpile-only src/refill", "storeBridgedCoins": "ts-node --transpile-only src/cli/storeBridgedCoins", "build": "sls package", "runner-test": "export AWS_REGION='eu-central-1' && export tableName='prod-coins-table' && npx ts-node --transpile-only src/scripts/test.ts", "fetch-cg-hourly": "export AWS_REGION='eu-central-1' && export tableName='prod-coins-table' && npx ts-node --transpile-only src/scripts/coingecko.ts true", "fetch-cg-min": "export AWS_REGION='eu-central-1' && export tableName='prod-coins-table' && npx ts-node --transpile-only src/scripts/coingecko.ts false", "store-defi-coins": "export AWS_REGION='eu-central-1' && export tableName='prod-coins-table' && npx ts-node --transpile-only src/scripts/defiCoins.ts", "store-bridge-coins": "export AWS_REGION='eu-central-1' && export tableName='prod-coins-table' && npx ts-node --transpile-only src/scripts/bridges.ts", "update-token-mapping": "node src/adapters/utils/updateTokenMapping.js", "check-rpc-blocks": "ts-node src/scripts/checkRpcBlocks.ts"}, "devDependencies": {"@babel/preset-env": "^7.13.12", "@babel/preset-typescript": "^7.13.0", "@types/aws-lambda": "^8.10.72", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.0.0", "@types/node": "^14.14.35", "@types/node-fetch": "^2.5.10", "aws-sdk": "^2.1692.0", "babel-jest": "^29.0.0", "babel-loader": "^8.2.2", "esbuild": "^0.14.38", "jest": "^29.0.0", "prettier": "^2.2.1", "serverless": "^2.31.0", "serverless-esbuild": "^1.26.2", "serverless-offline": "^7.0.0", "serverless-plugin-log-retention": "^2.0.0", "serverless-prune-plugin": "^1.4.4", "ts-jest": "^29.0.0", "ts-loader": "^8.0.18", "ts-node": "^10.0.0", "typescript": "^5.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.354.0", "@defillama/sdk": "^5.0.152", "@ethersproject/bignumber": "^5.5.0", "@project-serum/anchor": "^0.26.0", "@sentry/serverless": "^6.19.7", "@sentry/tracing": "^6.19.7", "@solana/web3.js": "^1.73.3", "@supercharge/promise-pool": "^2.1.0", "ajv": "^6.12.6", "axios": "^1.6.7", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dotenv": "^8.6.0", "ethers": "^6.0.0", "fs": "^0.0.1-security", "graphql": "^16.6.0", "graphql-request": "^5.1.0", "ioredis": "^5.3.2", "kafkajs": "^2.2.4", "node-fetch": "^2.7.0", "p-limit": "^3.1.0", "path": "^0.12.7", "postgres": "^3.4.3", "simple-git": "^3.27.0", "starknet": "^5.24.3"}}