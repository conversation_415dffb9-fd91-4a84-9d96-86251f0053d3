require("dotenv").config();
import { successResponse, wrap, IResponse } from "./utils/shared";
import getRecordClosestToTimestamp from "./utils/shared/getRecordClosestToTimestamp";
import { CoinsResponse, getBasicCoins } from "./utils/getCoinsUtils";
import { quantisePeriod } from "./utils/timestampUtils";

const handler = async (
  event: AWSLambda.APIGatewayEvent
): Promise<IResponse> => {
  const requestedCoins = (event.pathParameters?.coins ?? "").split(",");
  const searchWidth: number = quantisePeriod(
    event.queryStringParameters?.searchWidth?.toLowerCase() ?? "6h"
  );
  const timestampRequested = Number(event.pathParameters!.timestamp);
  const { PKTransforms, coins } = await getBasicCoins(requestedCoins);
  const response = {} as CoinsResponse;
  await Promise.all(
    coins.map(async (coin) => {
      const finalCoin = await getRecordClosestToTimestamp(
        coin.redirect ?? coin.PK,
        timestampRequested,
        searchWidth
      );
      if (finalCoin.SK === undefined) {
        // if (process.env.DEFILLAMA_SDK_MUTED == "true") {
        //   const currentCoin = await getRecordClosestToTimestamp(
        //     coin.redirect ?? coin.PK,
        //     getCurrentUnixTimestamp(),
        //     DAY / 4
        //   );
        //   if (currentCoin.SK == undefined) {
        //     return;
        //   }
        //   await currentCoin.adapter()
        // } else {
        return;
        // }
      }
      PKTransforms[coin.PK].forEach((coinName) => {
        response[coinName] = {
          decimals: coin.decimals,
          symbol: coin.symbol,
          price: finalCoin.price,
          timestamp: finalCoin.SK,
          confidence: finalCoin.confidence
        };
      });
    })
  );
  return successResponse(
    {
      coins: response
    },
    3600
  ); // 1 hour cache
};

export default wrap(handler);

// handler({
//   pathParameters: {
//     coins:
//       "ethereum:******************************************,ethereum:******************************************",
//     //"1664897509" //
//     timestamp: "1656944730"
//   }
// });
// ts-node coins/src/getHistoricalCoins.ts
