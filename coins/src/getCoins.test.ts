
import fetch from "node-fetch";
import handler from "./getCoins";

describe("snapshot of error provided", () => {
  it("executes as expected", async () => {
    const body = JSON.stringify({
        "coins": [
            "ethereum:******************************************",
            "avax:******************************************",
            "bsc:******************************************",
            "solana:********************************************",
            "dogechain:******************************************",
            "avax:******************************************",
            "bsc:******************************************"
        ]
    })
    const response = JSON.parse(((await handler({body} as any)) as any).body)
    expect(response).toEqual(await fetch(`https://coins.llama.fi/prices`, {
        method: "POST",
        body
    }).then(r=>r.json()));
  });
});
