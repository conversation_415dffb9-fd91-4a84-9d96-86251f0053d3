export default {
  ethereum: [
    {
      underlying: '******************************************',
      nfts: [
        {   // Autoglyphs
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Azuki
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // BAKC
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // BAYC
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Beanz
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // CloneX
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Cool Cats
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Cryptoadz
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Cryptodickbutts
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Cryptopunks
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Cyberbrokers
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Decentraland
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Digidaigaku
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Doodles
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Forgotten Rune Wizards Cult
          token: "******************************************",
          oracle: "******************************************",
        },
        {   // Goblin Town
          token: "0xbCe3781ae7Ca1a5e050Bd9C4c77369867eBc307e",
          oracle: "0x11a67a301b80BC9b8cC0A5826b84876fb8542CaF",
        },
        {   // LobsterDAO
          token: "0x026224A2940bFE258D0dbE947919B62fE321F042",
          oracle: "0xd2fa1CAcF83C9889f215d0492BFceE717D149a6e",
        },
        {   // MAYC
          token: "0x60e4d786628fea6478f785a6d7e704777c86a7c6",
          oracle: "0x1823C89715Fe3fB96A24d11c917aCA918894A090",
        },
        {   // Meebits
          token: "0x7bd29408f11d2bfc23c34f18275bbf23bb716bc7",
          oracle: "0x29Ea94760f211A338eCef4a31F09d8Cef1795755",
        },
        {   // Milady
          token: "0x5Af0D9827E0c53E4799BB226655A1de152A425a5",
          oracle: "0xf04205d907aD314c717EFec0d2D3d97626130E19",
        },
        {   // Moonbirds
          token: "0x23581767a106ae21c074b2276d25e5c3e136a68b",
          oracle: "0x16De3b3D1620675D7BD240abEf4CE4F119462Bbd",
        },
        {   // Nouns
          token: "0x9c8ff314c9bc7f6e59a9d9225fb22946427edc03",
          oracle: "0x363B6E3648847B988B7C8E3A306e0881BdEE24Bd",
        },
        {   // Otherdeed
          token: "0x34d85c9CDeB23FA97cb08333b511ac86E1C4E258",
          oracle: "0xAa6128fAdBd64aAd55d2A235827d976508649509",
        },
        {   // Pudgy Penguins
          token: "0xbd3531da5cf5857e7cfaa92426877b022e612cf8",
          oracle: "0xaC9962D846D431254C7B3Da3AA12519a1E2Eb5e7",
        },
        {   // Sandbox Land
          token: "0x5cc5b05a8a13e3fbdb0bb9fccd98d38e50f90c38",
          oracle: "0xa62b4828a9f4b2e3cba050c6befdd8f0a0056af4",
        },
        // {   // Squiggles
        //   token: "0x059EDD72Cd353dF5106D2B9cC5ab83a52287aC3a",
        //   oracle: "0xEbF67AB8cFF336D3F609127E8BbF8BD6DD93cd81",
        // },
        {   // VeeFriends
          token: "0xa3aee8bce55beea1951ef834b99f3ac60d1abeeb",
          oracle: "0x35bf6767577091E7f04707c0290b3f889e968307",
        },
        {   // World of Women
          token: "0xe785e82358879f061bc3dcac6f0444462d4b5330",
          oracle: "0xDdf0B85C600DAF9e308AFed9F597ACA212354764",
        },
        {   // Sewer Pass
          token: "0x764AeebcF425d56800eF2c84F2578689415a2DAa",
          oracle: "0xA75a865d973276ba2f6D6A75C2D02f5d0c07b06f",
        },
      ]
    }
  ]
}
