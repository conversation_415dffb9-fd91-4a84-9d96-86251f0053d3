{"ethereum": {"usdc": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "usdt": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "eth": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "meth": {"underlying": "******************************************", "pools": ["******************************************"]}, "sgeth": {"underlying": "******************************************", "pools": ["******************************************"]}, "usdd": {"underlying": "******************************************", "pools": ["******************************************"]}, "dai": {"underlying": "******************************************", "pools": ["******************************************"]}, "frax": {"underlying": "******************************************", "pools": ["******************************************"]}, "susd": {"underlying": "******************************************", "pools": ["******************************************"]}, "lusd": {"underlying": "******************************************", "pools": ["******************************************"]}, "mai": {"underlying": "******************************************", "pools": ["******************************************"]}, "metis": {"underlying": "******************************************", "pools": ["0xd8772edBF88bBa2667ed011542343b0eDDaCDa47", "0xF14EEe033D8b00101aB147F87cB238a2d3E74940"]}, "metis.usdt": {"underlying": "0xdAC17F958D2ee523a2206206994597C13D831ec7", "pools": ["0x430Ebff5E3E80A6C58E7e6ADA1d90F5c28AA116d"]}}, "bsc": {"busd": {"underlying": "0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56", "pools": ["0x98a5737749490856b401DB5Dc27F522fC314A4e1"]}, "usdt": {"underlying": "0x55d398326f99059fF775485246999027B3197955", "pools": ["0x9aA83081AA06AF7208Dcc7A4cB72C94d057D2cda"]}, "usdd": {"underlying": "0xd17479997F34dd9156Deef8F95A52D81D265be9c", "pools": ["0x4e145a589e4c03cBe3d28520e4BF3089834289Df"]}, "mai": {"underlying": "******************************************", "pools": ["0x7BfD7f2498C4796f10b6C611D9db393D3052510C"]}, "metis": {"underlying": "0xe552Fb52a4F19e44ef5A967632DBc320B0820639", "pools": ["0xD4CEc732b3B135eC52a3c0bc8Ce4b8cFb9dacE46"]}, "metis.usdt": {"underlying": "0x55d398326f99059fF775485246999027B3197955", "pools": ["0x68C6c27fB0e02285829e69240BE16f32C5f8bEFe"]}, "usdc": {"underlying": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d", "pools": ["0xd5a9B8b07e9bA3D492b801c84B69E292476805B3"]}}, "avax": {"usdc": {"underlying": "0xB97EF9Ef8734C71904D8002F8b6Bc66Dd9c48a6E", "pools": ["0x1205f31718499dBf1fCa446663B532Ef87481fe1", "0xaDA7A31B692e3AbFccD02C3d7f8aDc5944510291"]}, "usdt": {"underlying": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "pools": ["0x29e38769f23701A2e4A8Ef0492e19dA4604Be62c"]}, "frax": {"underlying": "0xD24C2Ad096400B6FBcd2ad8B24E7acBc21A1da64", "pools": ["0x1c272232Df0bb6225dA87f4dEcD9d37c32f63Eea"]}, "mai": {"underlying": "0x5c49b268c9841AFF1Cc3B0a418ff5c3442eE3F3b", "pools": ["******************************************"]}, "metis.usdt": {"underlying": "0x9702230A8Ea53601f5cD2dc00fDBc13d4dF4A8c7", "pools": ["0xEAe5c2F6B25933deB62f754f239111413A0A25ef"]}}, "polygon": {"usdc": {"underlying": "0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174", "pools": ["0x1205f31718499dBf1fCa446663B532Ef87481fe1", "******************************************"]}, "usdt": {"underlying": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "pools": ["0x29e38769f23701A2e4A8Ef0492e19dA4604Be62c"]}, "dai": {"underlying": "0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063", "pools": ["0x1c272232Df0bb6225dA87f4dEcD9d37c32f63Eea"]}, "maimatic": {"underlying": "******************************************", "pools": ["******************************************"]}}, "arbitrum": {"usdc": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "usdt": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "eth": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "sgeth": {"underlying": "******************************************", "pools": ["******************************************"]}, "frax": {"underlying": "******************************************", "pools": ["******************************************"]}, "mai": {"underlying": "******************************************", "pools": ["******************************************"]}, "lusd": {"underlying": "******************************************", "pools": ["******************************************"]}}, "optimism": {"usdc": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "eth": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "sgeth": {"underlying": "******************************************", "pools": ["******************************************"]}, "dai": {"underlying": "******************************************", "pools": ["******************************************"]}, "frax": {"underlying": "******************************************", "pools": ["******************************************"]}, "susd": {"underlying": "******************************************", "pools": ["******************************************"]}, "lusd": {"underlying": "******************************************", "pools": ["******************************************"]}, "mai": {"underlying": "******************************************", "pools": ["******************************************"]}}, "fantom": {"usdc": {"underlying": "******************************************", "pools": ["******************************************"]}}, "kava": {"usdt": {"underlying": "******************************************", "pools": ["******************************************"]}}, "base": {"eth": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}, "sgeth": {"underlying": "******************************************", "pools": ["******************************************"]}, "usdc": {"underlying": "******************************************", "pools": ["******************************************", "******************************************"]}}, "mantle": {"eth": {"underlying": "******************************************", "pools": ["******************************************"]}, "usdc": {"underlying": "******************************************", "pools": ["******************************************"]}}, "metis": {"eth": {"underlying": "******************************************", "pools": ["******************************************"]}}, "scroll": {"eth": {"underlying": "******************************************", "pools": ["******************************************"]}, "usdc": {"underlying": "******************************************", "pools": ["******************************************"]}}, "linea": {"eth": {"underlying": "******************************************", "pools": ["******************************************"]}}, "aurora": {"usdc": {"underlying": "******************************************", "pools": ["******************************************"]}}, "lightlink_phoenix": {"eth": {"underlying": "******************************************", "pools": ["******************************************"]}}}