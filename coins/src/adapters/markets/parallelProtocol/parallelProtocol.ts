import { addToDBWritesList } from "../../utils/database";
import { Write } from "../../utils/dbInterfaces";
import { call } from "@defillama/sdk/build/abi";
import getBlock from "../../utils/block";

export const STABLES: Record<
  string,
  { symbol: string; address: string; feed?: string }[]
> = {
  ethereum: [
    {
      symbol: "PAR",
      address: "******************************************",
      feed: "******************************************",
    },
    {
      symbol: "paUSD",
      address: "******************************************",
      feed: undefined,
    },
  ],
};

export async function getTokenPrices(chain: string, timestamp: number) {
  const stables = STABLES[chain] ?? [];
  const writes: Write[] = [];
  const block = await getBlock(chain, timestamp);

  for (let i = 0; i < stables.length; i++) {
    const { symbol, address, feed } = stables[i];
    const price = feed ? await getPrice(block, chain, feed) : 1;

    addToDBWritesList(
      writes,
      chain,
      address,
      price,
      18,
      symbol,
      timestamp,
      "parallel",
      1,
    );
  }
  return writes;
}

async function getPrice(
  block: number | undefined,
  chain: any,
  target: string,
): Promise<number> {
  const { output } = await call({ target, chain, block, abi });
  return output / 10 ** 8;
}

const abi = {
  name: "latestAnswer",
  outputs: [
    {
      internalType: "int256",
      name: "",
      type: "int256",
    },
  ],
  stateMutability: "view",
  type: "function",
};
