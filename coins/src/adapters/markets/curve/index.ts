import getTokenPrices2 from "./curve2";
import getGaugePrices from "./gauges";

const defaultRegistries = [
  "stableswap",
  "crypto",
  "stableFactory",
  "cryptoFactory",
];
export function curve(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("ethereum", ["crypto"], timestamp),
    getTokenPrices2("ethereum", [], timestamp, "eth-custom", [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "0x28ca243dc0ac075dd012fcf9375c25d18a844d96",
      "0x8ffc7b89412efd0d17edea2018f6634ea4c2fcb2",
      "0xd6982da59f1d26476e259559508f4135135cf9b8",
      "0x189b4e49b5caf33565095097b4b960f14032c7d0",
      "0x1ee81c56e42ec34039d993d12410d437ddea341e",
      "0x74345504eaea3d9408fc69ae7eb2d14095643c5b",
      "0xca554e2e2948a211d4650fe0f4e271f01f9cb5f1",
      "0x27cb9629ae3ee05cb266b99ca4124ec999303c9d",
      "0xd7bf9bb6bd088317effd116e2b70ea3a054cbceb",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ]),
    getGaugePrices("ethereum", timestamp),
  ]);
}
export function curve1(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("fantom", defaultRegistries, timestamp),
    getGaugePrices("fantom", timestamp),
  ]);
}
export function curve2(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("arbitrum", defaultRegistries, timestamp),
    getTokenPrices2("arbitrum", [], timestamp, "eth-custom", [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "0xad37295881f53fe6fdab54493a06cd84f988646b",
      "0x186cf879186986a20aadfb7ead50e3c20cb26cec",
      "0x86ea1191a219989d2da3a85c949a12a92f8ed3db",
      "0x93a416206b4ae3204cfe539edfee6bc05a62963e",
      "0x1fb84fa6d252762e8367ea607a6586e09dcebe3d",
      "0xe07f1151887b8fdc6800f737252f6b91b46b5865",
      "0xd3ca9bec3e681b0f578fd87f20ebcf2b7e0bb739",
      "0xa6c2e6a83d594e862cdb349396856f7ffe9a979b",
      "0x6579758e9e85434450d638cfbea0f2fe79856dda",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ]),
    getGaugePrices("arbitrum", timestamp),
  ]);
}
export function curve3(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("optimism", defaultRegistries, timestamp),
    getTokenPrices2("optimism", [], timestamp, "eth-custom", [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ]),
    getGaugePrices("optimism", timestamp),
  ]);
}
export function curve4(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("polygon", defaultRegistries, timestamp),
    getTokenPrices2("polygon", [], timestamp, "eth-custom", [
      "******************************************",
      "******************************************",
      "******************************************",
    ]),
    getGaugePrices("polygon", timestamp),
  ]);
}
export function curve5(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("avax", defaultRegistries, timestamp),
    getGaugePrices("avax", timestamp),
  ]);
}
export function curve6(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("base", [], timestamp, "eth-custom", [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ]),
  ]);
}
export function curve7(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("moonbeam", defaultRegistries, timestamp),
    getGaugePrices("moonbeam", timestamp),
  ]);
}
export function curve8(timestamp: number = 0) {
  return Promise.all([getTokenPrices2("aurora", defaultRegistries, timestamp)]);
}
export function curve9(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("celo", defaultRegistries, timestamp),
    getGaugePrices("celo", timestamp),
  ]);
}
export function curve10(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("bsc", defaultRegistries, timestamp),
    getTokenPrices2("bsc", [], timestamp, "eth-custom", [
      "******************************************",
    ]),
  ]);
}
export function curve11(timestamp: number = 0) {
  return Promise.all([getTokenPrices2("bsc", ["pcs"], timestamp, "pcs")]);
}
export function curve12(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("xdai", defaultRegistries, timestamp),
    getGaugePrices("xdai", timestamp),
  ]);
}
export function curve13(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("kava", defaultRegistries, timestamp),
    getGaugePrices("kava", timestamp),
  ]);
}
export function curve14(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("fraxtal", defaultRegistries, timestamp),
    getGaugePrices("fraxtal", timestamp),
  ]);
}
export function curve15(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices2("sonic", defaultRegistries, timestamp),
    // getGaugePrices("sonic", timestamp),
  ]);
}

export const adapters = {
  curve,
  curve1,
  curve2,
  curve3,
  curve4,
  curve5,
  curve6,
  curve7,
  curve8,
  curve9,
  curve10,
  curve11,
  curve12,
  curve13,
  curve14,
  curve15,
};
