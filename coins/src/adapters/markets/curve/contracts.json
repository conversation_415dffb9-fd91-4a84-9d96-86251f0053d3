{"ethereum": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "CRV": "******************************************", "veCRV": "******************************************", "wrapped": "******************************************", "metapoolBases": {"3CRV": "******************************************", "crvRenWSBTC": "******************************************"}, "yearnTokens": {"yDAI1": "******************************************", "yUSDT1": "******************************************", "yDAI2": "******************************************", "yUSDC": "******************************************", "yUSDT2": "******************************************", "ycDAI": "******************************************", "ycUSDC": "******************************************", "ycUSDT": "******************************************"}, "creamTokens": {"cyDAI": "******************************************", "cyUSDC": "******************************************", "cyUSDT": "******************************************"}, "sdTokens": {"sdCrv": "******************************************", "sdANGLE": "******************************************", "sdFXS": "******************************************", "sdBAL": "******************************************"}, "fxTokens": {"ibKRW": {"address": "0x95dfdc8161832e4ff7816ac4b6367ce201538253", "currency": "KRW"}}}, "polygon": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270", "metapoolBases": {"am3CRV": "0x19793b454d3afc7b454f206ffe95ade26ca6912c", "4eur-f": "0xad326c253a84e9805559b73a08724e11e49ca651"}, "blacklist": ["0xd6d9bc8e2b894b5c73833947abdb5031cc7a4894", "0xa7fd7d83e2d63f093b71c5f3b84c27cff66a7802", "0xacfbe6979d58b55a681875fc9adad0da4a37a51b"]}, "arbitrum": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0x82af49447d8a07e3bd95bd0d56f35241523fbab1", "metapoolBases": {"2CRV": "0xbf7e49483881c76487b0989cd7d9a8239b20ca41"}}, "aurora": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0xc9bdeed33cd01541e1eed10f90519d2c06fe3feb", "metapoolBases": {"3CRV": "0xbf7e49483881c76487b0989cd7d9a8239b20ca41"}}, "avax": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0xb31f66aa3c1e785363f0875a1b74e27b85fd66c7", "metapoolBases": {"av3CRV": "0xb0d2eb3c2ca3c6916fab8dcbf9d9c165649231ae"}}, "fantom": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0x21be370d5312f44cb42ce377bc9b8a0cef1a4c83", "metapoolBases": {"2CRV": "0x27e611fd27b276acbd5ffd632e5eaebec9761e40", "g3CRV": "0xd4f94d0aaa640bbb72b5eec2d85f6d114d81a88e"}, "creamTokens": {"iDAI": "0x04c762a5df2fa02fe868f25359e0c259fb811cfe", "iUSDC": "0x328a7b4d538a2b3942653a9983fda3c12c571141", "iFUSDT": "0x70fac71debfd67394d1278d98a29dea79dc6e57a", "scUSDC": "0xe45ac34e528907d0a0239ab5db507688070b20bf", "scDAI": "0x8d9aed9882b4953a0c9fa920168fa1fdfa0ebe75", "scFUSDT": "0x02224765bc8d54c21bb51b0951c80315e1c263f9", "scMIM": "0x90b7c21be43855afd2515675fc307c084427404f"}, "yearnTokens": {}}, "harmony": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0xcf664087a5bb0237a0bad6742852ec6c8d69a27a", "metapoolBases": {"3CRV": "0xc5cfada84e902ad92dd40194f0883ad49639b023"}}, "optimism": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0x4200000000000000000000000000000000000006", "metapoolBases": {"3CRV": "0x061b87122ed14b9526a813209c8a59a633257bab"}}, "xdai": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0xe91d153e0b41518a2ce8dd3d7944fa863463a97d", "metapoolBases": {"3CRV": "0x1337bedc9d22ecbe766df105c9623922a27963ec"}}, "moonbeam": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0x4244eb811d6e0ef302326675207a95113db4e1f8", "metapoolBases": {}}, "bsc": {"addressProvider": "0x31d236483a15f9b9dd60b36d4013d75e9dbf852b", "gasTokenDummy": "******************************************", "wrapped": "0x968f6f898a6df937fc1859b323ac2f14643e3fed"}, "celo": {"addressProvider": "0x0000000022d53366457f9d5e68ec105046fc4383", "gasTokenDummy": "******************************************", "wrapped": "0x471ece3750da237f93b8e339c536989b8978a438"}, "kava": {"addressProvider": "******************************************", "gasTokenDummy": "******************************************", "wrapped": "0xc86c7C0eFbd6A49B35E8714C5f59D99De09A225b"}, "base": {"addressProvider": "0x5ffe7FB82894076ECB99A30D6A32e969e6e35E98", "gasTokenDummy": "******************************************", "wrapped": "0x4200000000000000000000000000000000000006"}, "fraxtal": {"addressProvider": "0x5ffe7FB82894076ECB99A30D6A32e969e6e35E98", "gasTokenDummy": "******************************************", "wrapped": "0x4200000000000000000000000000000000000006"}, "sonic": {"addressProvider": "0x87FE17697D0f14A222e8bEf386a0860eCffDD617", "gasTokenDummy": "******************************************", "wrapped": "0x039e2fb66102314ce7b64ce5ce3e5183bc94ad38"}}