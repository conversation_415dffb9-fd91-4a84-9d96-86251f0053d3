import * as sdk from "@defillama/sdk";
import getTokenPrices from "./aktionariat";

export function aktionariat(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "ethereum",
      sdk.graph.modifyEndpoint('2ZoJCp4S7YP7gbYN2ndsYNjPeZBV1PMti7BBoPRRscNq'),
      timestamp,
      ["******************************************"] // ignore only xchf, other stable pairings are not used 
    ),
    getTokenPrices(
      "optimism",
      sdk.graph.modifyEndpoint('********************************************'),
      timestamp,
      ["******************************************"] // ignore only xchf, other stable pairings are not used
    ),
  ]);
}