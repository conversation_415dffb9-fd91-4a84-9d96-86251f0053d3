{"bonders": {"USDC": {"ethereum": {"optimism": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "base": "******************************************"}, "optimism": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "base": "******************************************"}, "arbitrum": {"ethereum": "******************************************", "optimism": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "base": "******************************************"}, "gnosis": {"ethereum": "******************************************", "arbitrum": "******************************************", "optimism": "******************************************", "polygon": "******************************************", "base": "******************************************"}, "polygon": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************", "base": "******************************************"}, "base": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************", "polygon": "******************************************"}}, "USDT": {"ethereum": {"optimism": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "optimism": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "arbitrum": {"ethereum": "******************************************", "optimism": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "gnosis": {"ethereum": "******************************************", "arbitrum": "******************************************", "optimism": "******************************************", "polygon": "******************************************"}, "polygon": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************"}}, "MATIC": {"ethereum": {"gnosis": "******************************************", "polygon": "******************************************"}, "gnosis": {"ethereum": "******************************************", "polygon": "******************************************"}, "polygon": {"ethereum": "******************************************", "gnosis": "******************************************"}}, "DAI": {"ethereum": {"optimism": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "optimism": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "arbitrum": {"ethereum": "******************************************", "optimism": "******************************************", "gnosis": "******************************************", "polygon": "******************************************"}, "gnosis": {"ethereum": "******************************************", "arbitrum": "******************************************", "optimism": "******************************************", "polygon": "******************************************"}, "polygon": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************"}}, "ETH": {"ethereum": {"optimism": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "nova": "******************************************", "base": "******************************************"}, "optimism": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "nova": "******************************************", "base": "******************************************"}, "arbitrum": {"ethereum": "******************************************", "optimism": "******************************************", "gnosis": "******************************************", "polygon": "******************************************", "nova": "******************************************", "base": "******************************************"}, "gnosis": {"ethereum": "******************************************", "arbitrum": "******************************************", "optimism": "******************************************", "polygon": "******************************************", "nova": "******************************************", "base": "******************************************"}, "polygon": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************", "nova": "******************************************", "base": "******************************************"}, "nova": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************", "polygon": "******************************************", "base": "******************************************"}, "base": {"ethereum": "******************************************", "arbitrum": "******************************************", "gnosis": "******************************************", "optimism": "******************************************", "polygon": "******************************************", "nova": "******************************************"}}, "HOP": {"ethereum": {"optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "optimism": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "arbitrum": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "gnosis": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "polygon": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "nova": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "base": "0x881296Edcb252080bd476c464cEB521d08df7631"}, "base": {"ethereum": "0x881296Edcb252080bd476c464cEB521d08df7631", "arbitrum": "0x881296Edcb252080bd476c464cEB521d08df7631", "gnosis": "0x881296Edcb252080bd476c464cEB521d08df7631", "optimism": "0x881296Edcb252080bd476c464cEB521d08df7631", "polygon": "0x881296Edcb252080bd476c464cEB521d08df7631", "nova": "0x881296Edcb252080bd476c464cEB521d08df7631"}}, "SNX": {"ethereum": {"optimism": "0x547d28cDd6A69e3366d6aE3EC39543F09Bd09417"}, "optimism": {"ethereum": "0x547d28cDd6A69e3366d6aE3EC39543F09Bd09417"}}, "sUSD": {"ethereum": {"optimism": "0x547d28cDd6A69e3366d6aE3EC39543F09Bd09417"}, "optimism": {"ethereum": "0x547d28cDd6A69e3366d6aE3EC39543F09Bd09417"}}, "rETH": {"ethereum": {"optimism": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94", "arbitrum": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94"}, "optimism": {"ethereum": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94", "arbitrum": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94"}, "arbitrum": {"ethereum": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94", "optimism": "0xD38B96277df34F1f7ac5965F86016E7d02c4Ca94"}}, "MAGIC": {"ethereum": {"arbitrum": "******************************************", "nova": "******************************************"}, "arbitrum": {"ethereum": "******************************************", "nova": "******************************************"}, "nova": {"ethereum": "******************************************", "arbitrum": "******************************************"}}}, "rewardsContracts": {"USDC": {"arbitrum": ["******************************************"], "optimism": ["******************************************"], "polygon": ["******************************************", "******************************************"], "gnosis": ["******************************************", "******************************************"], "base": ["******************************************"]}, "USDT": {"arbitrum": ["******************************************"], "optimism": ["******************************************"], "polygon": ["******************************************", "******************************************"], "gnosis": ["******************************************", "******************************************"]}, "DAI": {"arbitrum": ["******************************************"], "optimism": ["******************************************"], "polygon": ["******************************************", "******************************************"], "gnosis": ["******************************************", "******************************************"]}, "MATIC": {"polygon": ["******************************************"]}, "ETH": {"arbitrum": ["******************************************"], "optimism": ["******************************************"], "polygon": ["******************************************", "******************************************"], "gnosis": ["******************************************", "******************************************"], "base": ["******************************************"]}, "SNX": {"optimism": ["******************************************", "******************************************"]}, "sUSD": {"optimism": ["******************************************", "******************************************"]}, "rETH": {"arbitrum": ["******************************************"], "optimism": ["******************************************"]}, "MAGIC": {"arbitrum": ["******************************************"], "nova": ["******************************************"]}}, "bridges": {"USDC": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 12650032}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 16617211}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x1e1607db33D38715544E595A5D8f94557C487DfA", "l2CanonicalBridge": "0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174", "l2CanonicalToken": "0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1FxBaseRootTunnel": "0x1e1607db33D38715544E595A5D8f94557C487DfA", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "******************************************", "l2MessengerProxy": "0xf8024972590cad31C47C41371ff5c98956311747", "bridgeDeployedBlockNumber": 15810014}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x6587a6164B091a058aCba2e91f971454Ec172940", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "0x7F5c764cBc14f9669B88837ca1490cCa17c31607", "l2Bridge": "0xa81D244A1814468C734E5b4101F7b9c0c577a8fC", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "0x2ad09850b0CA4c7c1B33f5AcD6cBAbCaB5d6e796", "l2SaddleSwap": "0x3c0FFAca566fCcfD9Cc95139FEF6CBA143795963", "l2SaddleLpToken": "0x2e17b8193566345a2Dd467183526dEdc42d2d5A8", "bridgeDeployedBlockNumber": 1}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x39Bf4A32E689B6a79360854b7c901e991085D6a3", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "0xFF970A61A04b1cA14834A43f5dE4533eBDDB5CC8", "l2Bridge": "0x0e0E3d2C5c292161999474247956EF542caBF8dd", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "0xB67c014FA700E69681a673876eb8BAFAA36BFf71", "bridgeDeployedBlockNumber": 440027}, "base": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x4a55e8e407609A3046804ca500BeF6F5ebaCb6F9", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 2077758}}, "USDT": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 12860139}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 17155195}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x1CD391bd1D915D189dE162F0F1963C07E60E4CD6", "l2CanonicalBridge": "0xc2132D05D31c914a87C6611C10748AEb04B58e8F", "l2CanonicalToken": "0xc2132D05D31c914a87C6611C10748AEb04B58e8F", "l2Bridge": "0x6c9a1ACF73bd85463A46B0AFc076FBdf602b690B", "l2HopBridgeToken": "0x9F93ACA246F457916E49Ec923B8ed099e313f763", "l2AmmWrapper": "0x8741Ba6225A6BF91f9D73531A98A89807857a2B3", "l2SaddleSwap": "0xB2f7d27B21a69a033f85C42d5EB079043BAadC81", "l2SaddleLpToken": "0x3cA3218D6c52B640B0857cc19b69Aa9427BC842C", "l1FxBaseRootTunnel": "0x1CD391bd1D915D189dE162F0F1963C07E60E4CD6", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "******************************************", "l2MessengerProxy": "0x73C657d54E8512E59c691eA38a3Fa17a5e838c4c", "bridgeDeployedBlockNumber": 17058878}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x9fc22E269c3752620EB281ce470855886b982501", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "0x94b008aA00579c1307B0EF2c499aD98a8ce58e58", "l2Bridge": "******************************************", "l2HopBridgeToken": "0x2057C8ECB70Afd7Bee667d76B4CD373A325b1a20", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "0xeC4B41Af04cF917b54AEb6Df58c0f8D78895b5Ef", "l2SaddleLpToken": "0xF753A50fc755c6622BBCAa0f59F0522f264F006e", "bridgeDeployedBlockNumber": 1}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x967F8E2B66D624Ad544CB59a230b867Ac3dC60dc", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 441562}}, "MATIC": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 12969385}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 17444924}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "0x0000000000000000000000000000000000001010", "l2CanonicalToken": "0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270", "l2Bridge": "0x553bC791D746767166fA3888432038193cEED5E2", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1FxBaseRootTunnel": "******************************************", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "******************************************", "l2MessengerProxy": "******************************************", "bridgeDeployedBlockNumber": 17669097}}, "DAI": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 13226217}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 18093617}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x172cAbe34c757472249aD4Bd97560373fBbf0DA3", "l2CanonicalBridge": "0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063", "l2CanonicalToken": "0x8f3Cf7ad23Cd3CaDbD9735AFf958023239c6A063", "l2Bridge": "0xEcf268Be00308980B5b3fcd0975D47C4C8e1382a", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "0x28529fec439cfF6d7D1D5917e956dEE62Cd3BE5c", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "0x8b7aA8f5cc9996216A88D900df8B8a0a3905939A", "l1FxBaseRootTunnel": "0x172cAbe34c757472249aD4Bd97560373fBbf0DA3", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "******************************************", "l2MessengerProxy": "0x60013Ae36DdD3aF49b2bdB7Fb1545c5C1CF21638", "bridgeDeployedBlockNumber": 19112518}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x115F423b958A2847af0F5bF314DB0f27c644c308", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "0x7191061D5d4C60f598214cC6913502184BAddf18", "l2HopBridgeToken": "0x56900d66D74Cb14E3c86895789901C9135c95b16", "l2AmmWrapper": "0xb3C68a491608952Cb1257FC9909a537a0173b63B", "l2SaddleSwap": "0xF181eD90D6CfaC84B8073FdEA6D34Aa744B41810", "l2SaddleLpToken": "0x22D63A26c730d49e5Eab461E4f5De1D8BdF89C92", "bridgeDeployedBlockNumber": 1}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x2d6fd82C7f531328BCaCA96EF985325C0894dB62", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 945326}}, "ETH": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 13331564}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 18359713}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x26a1fDdaCfb9F6F5072eE5636ED3429101E6C069", "l2CanonicalBridge": "0x7ceB23fD6bC0adD59E62ac25578270cFf1b9f619", "l2CanonicalToken": "0x7ceB23fD6bC0adD59E62ac25578270cFf1b9f619", "l2Bridge": "0xb98454270065A31D71Bf635F6F7Ee6A518dFb849", "l2HopBridgeToken": "0x1fDeAF938267ca43388eD1FdB879eaF91e920c7A", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "0x971039bF0A49c8d8A675f839739eE7a42511eC91", "l1FxBaseRootTunnel": "0x26a1fDdaCfb9F6F5072eE5636ED3429101E6C069", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "0x8484Ef722627bf18ca5Ae6BcF031c23E6e922B30", "l2MessengerProxy": "0xf2119C5AeB734590477EAE272990773df722B49D", "bridgeDeployedBlockNumber": 19706859}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0xa45DF1A388049fb8d76E72D350d24E2C3F7aEBd1", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "0x83f6244Bd87662118d96D9a6D44f09dffF14b30E", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 1}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0xDD378a11475D588908001E0E99E4fD89ABda5434", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "0x82aF49447D8a07e3bd95BD0d56f35241523fBab1", "l2Bridge": "0x3749C4f034022c39ecafFaBA182555d4508caCCC", "l2HopBridgeToken": "0xDa7c0de432a9346bB6e96aC74e3B61A36d8a77eB", "l2AmmWrapper": "0x33ceb27b39d2Bb7D2e61F7564d3Df29344020417", "l2SaddleSwap": "0x652d27c0F72771Ce5C76fd400edD61B406Ac6D97", "l2SaddleLpToken": "0x59745774Ed5EfF903e615F5A2282Cae03484985a", "bridgeDeployedBlockNumber": 2135598}, "nova": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x468F5E5a77C78275c3A6Df6A59fF5DBED2559F74", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "0x722E8BdD2ce80A4422E880164f2079488e115365", "l2Bridge": "******************************************", "l2HopBridgeToken": "0xba9d3040E79Ec1e27c67fe8b184F552fE9398F07", "l2AmmWrapper": "0xd6bFB71b5Ad5fD378CaC15C72D8652E3b8D542c4", "l2SaddleSwap": "0x9908109e51D093cF17ef945Df2C62cb17cB877c4", "l2SaddleLpToken": "0x82f9EC21C1FB86F2B31734e27F695199E996f41E", "bridgeDeployedBlockNumber": 1724464}, "base": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x17B5ACE1cD6b0d033431873826937F499Eec2C95", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 1397238}}, "HOP": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 15592825}, "gnosis": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1Amb": "******************************************", "l2Amb": "******************************************", "bridgeDeployedBlockNumber": 24309494}, "polygon": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0xAa1603822b43e592e33b58d34B4423E1bcD8b4dC", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "0x58c61AeE5eD3D748a1467085ED2650B697A66234", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "l1FxBaseRootTunnel": "0xAa1603822b43e592e33b58d34B4423E1bcD8b4dC", "l1PosRootChainManager": "******************************************", "l1PosPredicate": "******************************************", "l2MessengerProxy": "0x54eBc18D68C664DA7100981dD9353566dC15f6B8", "bridgeDeployedBlockNumber": 33447834}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x9D3A7fB18CA7F1237F977Dc5572883f8b24F5638", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 24710333}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x41BF5Fd5D1C85f00fd1F23C77740F1A7eBa6A35c", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 26088165}, "nova": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0xEbC49b236ff1920C788eF3c0687A3A1B6fCB35f1", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "0x02D47f76523d2f059b617E4346de67482792eB83", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 15937602}, "base": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "0x86eD3B8AD6b721fD3a2Fa73c227987Fb9AD3D1Ae", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 1859764}}, "SNX": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 15634323}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 26092038}}, "sUSD": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 15678491}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 27410328}}, "rETH": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 17141336}, "optimism": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 94877802}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 85020329}}, "MAGIC": {"ethereum": {"l1CanonicalToken": "******************************************", "l1Bridge": "******************************************", "bridgeDeployedBlockNumber": 17445930}, "arbitrum": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 99536351}, "nova": {"l1CanonicalBridge": "******************************************", "l1MessengerWrapper": "******************************************", "l2CanonicalBridge": "******************************************", "l2CanonicalToken": "******************************************", "l2Bridge": "******************************************", "l2HopBridgeToken": "******************************************", "l2AmmWrapper": "******************************************", "l2SaddleSwap": "******************************************", "l2SaddleLpToken": "******************************************", "bridgeDeployedBlockNumber": 11559944}}}, "bonderFeeBps": {"USDC": {"ethereum": 7, "polygon": 7, "gnosis": 7, "optimism": 7, "arbitrum": 7, "nova": 7, "base": 7}, "USDT": {"ethereum": 20, "polygon": 20, "gnosis": 20, "optimism": 20, "arbitrum": 20}, "DAI": {"ethereum": 26, "polygon": 26, "gnosis": 30, "optimism": 26, "arbitrum": 26}, "MATIC": {"ethereum": 5, "polygon": 5, "gnosis": 5, "optimism": 0, "arbitrum": 0}, "ETH": {"ethereum": 5, "polygon": 5, "gnosis": 5, "optimism": 5, "arbitrum": 5, "nova": 5, "zksync": 5, "linea": 5, "scrollzk": 5, "base": 5}, "WBTC": {"ethereum": 23, "polygon": 23, "gnosis": 25, "optimism": 23, "arbitrum": 23}, "HOP": {"ethereum": 20, "polygon": 20, "gnosis": 20, "optimism": 20, "arbitrum": 20, "nova": 20, "base": 20}, "SNX": {"ethereum": 20}, "sUSD": {"ethereum": 20}, "rETH": {"ethereum": 15, "optimism": 15, "arbitrum": 15}, "MAGIC": {"ethereum": 20, "arbitrum": 20, "nova": 20}}, "destinationFeeGasPriceMultiplier": 1.2, "relayerFeeEnabled": {"polygon": false, "gnosis": false, "optimism": false, "arbitrum": false, "nova": false, "base": false, "zksync": false, "linea": false, "scrollzk": false, "polygonzk": false}}