{"getVirtualPrice": {"inputs": [], "name": "getVirtualPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "getToken": {"inputs": [{"internalType": "uint8", "name": "index", "type": "uint8"}], "name": "getToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}}