import { Write, } from "../../utils/dbInterfaces";
import { getApi } from "../../utils/sdk";
import { addToDBWritesList, getTokenAndRedirectDataMap } from "../../utils/database";

export const config = {
  ethereum: {
    lps: [
      '******************************************',
      '******************************************',
    ],
  },
  polygon: {
    lps: [
      '******************************************',
    ],
  },
} as any

export default async function getTokenPrices(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp)

  const { lps } = config[chain]
  const writes: Write[] = [];
  await _getWrites()

  return writes

  async function _getWrites() {
    if (!lps.length) return;
    const [
      token0s, token1s, supplies, decimals, symbols, uBalances
    ] = await Promise.all([
      api.multiCall({ abi: 'address:token0', calls: lps }),
      api.multiCall({ abi: 'address:token1', calls: lps }),
      api.multiCall({ abi: 'uint256:totalSupply', calls: lps }),
      api.multiCall({ abi: 'erc20:decimals', calls: lps }),
      api.multiCall({ abi: "string:symbol", calls: lps }),
      api.multiCall({ abi: 'function getUnderlyingBalances() view returns (uint256 token0Bal, uint256 token1Bal)', calls: lps }),
    ])
    const coinData = await getTokenAndRedirectDataMap([...token0s, ...token1s], chain, timestamp)

    uBalances.forEach(({ token0Bal, token1Bal }: any, i: number) => {
      const t0Data = coinData[token0s[i].toLowerCase()]
      const t1Data = coinData[token1s[i].toLowerCase()]

      if (!t0Data || !t1Data) return;

      const t0Value = (t0Data.price * token0Bal) / (10 ** t0Data.decimals)
      const t1Value = (t1Data.price * token1Bal) / (10 ** t1Data.decimals)
      const price = (t0Value + t1Value) / (supplies[i] / (10 ** decimals[i]))
      const t0confidence = t0Data.confidence ?? 0.8
      const t1confidence = t1Data.confidence ?? 0.8
      const confidence = t0confidence < t1confidence ? t0confidence : t1confidence

      addToDBWritesList(writes, chain, lps[i], price, decimals[i], symbols[i], timestamp, 'arrakis', confidence)
    })
  }
}
