export const OFTs: { [symbol: string]: { [chain: string]: string[] } } = {
  "7007": {
    Arbitrum: ["0xa4889a7023e375344f7fc42ca164d8aadf5e8dcc"],
  },
  USDC: {
    Aptos: [
      "******************************************",
      "******************************************",
      "******************************************",
      "0xff970a61a04b1ca14834a43f5de4533ebddb5cc8",
      "******************************************",
      "******************************************",
    ],
    Fantom: [
      "0x04068da6c83afcfa0e13ba15a6696662335d5b75",
      "0x28a92dde19d9989f39a49905d7c9c2fac7799bdf",
    ],
    Arbitrum: [
      "0xff970a61a04b1ca14834a43f5de4533ebddb5cc8",
      "******************************************",
      "0x1c3979c2bb4f0e6dcb75daf22ad0741cf7d5f160",
    ],
    Avalanche: ["******************************************"],
    Optimism: [
      "******************************************",
      "******************************************",
    ],
    Polygon: [
      "******************************************",
      "******************************************",
    ],
    Base: ["******************************************"],
    "BNB Chain": [
      "******************************************",
      "******************************************",
    ],
    Ethereum: [
      "******************************************",
      "******************************************",
    ],
    Harmony: [
      "******************************************",
      "******************************************",
    ],
    "Core Blockchain Mainnet": [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    Taiko: ["******************************************"],
    Mantle: ["******************************************"],
    Scroll: ["******************************************"],
    Islander: ["******************************************"],
    Iota: ["******************************************"],
    Ink: ["******************************************"],
    Sei: ["0x3894085ef7ff0f0aedf52e2a2704928d1ec074f1"],
    Superposition: ["0x6c030c5cc283f791b26816f325b9c632d964f8a1"],
    Xchain: ["******************************************"],
    "Aurora Mainnet": ["0x368ebb46aca6b8d0787c96b2b20bd3cc3f2c45f7"],
    "Klaytn Mainnet Cypress": ["0xe2053bcf56d2030d2470fb454574237cf9ee3d4b"],
    Flare: ["******************************************"],
    "Fuse Mainnet": ["0xc6bc407706b7140ee8eef2f86f9504651b63e7f9"],
    "Plume Mainnet": ["0x78adD880A697070c1e765Ac44D65323a0DcCE913"],
    Gravity: ["******************************************"],
    "Rari Chain": ["******************************************"],
    Degen: ["******************************************"],
  },
  MATIC: {
    Polygon: ["\\\\n"],
    "BNB Chain": ["0x8b97b6ac20721c1536465d4d77a867749b3831f4"],
  },
  USDT: {
    Avalanche: ["******************************************"],
    "BNB Chain": [
      "******************************************",
      "0x0551ca9e33bada0355dfce34685ad3b73cf3ad70",
    ],
    Polygon: ["******************************************"],
    "Core Blockchain Mainnet": [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
      "0x900101d06a7426441ae63e9ab3b9b0f63be145f1",
    ],
    Metis: ["******************************************"],
    Aptos: [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    Arbitrum: ["******************************************"],
    Harmony: [
      "******************************************",
      "******************************************",
    ],
    Ethereum: [
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    Optimism: ["******************************************"],
    Kava: ["******************************************"],
    Mantle: ["******************************************"],
    Taiko: ["******************************************"],
    EBI: ["******************************************"],
    Flare: ["******************************************"],
    "Klaytn Mainnet Cypress": ["******************************************"],
    Gravity: ["******************************************"],
    Sei: ["******************************************"],
    Iota: ["******************************************"],
    Fantom: ["******************************************"],
    Lightlink: ["******************************************"],
    Ink: ["******************************************"],
    "Plume Mainnet": ["******************************************"]
  },
  FTM: {
    Fantom: ["\\\\n"],
  },
  SGETH: {
    Base: ["******************************************"],
    Arbitrum: ["******************************************"],
    Linea: ["******************************************"],
    Optimism: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  EURA: {
    Gnosis: ["******************************************"],
    "Celo Mainnet": ["******************************************"],
    Polygon: ["******************************************"],
    Optimism: ["******************************************"],
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Linea: ["******************************************"],
    Avalanche: ["******************************************"],
    Base: ["******************************************"],
    "Polygon zkEVM": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  "BTC.b": {
    Polygon: ["******************************************"],
    Avalanche: ["******************************************"],
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Optimism: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  BNB: {
    "BNB Chain": ["\\\\n"],
    "opBNB Mainnet": ["\\\\n"],
  },
  MIM: {
    "BNB Chain": ["******************************************"],
    Fantom: ["******************************************"],
    Polygon: ["******************************************"],
    Arbitrum: ["******************************************"],
    Avalanche: ["******************************************"],
    Moonriver: ["******************************************"],
    Optimism: ["******************************************"],
    Kava: ["******************************************"],
    Ethereum: ["******************************************"],
    Base: ["******************************************"],
  },
  KLAY: {
    "Klaytn Mainnet Cypress": ["\\\\n"],
  },
  AVAX: {
    Avalanche: ["\\\\n"],
  },
  STG: {
    Fantom: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Polygon: ["******************************************"],
    Arbitrum: ["******************************************"],
    Avalanche: ["******************************************"],
    Base: ["******************************************"],
    Linea: ["******************************************"],
    Optimism: ["******************************************"],
    Mantle: ["******************************************"],
    Kava: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  ETH: {
    Base: ["******************************************", "\\\\n"],
    Ethereum: ["\\\\n", "******************************************"],
    Arbitrum: ["\\\\n", "******************************************"],
    "zkSync Era Mainnet": ["\\\\n"],
    Optimism: ["******************************************", "\\\\n"],
    Linea: ["\\\\n", "******************************************"],
    "Polygon zkEVM": ["\\\\n"],
    "Arbitrum Nova": ["\\\\n"],
    Scroll: ["******************************************", "\\\\n"],
    Zora: ["\\\\n"],
    "Orderly Mainnet": ["\\\\n"],
    "Aurora Mainnet": ["\\\\n"],
    Manta: ["\\\\n"],
    "BNB Chain": ["******************************************"],
    Blast: ["\\\\n"],
    Abstract: ["******************************************"],
    Lightlink: ["******************************************"],
    "Rari Chain": ["\\\\n"],
  },
  DAI: {
    Gnosis: ["\\\\n"],
    Optimism: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Polygon: ["******************************************"],
    Harmony: [
      "******************************************",
      "******************************************",
    ],
    Ethereum: ["******************************************"],
    Arbitrum: ["******************************************"],
  },
  WETH: {
    Aptos: [
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    Ethereum: [
      "******************************************",
      "******************************************",
    ],
    Arbitrum: ["******************************************"],
    Optimism: ["******************************************"],
    Metis: ["******************************************"],
    Mantle: ["******************************************"],
    "Core Blockchain Mainnet": ["******************************************"],
    Islander: ["******************************************"],
    Bera: ["******************************************"],
    Gravity: ["******************************************"],
    Harmony: ["******************************************"],
    Fantom: ["******************************************"],
    Flare: ["******************************************"],
    Sei: ["0x160345fc359604fc6e70e3c5facbde5f7a9342d8"],
    "Plume Mainnet": ["0xca59cA09E5602fAe8B629DeE83FfA819741f14be"],
  },
  "USD₮0": {
    Arbitrum: ["0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9"],
    "Core Blockchain Mainnet": ["0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9"],
  },
  GLMR: {
    Moonbeam: ["\\\\n"],
  },
  ZRO: {
    Arbitrum: ["******************************************"],
    Polygon: ["******************************************"],
    Base: ["******************************************"],
    Optimism: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Avalanche: ["******************************************"],
  },
  CELO: {
    "Celo Mainnet": ["\\\\n"],
  },
  GSWIFT: {
    Arbitrum: ["******************************************"],
    Ethereum: ["******************************************"],
    Base: ["******************************************"],
  },
  JOE: {
    Avalanche: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Mantle: ["******************************************"],
  },
  PEPE: {
    Ethereum: ["******************************************"],
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  FRAX: {
    Optimism: ["******************************************"],
    Avalanche: ["******************************************"],
    Arbitrum: ["******************************************"],
  },
  MAV: {
    "zkSync Era Mainnet": ["******************************************"],
    Base: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  MOVR: {
    Moonriver: ["\\\\n"],
  },
  USDbC: {
    Base: ["******************************************"],
  },
  Cake: {
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
    "Polygon zkEVM": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  RDNT: {
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Base: ["******************************************"],
  },
  OSAK: {
    Polygon: ["******************************************"],
    Optimism: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Avalanche: ["******************************************"],
    Arbitrum: ["******************************************"],
    Base: ["******************************************"],
  },
  APT: {
    Aptos: ["\\\\n"],
  },
  BUSD: {
    "BNB Chain": [
      "******************************************",
      "******************************************",
    ],
    Harmony: ["******************************************"],
  },
  STONE: {
    Linea: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  MTR: {
    "Meter Mainnet": ["\\\\n"],
  },
  JEWEL: {
    DFK: ["\\\\n"],
  },
  USDe: {
    Arbitrum: ["******************************************"],
    Manta: ["******************************************"],
    Blast: ["******************************************"],
    Mantle: ["******************************************"],
    Optimism: ["******************************************"],
    Ethereum: ["******************************************"],
    Linea: ["******************************************"],
    Morph: ["******************************************"],
    Base: ["******************************************"],
    Fraxtal: ["******************************************"],
    Mode: ["******************************************"],
  },
  ONE: {
    Harmony: ["\\\\n"],
  },
  WOO: {
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Optimism: ["******************************************"],
    Avalanche: ["******************************************"],
    Fantom: ["******************************************"],
    Polygon: ["******************************************"],
  },
  CORE: {
    "Core Blockchain Mainnet": ["\\\\n"],
  },
  ATOM: {
    Harmony: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  EXTRA: {
    Base: ["******************************************"],
  },
  METIS: {
    Metis: [
      "\\\\n",
      "******************************************",
      "******************************************",
    ],
    Avalanche: ["******************************************"],
    "BNB Chain": [
      "******************************************",
      "******************************************",
    ],
    Ethereum: ["******************************************"],
  },
  OMNI: {
    Polygon: ["******************************************"],
    Arbitrum: ["******************************************"],
    Ethereum: ["******************************************"],
    Base: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  ORDER: {
    Arbitrum: ["******************************************"],
    Ethereum: ["******************************************"],
    Base: ["******************************************"],
  },
  APE: {
    Ethereum: ["******************************************"],
    Ape: ["******************************************"],
    Arbitrum: ["******************************************"],
  },
  WONE: {
    "BNB Chain": ["******************************************"],
  },
  INJ: {
    Injective: ["\\\\n"],
  },
  TAROT: {
    Optimism: ["******************************************"],
    Base: ["******************************************"],
    "BNB Chain": ["******************************************"],
    "zkSync Era Mainnet": ["******************************************"],
    Arbitrum: ["******************************************"],
    Avalanche: ["******************************************"],
    Fantom: ["******************************************"],
    Polygon: ["******************************************"],
    Kava: ["******************************************"],
  },
  weETH: {
    Blast: ["******************************************"],
    Ethereum: [
      "******************************************",
      "******************************************",
    ],
    Linea: ["******************************************"],
    Mode: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Optimism: ["******************************************"],
    Base: ["******************************************"],
    Scroll: ["******************************************"],
  },
  CAKE: {
    "zkSync Era Mainnet": ["******************************************"],
    Aptos: [
      "0x159df6b7689437016108a019fd5bef736bac692b6d4a1f10c941f6fbb9a74ca6",
    ],
    Harmony: ["******************************************"],
    "BNB Chain": ["0x9876993e7e1750cc375a569731ca0583d0849f65"],
    Base: ["0x3055913c90fcc1a6ce9a358911721eeb942013a1"],
    Linea: ["******************************************"],
  },
  ITP: {
    Linea: ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
    Optimism: ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
    "zkSync Era Mainnet": ["0xd03465338226ea0178337f4abb16fdd6df529f57"],
    "BNB Chain": ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
    Arbitrum: ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
    Base: ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
    Fantom: ["0x2b1d36f5b61addaf7da7ebbd11b35fd8cfb0de31"],
  },
  vMANTA: {
    Moonbeam: ["0xdebbb9309d95dabbfb82411a9c6daa3909b164a4"],
  },
  GRAI: {
    Arbitrum: ["0x894134a25a5fac1c2c26f1d8fbf05111a3cb9487"],
    "zkSync Era Mainnet": ["0x5fc44e95eaa48f9eb84be17bd3ac66b6a82af709"],
    Linea: ["0x894134a25a5fac1c2c26f1d8fbf05111a3cb9487"],
  },
  FUSE: {
    "Fuse Mainnet": ["\\\\n"],
  },
  CYBER: {
    Optimism: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  WAGMI: {
    Fantom: ["******************************************"],
    "BNB Chain": ["******************************************"],
    "zkSync Era Mainnet": ["******************************************"],
    Polygon: ["******************************************"],
    Kava: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  MAI: {
    Avalanche: ["******************************************"],
    Polygon: ["******************************************"],
    Optimism: ["******************************************"],
    Ethereum: ["******************************************"],
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  WBNB: {
    Harmony: ["******************************************"],
    "BNB Chain": [
      "******************************************",
      "******************************************",
    ],
    Arbitrum: ["******************************************"],
  },
  RWA: {
    "BNB Chain": ["******************************************"],
    Base: ["******************************************"],
  },
  KAVA: {
    Kava: ["\\\\n"],
  },
  OP: {
    Avalanche: ["******************************************"],
    Optimism: ["******************************************"],
    Arbitrum: ["******************************************"],
  },
  rsETH: {
    Base: ["******************************************"],
    Ethereum: ["******************************************"],
    Scroll: ["******************************************"],
    Linea: ["******************************************"],
    Blast: ["******************************************"],
    Arbitrum: ["******************************************"],
    Mode: ["******************************************"],
    Zircuit: ["******************************************"],
    Optimism: ["******************************************"],
    Manta: ["******************************************"],
  },
  LilPudgys: {
    Polygon: ["\\\\n"],
    Ethereum: ["\\\\n"],
    Arbitrum: ["\\\\n"],
    "BNB Chain": ["\\\\n"],
  },
  WCANTO: {
    Canto: ["******************************************"],
  },
  USH: {
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  OLE: {
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  XVS: {
    "BNB Chain": [
      "******************************************",
      "******************************************",
    ],
    Arbitrum: ["******************************************"],
    "zkSync Era Mainnet": ["******************************************"],
  },
  WBTC: {
    "BNB Chain": ["******************************************"],
    Ethereum: [
      "******************************************",
      "******************************************",
      "******************************************",
    ],
    "Core Blockchain Mainnet": ["******************************************"],
    Sei: ["******************************************"],
  },
  BEETS: {
    Fantom: ["******************************************"],
  },
  OX: {
    Avalanche: ["******************************************"],
    Base: ["******************************************"],
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  MODE: {
    Mode: ["******************************************"],
    Optimism: ["******************************************"],
  },
  QORPO: {
    Ethereum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  IDIA: {
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Mantle: ["******************************************"],
    Base: ["******************************************"],
  },
  PREON: {
    Polygon: ["******************************************"],
    Base: ["******************************************"],
  },
  FXS: {
    Sei: ["******************************************"],
  },
  TLOS: {
    TelosEVM: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  ARKEN: {
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  CFX: {
    "Conflux eSpace": ["\\\\n"],
  },
  BETS: {
    Arbitrum: ["******************************************"],
    Polygon: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  SepoliaETH: {
    Ethereum: ["******************************************"],
  },
  rswETH: {
    Ethereum: ["******************************************"],
  },
  SOL: {
    Solana: ["\\\\n"],
  },
  DOGE: {
    "BNB Chain": ["******************************************"],
    Harmony: ["******************************************"],
  },
  SHRAP: {
    "BNB Chain": ["******************************************"],
    Avalanche: ["******************************************"],
  },
  VANA: {
    Islander: ["\\\\n", "******************************************"],
    Base: ["******************************************"],
  },
  BEAM: {
    "Merit Circle": ["\\\\n"],
  },
  DMT: {
    Ethereum: ["******************************************"],
    Arbitrum: [
      "******************************************",
      "******************************************",
    ],
  },
  AVA: {
    Base: ["******************************************"],
  },
  FUEGO: {
    Base: ["******************************************"],
  },
  AURA: {
    Optimism: ["******************************************"],
    Arbitrum: ["******************************************"],
    Polygon: ["******************************************"],
  },
  unshETH: {
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
  },
  IOTA: {
    Iota: ["\\\\n"],
  },
  "Omni Elements": {
    Ethereum: ["\\\\n"],
    Polygon: ["\\\\n"],
    Arbitrum: ["\\\\n"],
    Moonbeam: ["\\\\n"],
  },
  MNT: {
    Mantle: ["\\\\n"],
  },
  REUNI: {
    Arbitrum: ["******************************************"],
    Optimism: ["******************************************"],
    Polygon: ["******************************************"],
  },
  Gregs: {
    Arbitrum: ["\\\\n"],
    Avalanche: ["\\\\n"],
    "BNB Chain": ["\\\\n"],
  },
  TENET: {
    Tenet: ["\\\\n"],
    "BNB Chain": ["******************************************"],
    Arbitrum: ["******************************************"],
    Avalanche: ["******************************************"],
    Ethereum: ["******************************************"],
  },
  "Manta mETH": {
    Manta: ["******************************************"],
  },
  ZEREBRO: {
    Base: ["******************************************"],
  },
  ADA: {
    "BNB Chain": ["******************************************"],
    Harmony: ["******************************************"],
  },
  RLP: {
    Ethereum: ["******************************************"],
  },
  LIF3: {
    Fantom: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Ethereum: ["******************************************"],
    Polygon: ["******************************************"],
  },
  SFUND: {
    Arbitrum: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  "Kanpai Pandas": {
    Optimism: ["\\\\n"],
    Ethereum: ["\\\\n"],
    Polygon: ["\\\\n"],
    "BNB Chain": ["\\\\n"],
    Avalanche: ["\\\\n"],
    Fantom: ["\\\\n"],
  },
  USDD: {
    Aptos: ["******************************************"],
    "BNB Chain": ["******************************************"],
  },
  USDY: {
    Arbitrum: ["******************************************"],
  },
  WIF: {
    "BNB Chain": ["******************************************"],
  },
  WNCG: {
    "BNB Chain": ["******************************************"],
  },
  "Shredding Sassy": {
    Polygon: ["\\\\n"],
    Base: ["\\\\n"],
  },
  sUSDe: {
    Ethereum: ["******************************************"],
    Arbitrum: ["******************************************"],
    Blast: ["******************************************"],
  },
  WMATIC: {
    Harmony: ["******************************************"],
  },
  ASTR: {
    Astar: ["\\\\n"],
  },
  DOT: {
    "BNB Chain": ["******************************************"],
  },
  LILY: {
    Ethereum: ["******************************************"],
  },
  wstETH: {
    Ethereum: ["******************************************"],
  },
  SWORLD: {
    Arbitrum: ["******************************************"],
    Avalanche: ["******************************************"],
    "BNB Chain": ["******************************************"],
    Ethereum: ["******************************************"],
  },
  ORA: {
    Ethereum: ["0x5e1f75388dc768f6129b9ee859bfb1361631a77e"],
  },
  QI: {
    Fantom: ["0xd3fdcb837dafdb7c9c3ebd48fe22a53f6dd3d7d7"],
    Base: ["0xd3fdcb837dafdb7c9c3ebd48fe22a53f6dd3d7d7"],
  },
  USDA: {
    Optimism: ["0xc69e66109943faf5cbda22f360b7eb7c27bb5c88"],
  },
  RAIN: {
    Ethereum: ["0x1f1aa4d239002bb818536c95e9b762a1fc8484c1"],
  },
  GOGLZ: {
    Fantom: ["0x95ae0a728375e293eface67ea94e4b50c3a7a0fd"],
  },
  sfrxETH: {
    Metis: ["0x1f55a02a049033e3419a8e2975cf3f572f4e6e9a"],
  },
  LBR: {
    Arbitrum: ["0xa23e44aea714fbbc08ef28340d78067b9a8cad73"],
  },
  DegenReborn: {
    "BNB Chain": ["0x5e6adf21e76f9d4258fc68fd5c8fcb826185f8ef"],
  },
  WFTM: {
    Harmony: ["0x1b0afbf1a7ef7db80f79e540dce52fc259b4b65f"],
  },
  MTVT: {
    Metis: ["0x52b4fc2ca0d9b194769ab9a3dc36c2c6831f2520"],
    "BNB Chain": ["0x76326791a0d491cdb24a349354ec024756816d12"],
  },
  SNSY: {
    Ethereum: ["0x82a605d6d9114f4ad6d5ee461027477eeed31e34"],
    Arbitrum: ["0x3124678d62d2aa1f615b54525310fbfda6dcf7ae"],
  },
  ENA: {
    Base: ["0x58538e6a46e07434d7e7375bc268d3cb839c0133"],
    Scroll: ["0x58538e6a46e07434d7e7375bc268d3cb839c0133"],
    Fraxtal: ["0x58538e6a46e07434d7e7375bc268d3cb839c0133"],
    Mantle: ["0x58538e6a46e07434d7e7375bc268d3cb839c0133"],
  },
  UNIX: {
    Avalanche: ["0x6f97d3f120fbbdaacf1c9da61a8ad126b7426861"],
  },
  sFRAX: {
    Blast: ["0xe4796ccb6bb5de2290c417ac337f2b66ca2e770e"],
  },
  MAVIA: {
    Base: ["0x24fcfc492c1393274b6bcd568ac9e225bec93584"],
  },
  BTCB: {
    "Core Blockchain Mainnet": ["0x7130d2a12b9bcbfae4f2634d864a1ee1ce3ead9c"],
    "BNB Chain": ["0x7130d2a12b9bcbfae4f2634d864a1ee1ce3ead9c"],
  },
  CNDY: {
    Arbitrum: ["0x6b43732a9ae9f8654d496c0a075aa4aa43057a0b"],
  },
  TROVE: {
    Arbitrum: ["0x982239d38af50b0168da33346d85fb12929c4c07"],
  },
  "Gh0stly Gh0sts": {
    "BNB Chain": ["\\\\n"],
    Arbitrum: ["\\\\n"],
  },
  ZEN: {
    "Horizen EON Mainnet": ["\\\\n"],
  },
  ABOND: {
    "BNB Chain": ["0xe6828d65bf5023ae1851d90d8783cc821ba7eee1"],
    Polygon: ["0xe6828d65bf5023ae1851d90d8783cc821ba7eee1"],
  },
  HERA: {
    Ethereum: ["0xa2c2c937333165d4c5f2dc5f31a43e1239fecfeb"],
  },
  mETH: {
    Mantle: ["0xcda86a272531e8640cd7f1a92c01839911b90bb0"],
    Ethereum: ["0xd5f7838f5c461feff7fe49ea5ebaf7728bb0adfa"],
  },
  SEILOR: {
    Base: ["0x9bde70bad05b7d84dac03024dae15aace8c9cca2"],
  },
  GOLD: {
    Arbitrum: ["0x8b5e4c9a188b1a187f2d1e80b1c2fb17fa2922e1"],
  },
  G: {
    Gravity: ["\\\\n"],
  },
  "OX.old": {
    Polygon: ["******************************************"],
  },
  LUSD: {
    Arbitrum: ["******************************************"],
    Optimism: ["******************************************"],
  },
  POLTER: {
    Fantom: ["******************************************"],
  },
  OKT: {
    "OKXChain Mainnet": ["\\\\n"],
  },
  ZERO: {
    Linea: ["******************************************"],
    Base: ["******************************************"],
  },
  MCG: {
    Ethereum: ["******************************************"],
  },
  EDU: {
    "BNB Chain": ["******************************************"],
  },
  CAT: {
    "BNB Chain": ["******************************************"],
  },
  USR: {
    Ethereum: ["******************************************"],
  },
  ISLAND: {
    Ethereum: ["******************************************"],
  },
  SQUID: {
    Base: ["******************************************"],
  },
  USDz: {
    Base: ["******************************************"],
  },
  msETH: {
    Base: ["******************************************"],
  },
  AGLD: {
    Loot: ["\\\\n"],
  },
  peUSD: {
    Ethereum: ["******************************************"],
  },
  OOE: {
    Arbitrum: ["******************************************"],
  },
  TECH: {
    Mantle: ["******************************************"],
  },
  "Tiny Dinos": {
    Polygon: ["\\\\n"],
  },
  frxETH: {
    Fraxtal: ["\\\\n"],
  },
  PayUSD: {
    Ethereum: ["******************************************"],
    "Plume Mainnet": ["******************************************"],
  },
};

// let fs = require("fs");
// let { parse } = require("csv-parse");

// let csvData: any[] = [];
// let i: number = 0;
// let headers: { [key: string]: number } = {};
// let tokens: { [symbol: string]: { [chain: string]: string[] } } = {};

// fs.createReadStream(
//   "",
// )
//   .pipe(parse({ delimiter: "," }))
//   .on("data", function (csvrow: any) {
//     if (i == 0) {
//       csvrow.map((key: string, i: number) => {
//         headers[key] = i;
//       });
//       i++;
//       return;
//     }

//     const { SYMBOL, TOKEN, CHAINSOURCE } = headers;
//     const token = csvrow[TOKEN].toLowerCase();
//     const symbol = csvrow[SYMBOL];
//     const chain = csvrow[CHAINSOURCE];

//     if (!(symbol in tokens)) tokens[symbol] = {};
//     if (!(chain in tokens[symbol])) tokens[symbol][chain] = [];
//     if (!tokens[symbol][chain].includes(token))
//       tokens[symbol][chain].push(token);
//   })
//   .on("end", function () {
//     tokens;
//     //do something with csvData
//     console.log("done");
//   });
