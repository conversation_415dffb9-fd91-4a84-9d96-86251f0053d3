import {call} from "@defillama/sdk/build/abi/abi2"
import { getAllInfo } from "../utils"

export default async function bridge() {
  const tokens = [
    "******************************************", // matic
    "******************************************", // usdt
    "******************************************", // usdc
    "******************************************", // dai
    "******************************************", // wbtc
  ]

  return await Promise.all(tokens
    .map(async (token) => {
      const [name, symbol, decimals] = await Promise.all(["string:name", "string:symbol", "uint8:decimals"].map(abi => call({ abi, target: token })))
      const wrapperAddress = await call({
        target: "******************************************",
        abi: "function precalculatedWrapperAddress(uint32 originNetwork,address originTokenAddress,string calldata name,string calldata symbol,uint8 decimals) external view returns (address)",
        params: [0, token, name, symbol, decimals],
        chain: "polygon_zkevm"
      })
      const to = `ethereum:${token}`
      return {
        from: `polygon_zkevm:${wrapperAddress}`,
        to,
        getAllInfo: getAllInfo(wrapperAddress, 'polygon_zkevm', to)
      };
    }))
}
