import { fetch, formatExtraTokens } from "../utils"

export default async function bridge() {
    const binanceBridge = (
        await fetch(
            "https://api.binance.org/bridge/api/v2/tokens?walletNetwork="
        )
    ).data.tokens as any[];

    return binanceBridge.filter(token => token.ethContractAddress !== "").map(token=>({
        from: `bsc:${token.bscContractAddress}`,
        to: `ethereum:${token.ethContractAddress}`,
        decimals: token.bscContractDecimal,
        symbol: token.bscSymbol,
    })).concat(extraTokens)
}

const extraTokens = formatExtraTokens("bsc", [
    ["******************************************", "avax:******************************************", "ELK", 18],
    ["******************************************", "ethereum:******************************************", "ETH", 18],
    ["******************************************", "bsc:0x123", "aBnBc", 18],   // HOTFIX for hacked aBnB token
    ["******************************************", "bsc:0x123", "aBnBb", 18],   // HOTFIX for hacked aBnB token
])
