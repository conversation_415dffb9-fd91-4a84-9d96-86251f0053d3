import { fetch, formatExtraTokens } from "../utils";

export default async function bridge() {
  const bridge = (
    await fetch("https://bridge.arbitrum.io/token-list-42161.json")
  ).tokens as any[];

  return bridge
    .map((token) => {
      if (token.extensions == null)
        return {
          from: `arbitrum:${token.address}`,
          to: `null`,
          symbol: token.symbol,
          decimals: token.decimals
        };
      const bridged = token.extensions.bridgeInfo[1].tokenAddress;
      return {
        from: `arbitrum:${token.address}`,
        to: `ethereum:${bridged}`,
        symbol: token.symbol,
        decimals: token.decimals
      };
    })
    .filter((t) => t.to != "null")
    .concat(extraTokens);
}

const extraTokens = formatExtraTokens("arbitrum", [
  [
    "******************************************",
    "ethereum:******************************************",
    "MIM",
    18
  ],
  [
    "******************************************",
    "ethereum:******************************************",
    "renBTC",
    8
  ],
  [
    "******************************************",
    "polygon:******************************************",
    "XDO",
    18
  ]
]);
