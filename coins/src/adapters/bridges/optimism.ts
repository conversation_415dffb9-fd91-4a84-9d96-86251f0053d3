import { Token } from "./index";
import { fetch, formatExtraTokens } from "../utils";
// import tokenMappings from "../tokenMapping_added.json";
import tokenMappings2 from "../tokenMapping.json";

export default async function bridge(): Promise<Token[]> {
  const bridge = (
    await fetch("https://static.optimism.io/optimism.tokenlist.json")
  ).tokens as any[];

  const ethUrlMap = bridge
    .filter((token) => token.chainId === 1)
    .reduce((all, token) => {
      all[token.logoURI] = token;
      return all;
    }, {});

  const tokens: Token[] = [];
  bridge.map((optToken) => {
    const ethToken = ethUrlMap[optToken.logoURI];
    if (ethToken === undefined) return;
    let chain;
    switch (optToken.chainId) {
      case 10:
        chain = "optimism";
        break;
      case 8453:
        chain = "base";
        break;
      default:
        return;
    }
    tokens.push({
      from: `${chain}:${optToken.address}`,
      to: `ethereum:${ethToken.address}`,
      symbol: optToken.symbol,
      decimals: optToken.decimals,
    });
  });
  const response = [tokens, extraTokens];

  // Object.entries(tokenMappings).forEach(([chain, tokenMap]) => {
  //   const tokens: [string, string, string, number][] = [];
  //   Object.entries(tokenMap).map(
  //     ([from, { to, symbol, decimals: decimalsNum }]) => {
  //       const decimals = +decimalsNum;
  //       if (isNaN(decimals))
  //         throw new Error("Is not valid token mapping: " + from);
  //       // const from_lowerCase = from.toLowerCase()
  //       // if (from_lowerCase !== from)
  //       //   tokens.push([from_lowerCase, to, symbol, decimals]);
  //       let token = from;
  //       if (!chainsThatShouldNotBeLowerCased.includes(chain)) token = token.toLowerCase();
  //       tokens.push([token, to, symbol, decimals]);
  //     },
  //   );
  //   response.push(formatExtraTokens(chain, tokens));
  // });

  Object.entries(tokenMappings2).forEach(([chain, tokenMap]) => {
    const tokens: [string, string, string, number][] = [];
    Object.entries(tokenMap).map(
      ([from, { to, symbol, decimals: decimalsNum }]) => {
        const decimals = +decimalsNum;
        if (isNaN(decimals))
          throw new Error("Is not valid token mapping: " + from);
        tokens.push([from, to, symbol, decimals]);
      },
    );
    response.push(formatExtraTokens(chain, tokens));
  });

  return response.flat();
}

const extraTokens = formatExtraTokens("optimism", [
  // SNX synths
  [
    "******************************************",
    "ethereum:******************************************",
    "sUSD",
    18,
  ],
  [
    "******************************************",
    "ethereum:******************************************",
    "sLINK",
    18,
  ],
  [
    "******************************************",
    "ethereum:******************************************",
    "sETH",
    18,
  ],
  [
    "******************************************",
    "ethereum:******************************************",
    "sBTC",
    18,
  ],
  // BitAnt
  [
    "******************************************",
    "ethereum:******************************************",
    "BitANT",
    18,
  ],
  // WETH
  [
    "******************************************",
    "ethereum:******************************************",
    "WETH",
    18,
  ],
]);
