import { Token } from "./index";
import { fetch, } from "../utils";

export default async function bridge(): Promise<Token[]> {
  const allTokens = []
  let page = 1
  do {
    const { items, meta } = await fetch(`https://explorer-api.zklink.io/tokens?limit=200&page=${page}&key=`)
    allTokens.push(...items)
    page++
    if (page >= meta.totalPages) break;
  } while (page < 100)

  const tokens: Token[] = [{
    from: 'zklink:******************************************',
    to: 'ethereum:******************************************',
    symbol: 'WETH',
    decimals: 18
  }];
  allTokens
    .filter((token) => token.l1Address && token.l2Address && token.networkKey)
    .map((token) => {
      let sourceChain
      switch (token.networkKey) {
        case 'ethereum': sourceChain = 'ethereum'; break;
        case 'zksync': sourceChain = 'era'; break;
        case 'manta': sourceChain = 'manta'; break;
        case 'blast': sourceChain = 'blast'; break;
        case 'arbitrum': sourceChain = 'arbitrum'; break;
        case 'mantle': sourceChain = 'mantle'; break;
        case 'base': sourceChain = 'base'; break;
        case 'optimism': sourceChain = 'optimism'; break;
        case 'primary': sourceChain = 'linea'; break;
        case 'scroll': sourceChain = 'scroll'; break;
        default: console.log('zklink Unknown networkKey', token.networkKey)
      }
      if (!sourceChain) return;
      tokens.push({
        from: `zklink:${token.l2Address}`,
        to: `${sourceChain}:${token.l1Address}`,
        symbol: token.symbol,
        decimals: token.decimals
      });
    });

  return tokens
}
