import { Token } from "./index";

const tokenAddresses: { [symbol: string]: { [chain: string]: string } } = {
  nUSD: {
    reference: "ethereum:******************************************",
    ethereum: "******************************************",
    optimism: "******************************************",
    cronos: "******************************************",
    bsc: "******************************************",
    polygon: "******************************************",
    fantom: "******************************************",
    boba: "******************************************",
    metis: "******************************************",
    canto: "******************************************",
    arbitrum: "******************************************",
    avax: "******************************************",
    dfk: "******************************************",
    aurora: "******************************************",
    harmony: "******************************************",
    blast: "******************************************",
  },
  nETH: {
    reference: "ethereum:******************************************",
    optimism: "******************************************",
    fantom: "******************************************",
    boba: "******************************************",
    metis: "******************************************",
    moonbeam: "******************************************",
    dogechain: "******************************************",
    canto: "******************************************",
    klaytn: "******************************************",
    arbitrum: "******************************************",
    avax: "******************************************",
    harmony: "******************************************",
    blast: "******************************************",
  },
  nUSD2: {
    reference: "ethereum:******************************************",
    optimism: "******************************************",
  },
};

export default async function bridge(): Promise<Token[]> {
  const tokens: Token[] = [];

  Object.keys(tokenAddresses).map((symbol: string) =>
    Object.keys(tokenAddresses[symbol]).map((c: string) => {
      if (c == "reference") return;
      tokens.push({
        from: `${c}:${tokenAddresses[symbol][c]}`,
        to: tokenAddresses[symbol].reference,
        symbol,
        decimals: 18,
      });
    }),
  );

  return tokens;
}
