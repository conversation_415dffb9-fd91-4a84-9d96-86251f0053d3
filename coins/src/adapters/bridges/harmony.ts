import {fetch, formatExtraTokens} from "../utils"

const networkToIdentifier = {
    "ETHEREUM": "ethereum",
    "BINANCE": "bsc",
} as { [network: string]: string | undefined }

export default async function bridge() {
    const bridge = (
        await fetch("https://be4.bridge.hmny.io/tokens/?page=0&size=1000")
    ).content as any[];

    return bridge.map(token => {
        const chain = networkToIdentifier[token.network];
        if (chain === undefined) {
            return null
        }
        return {
            from: `harmony:${token.hrc20Address}`,
            to: `${chain}:${token.erc20Address}`,
            decimals: Number(token.decimals),
            symbol: token.symbol,
        }
    }).filter(t => t !== null).concat(extraTokens)
}

const extraTokens = formatExtraTokens("harmony", [
    ["******************************************", "coingecko#terrausd", "UST", 18],
    ["******************************************", "coingecko#elk-finance", "ELK", 18],
])