{"xdai": {"******************************************": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}}, "nova": {"******************************************": {"name": "Supernova", "decimals": "18", "symbol": "SNT", "to": "fantom:******************************************"}, "******************************************": {"name": "Wrapped Supernova", "decimals": "18", "symbol": "WSNT", "to": "fantom:******************************************"}, "******************************************": {"name": "Nova USD", "decimals": "6", "symbol": "NUSD", "to": "fantom:******************************************"}}, "metis": {"******************************************": {"name": "METIS", "decimals": "18", "symbol": "METIS", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "m.DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Wrapped METIS", "decimals": "18", "symbol": "WMETIS", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped METIS", "decimals": "18", "symbol": "WMETIS", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Open Dollar", "decimals": "18", "symbol": "USDO", "to": "bsc:******************************************"}, "******************************************": {"name": "Poly-Peg BNB", "decimals": "18", "symbol": "BNB", "to": "bsc:******************************************"}, "******************************************": {"name": "Poly-Peg BUSD", "decimals": "18", "symbol": "BUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "rAVAX", "to": "avax:******************************************"}, "******************************************": {"name": "Fantom <PERSON>", "decimals": "18", "symbol": "rFTM", "to": "fantom:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FTM", "to": "bsc:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "MATIC", "to": "bsc:******************************************"}, "******************************************": {"name": "Synapse", "decimals": "18", "symbol": "SYN", "to": "ethereum:******************************************"}, "******************************************": {"name": "BoringDAO", "decimals": "18", "symbol": "BORING", "to": "ethereum:******************************************"}, "******************************************": {"name": "WBTC Token", "decimals": "8", "symbol": "m.WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "AAVE Token", "decimals": "18", "symbol": "m.AAVE", "to": "ethereum:******************************************"}}, "velas": {"******************************************": {"name": "Wrapped VLX", "decimals": "18", "symbol": "WVLX", "to": "velas:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FTM", "to": "fantom:******************************************"}, "******************************************": {"name": "BambooDeFi", "decimals": "18", "symbol": "BAMBOO", "to": "ethereum:******************************************"}, "******************************************": {"name": "Swapz", "decimals": "18", "symbol": "SWAPZ", "to": "bsc:******************************************"}, "******************************************": {"name": "VELHALLA.io", "decimals": "18", "symbol": "SCAR", "to": "bsc:******************************************"}, "******************************************": {"name": "<PERSON>", "decimals": "6", "symbol": "ADA", "to": "coingecko#cardano"}, "******************************************": {"decimals": "18", "symbol": "STVLX", "to": "coingecko#staked-vlx"}, "******************************************": {"decimals": "18", "symbol": "VLX", "to": "coingecko#velas"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "fuse": {"******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FUSE", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped Fuse", "decimals": "18", "symbol": "WFUSE", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin on Fuse", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Dai Stablecoin on Fuse", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD on Fuse", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped Ether on Fuse", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Kyber Network Crystal on Fuse", "decimals": "18", "symbol": "KNC", "to": "ethereum:******************************************"}}, "optimism": {"******************************************": {"decimals": "18", "symbol": "PNP", "to": "coingecko#penpie"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FEI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Frax Share", "decimals": "18", "symbol": "FXS", "to": "ethereum:******************************************"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "Governance OHM", "decimals": "18", "symbol": "gOHM", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synth sLINK", "decimals": "18", "symbol": "sLINK", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synth sBTC", "decimals": "18", "symbol": "sBTC", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "TLX", "to": "coingecko#tlx"}, "******************************************": {"decimals": "18", "symbol": "COMP", "to": "coingecko#compound-governance-token"}}, "bsc": {"******************************************": {"decimals": "18", "symbol": "BTT", "to": "coingecko#bittorrent"}, "******************************************": {"decimals": "6", "symbol": "TRX", "to": "coingecko#tron"}, "******************************************": {"decimals": 18, "symbol": "CROWD", "to": "coingecko#crowdswap"}, "******************************************": {"name": "Paxos Standard", "decimals": "18", "symbol": "PAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "2030Floki", "decimals": "4", "symbol": "2030Floki", "to": "ethereum:0x123"}, "******************************************": {"name": "Zircon Gamma Token", "decimals": "18", "symbol": "ZRG", "to": "moonriver:******************************************"}, "******************************************": {"name": "DeFIL-V2", "decimals": "18", "symbol": "DFL", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Ethereum", "decimals": "6", "symbol": "sUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "MahaDAO", "decimals": "18", "symbol": "MAHA", "to": "ethereum:******************************************"}, "******************************************": {"name": "BUSD-USDC Staked APE-LP", "decimals": "18", "symbol": "BUSDUSDC-APE-LP-S", "to": "bsc:0xc087c78abac4a0e900a327444193dbf9ba69058e"}, "0xc5fb6476a6518dd35687e0ad2670cb8ab5a0d4c5": {"name": "BUSD-USDT Staked APE-LP", "decimals": "18", "symbol": "BUSDUSDT-APE-LP-S", "to": "bsc:0x2e707261d086687470b515b320478eb1c88d49bb"}, "0x532197ec38756b9956190b845d99b4b0a88e4ca9": {"name": "PAID Network", "decimals": "18", "symbol": "PAID", "to": "ethereum:0x1614f18fc94f47967a3fbe5ffcd46d4e7da3d787"}, "0x6d1b7b59e3fab85b7d3a3d86e505dd8e349ea7f3": {"name": "BXHToken", "decimals": "18", "symbol": "BXH", "to": "heco:******************************************"}, "0x42586ef4495bb512a86cf7496f6ef85ae7d69a64": {"name": "Spice", "decimals": "18", "symbol": "SPICE", "to": "polygon:0x66e8617d1df7ab523a316a6c01d16aa5bed93681"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "iZi", "to": "ethereum:******************************************"}, "0x0a3bb08b3a15a19b4de82f8acfc862606fb69a2d": {"name": "iZUMi Bond USD", "decimals": "18", "symbol": "iUSD", "to": "ethereum:0x0a3bb08b3a15a19b4de82f8acfc862606fb69a2d"}, "0xa8bb71facdd46445644c277f9499dd22f6f0a30c": {"name": "beltBNB", "decimals": "18", "symbol": "beltBNB", "to": "bsc:******************************************"}, "******************************************": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "bsc:******************************************"}, "******************************************": {"name": "binancecoin", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Belt.fi 4Belt", "decimals": "18", "symbol": "4Belt", "to": "bsc:******************************************"}, "******************************************": {"name": "beltBTC", "decimals": "18", "symbol": "beltBTC", "to": "bsc:******************************************"}, "******************************************": {"name": "beltETH", "decimals": "18", "symbol": "beltETH", "to": "bsc:******************************************"}, "******************************************": {"name": "Yield Guild Games Token", "decimals": "18", "symbol": "YGG", "to": "ethereum:******************************************"}, "******************************************": {"name": "Interest Bearing BNB", "decimals": "18", "symbol": "ibBNB", "to": "bsc:******************************************"}, "******************************************": {"name": "Interest Bearing BUSD", "decimals": "18", "symbol": "ibBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Interest Bearing USDT", "decimals": "18", "symbol": "ibUSDT", "to": "bsc:******************************************"}, "******************************************": {"name": "Interest Bearing BTCB", "decimals": "18", "symbol": "ibBTCB", "to": "bsc:******************************************"}, "******************************************": {"name": "Interest Bearing ETH", "decimals": "18", "symbol": "ibETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Interest Bearing TUSD", "decimals": "18", "symbol": "ibTUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Interest Bearing ALPACA", "decimals": "18", "symbol": "ibALPACA", "to": "bsc:******************************************"}, "******************************************": {"name": "DotDot Tokenized EPX Lock", "decimals": "18", "symbol": "dEPX", "to": "bsc:******************************************"}, "******************************************": {"name": "Rail BSC", "decimals": "18", "symbol": "RAILBSC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BNB", "decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Wrapped BNB", "decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Anchor <PERSON>", "decimals": "6", "symbol": "aUST", "to": "coingecko#anchorust"}, "******************************************": {"name": "Investintoken", "decimals": "18", "symbol": "IVN", "to": "coingecko#investin"}, "******************************************": {"name": "1MILNFT", "decimals": "18", "symbol": "1MIL", "to": "coingecko#1million-nfts"}, "******************************************": {"name": "APYSwap", "decimals": "18", "symbol": "APYS", "to": "coingecko#apyswap"}, "******************************************": {"name": "BRZ Token", "decimals": "4", "symbol": "BRZ", "to": "coingecko#brz"}, "******************************************": {"name": "<PERSON> Synthetic Brazilian Real", "decimals": "18", "symbol": "jBRL", "to": "coingecko#brz"}, "******************************************": {"name": "BRZ", "decimals": "4", "symbol": "BRZ", "to": "coingecko#brz"}, "******************************************": {"name": "Alpaca USD", "decimals": "18", "symbol": "AUSD", "to": "coingecko#tether"}, "******************************************": {"name": "deBridge USD Coin", "decimals": "6", "symbol": "deUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Axelar Wrapped USDC", "decimals": "6", "symbol": "axlUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "FLOKI", "decimals": "9", "symbol": "FLOKI", "to": "coingecko#floki"}, "******************************************": {"decimals": "18", "symbol": "ZEEP", "to": "coingecko#zeepr"}}, "boba": {"******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Binance-Peg BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Ethereum", "decimals": "6", "symbol": "sUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic Binance-Peg BUSD Token from BSC", "decimals": "18", "symbol": "sBUSD", "to": "bsc:******************************************"}}, "boba_bnb": {"******************************************": {"name": "USD Coin", "decimals": "18", "symbol": "USDC", "to": "bsc:******************************************"}, "******************************************": {"name": "Boba Network", "decimals": "18", "symbol": "BOBA", "to": "ethereum:******************************************"}, "******************************************": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "bsc:******************************************"}}, "boba_avax": {"******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC.e", "to": "avax:******************************************"}, "******************************************": {"name": "Wrapped BOBA Token", "decimals": "18", "symbol": "WBOBA", "to": "ethereum:******************************************"}, "******************************************": {"name": "Avalanche", "decimals": "18", "symbol": "AVAX", "to": "avax:******************************************"}}, "okexchain": {"******************************************": {"name": "Elk", "decimals": "18", "symbol": "ELK", "to": "avax:******************************************"}, "******************************************": {"name": "oec-token", "decimals": "18", "symbol": "OKC", "to": "coingecko#oec-token"}}, "milkomeda": {"******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Ethereum", "decimals": "6", "symbol": "sUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped ADA", "decimals": "18", "symbol": "WADA", "to": "coingecko#cardano"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "BLUES", "decimals": "18", "symbol": "BLUES", "to": "coingecko#blueshift"}}, "csc": {"******************************************": {"name": "CET", "decimals": "18", "symbol": "CET", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped CET", "decimals": "18", "symbol": "WCET", "to": "ethereum:******************************************"}, "******************************************": {"name": "CSC Peg USDT", "decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}}, "fantom": {"0xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FTM", "to": "ethereum:******************************************"}, "******************************************": {"name": "USDL", "decimals": "18", "symbol": "USDL Stablecoin", "to": "fantom:******************************************"}, "******************************************": {"name": "Creditum USD", "decimals": "18", "symbol": "cUSD", "to": "fantom:******************************************"}, "******************************************": {"name": "SINGLE Token", "decimals": "18", "symbol": "SINGLE", "to": "cronos:******************************************"}, "******************************************": {"name": "Wrapped Nova USD", "decimals": "6", "symbol": "nUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Staked Ice Tokens", "decimals": "18", "symbol": "nICE", "to": "fantom:******************************************"}}, "reichain": {"0xdd2bb4e845bd97580020d8f9f58ec95bf549c3d9": {"name": "KillSwitch-Peg BUSD Token", "decimals": "18", "symbol": "kBUSD", "to": "bsc:******************************************"}, "0xf8ab4aaf70cef3f3659d3f466e35dc7ea10d4a5d": {"name": "BNB Token (BSC)", "decimals": "18", "symbol": "BNB", "to": "bsc:******************************************"}}, "cronos": {"0x87efb3ec1576dec8ed47e58b832bedcd86ee186e": {"name": "TrueUSD", "decimals": "18", "symbol": "TUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "crypto-com-chain", "decimals": "18", "symbol": "CRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"name": "Wrapped CRO", "decimals": "18", "symbol": "WCRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"name": "Artificial Liquid Intelligence Token", "decimals": "18", "symbol": "ALI", "to": "coingecko#alethea-artificial-liquid-intelligence-token"}, "******************************************": {"name": "AKT", "decimals": "6", "symbol": "AKT", "to": "coingecko#akash-network"}, "******************************************": {"name": "SHIBA INU", "decimals": "18", "symbol": "SHIB", "to": "coingecko#shiba-inu"}, "******************************************": {"name": "ATOM", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "ELON", "to": "coingecko#dogelon-mars"}}, "kcc": {"******************************************": {"name": "Elk", "decimals": "18", "symbol": "ELK", "to": "avax:******************************************"}, "******************************************": {"name": "kucoin-shares", "decimals": "18", "symbol": "KCS", "to": "coingecko#kucoin-shares"}, "******************************************": {"name": "Wrapped KCS", "decimals": "18", "symbol": "WKCS", "to": "coingecko#kucoin-shares"}, "******************************************": {"name": "MojitoToken", "decimals": "18", "symbol": "MJT", "to": "coingecko#mojitoswap"}, "******************************************": {"name": "KCC-<PERSON><PERSON>", "decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "KCC-Peg USD Coin", "decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Binance-Peg BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "KCC-Peg BTCK Token", "decimals": "18", "symbol": "BTCK", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Staked KCS", "decimals": "18", "symbol": "sKCS", "to": "coingecko#staked-kcs"}}, "findora": {"******************************************": {"name": "Wrapped BNB", "decimals": "18", "symbol": "WBNB.b", "to": "bsc:******************************************"}, "******************************************": {"name": "Wrapped Ethereum", "decimals": "18", "symbol": "WETH.b", "to": "bsc:******************************************"}, "******************************************": {"name": "USDT", "decimals": "18", "symbol": "USDT.b", "to": "bsc:******************************************"}, "******************************************": {"name": "USDC", "decimals": "18", "symbol": "USDC.b", "to": "bsc:******************************************"}, "******************************************": {"name": "BUSD", "decimals": "18", "symbol": "BUSD.b", "to": "bsc:******************************************"}, "******************************************": {"name": "Wrapped Bitcoin", "decimals": "18", "symbol": "WBTC.b", "to": "bsc:******************************************"}, "******************************************": {"name": "find<PERSON>", "decimals": "18", "symbol": "FRA", "to": "coingecko#findora"}, "******************************************": {"name": "Find<PERSON>", "decimals": "18", "symbol": "FRA", "to": "coingecko#findora"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT.e", "to": "coingecko#tether"}}, "moonriver": {"******************************************": {"decimals": "8", "symbol": "WBTC.eth", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BNB.bsc", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "WMOVR", "to": "coingecko#moonriver"}, "******************************************": {"decimals": "6", "symbol": "USDC.m", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "10", "symbol": "DOT.m", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "6", "symbol": "XRP.m", "to": "coingecko#ripple"}, "******************************************": {"decimals": "18", "symbol": "AVAX.m", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "FTM.m", "to": "coingecko#fantom"}, "******************************************": {"decimals": "6", "symbol": "USDT.m", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WAN.m", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "18", "symbol": "ETH.m", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "8", "symbol": "BTC.m", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "12", "symbol": " stKSM", "to": "moonriver:******************************************"}, "******************************************": {"name": "Elk", "decimals": "18", "symbol": "ELK", "to": "avax:******************************************"}, "******************************************": {"name": "Wrapped MOVR", "decimals": "18", "symbol": "WMOVR", "to": "moonriver:******************************************"}, "******************************************": {"name": "Lido DAO Token", "decimals": "18", "symbol": "LDO", "to": "coingecko#lido-dao"}, "******************************************": {"name": "Kintsugi Wrapped BTC", "decimals": "8", "symbol": "xcKBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "BEPRO Network", "decimals": "18", "symbol": "BEPRO", "to": "coingecko#bepro-network"}}, "bittorrent": {"******************************************": {"name": "Tether USD_TRON", "decimals": "6", "symbol": "USDT_t", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin_TRON", "decimals": "6", "symbol": "USDC_t", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTT", "decimals": "18", "symbol": "WBTT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Decentralized USD_TRON", "decimals": "18", "symbol": "USDD_t", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin_Ethereum", "decimals": "6", "symbol": "USDC_e", "to": "ethereum:******************************************"}, "******************************************": {"name": "ETH", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD_Ethereum", "decimals": "6", "symbol": "USDT_e", "to": "ethereum:******************************************"}, "******************************************": {"name": "Kyber Network Crystal v2 - Ethereum", "decimals": "18", "symbol": "KNC_e", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTC_Ethereum", "decimals": "8", "symbol": "WBTC_e", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin_BSC", "decimals": "18", "symbol": "USDC_b", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Binance-Peg BSC-USD_BSC", "decimals": "18", "symbol": "USDT_b", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTT", "decimals": "18", "symbol": "WBTT", "to": "coingecko#bittorrent"}, "******************************************": {"name": "TRX", "decimals": "6", "symbol": "TRX", "to": "coingecko#tron"}}, "evmos": {"******************************************": {"decimals": "18", "symbol": "STEVMOS", "to": "coingecko#evmos"}, "******************************************": {"decimals": "18", "symbol": "AXLWETH", "to": "coingecko#axlweth"}, "******************************************": {"decimals": "6", "symbol": "STATOM", "to": "coingecko#stride-staked-atom"}, "******************************************": {"decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "******************************************": {"decimals": "6", "symbol": "STRIDE", "to": "coingecko#stride"}, "******************************************": {"decimals": "18", "symbol": "axlRETH", "to": "coingecko#rocket-pool-eth"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "tBTC v2", "decimals": "18", "symbol": "tBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "renBTC", "decimals": "8", "symbol": "renBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin (Celer)", "decimals": "6", "symbol": "ceUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether <PERSON> (Celer)", "decimals": "6", "symbol": "ceUSDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "Diffusion", "decimals": "18", "symbol": "DIFF", "to": "coingecko#diffusion"}, "******************************************": {"name": "Wrapped Evmos", "decimals": "18", "symbol": "WEVMOS", "to": "coingecko#evmos"}}, "ethereum": {"******************************************": {"decimals": "18", "symbol": "stataEthWETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "bb-a-WETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": 8, "symbol": "BITCOIN", "to": "coingecko#harrypotterobamasonic10in"}, "******************************************": {"decimals": 18, "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": 6, "symbol": "cUSDCv3", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": 18, "symbol": "cWETHv3", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "vlCVX", "to": "coingecko#convex-finance"}, "******************************************": {"name": "Convex FXS", "decimals": "18", "symbol": "cvxFXS", "to": "ethereum:******************************************"}, "******************************************": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "bsc:******************************************"}, "******************************************": {"name": "ParaSpace Compound APE", "decimals": "18", "symbol": "cAPE", "to": "ethereum:******************************************"}, "******************************************": {"name": "Staked CvxCrv", "decimals": "18", "symbol": "stkCvxCrv", "to": "ethereum:******************************************"}, "******************************************": {"name": "BXHToken", "decimals": "18", "symbol": "BXH", "to": "heco:******************************************"}, "******************************************": {"name": "Cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "******************************************": {"name": "tBTC v2", "decimals": "18", "symbol": "tBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "kUSD", "decimals": "18", "symbol": "kUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "fryUSD", "decimals": "18", "symbol": "fUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemakWethPool", "decimals": "18", "symbol": "tWETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemakWethPool", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Brewlabs", "decimals": "9", "symbol": "BREWLABS", "to": "coingecko#brewlabs"}, "******************************************": {"name": "TokemaktFRAX", "decimals": "18", "symbol": "tFRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktGAMMA", "decimals": "18", "symbol": "tGAMMA", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktSNX", "decimals": "18", "symbol": "tSNX", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktAPW", "decimals": "18", "symbol": "tAPW", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktTCR", "decimals": "18", "symbol": "tTCR", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktFOX", "decimals": "18", "symbol": "tFOX", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktSUSHI", "decimals": "18", "symbol": "tSUSHI", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktALCX", "decimals": "18", "symbol": "tALCX", "to": "ethereum:******************************************"}, "******************************************": {"name": "TokemaktFXS", "decimals": "18", "symbol": "tFXS", "to": "ethereum:******************************************"}, "******************************************": {"name": "SORA Synthetic USD", "decimals": "18", "symbol": "XSTUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Staked DYDX", "decimals": "18", "symbol": "stkDYDX", "to": "ethereum:******************************************"}, "******************************************": {"name": "dForce USD", "decimals": "18", "symbol": "USX", "to": "bsc:******************************************"}, "******************************************": {"name": "ElasticSwap Tic Token", "decimals": "18", "symbol": "TIC", "to": "avax:******************************************"}, "******************************************": {"name": "Morpho-Aave Dai Stablecoin Supply Vault", "decimals": "18", "symbol": "maDAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synth sJPY", "decimals": "18", "symbol": "sJPY", "to": "coingecko#jpyc"}, "******************************************": {"name": "NEUY", "decimals": "18", "symbol": "NEUY", "to": "coingecko#neuy"}, "******************************************": {"name": "Synth sGBP", "decimals": "18", "symbol": "sGBP", "to": "coingecko#jarvis-synthetic-british-pound"}, "******************************************": {"name": "Synth sCHF", "decimals": "18", "symbol": "sCHF", "to": "coingecko#upper-swiss-franc"}, "******************************************": {"name": "Neutrino EUR", "decimals": "18", "symbol": "EURN", "to": "coingecko#tether-eurt"}, "******************************************": {"name": "Wrapped Interest-Bearing Bitcoin", "decimals": "18", "symbol": "wibBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": " Curio Governance Token", "decimals": "18", "symbol": "CGT", "to": "coingecko#curio-governance"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "ARDN", "to": "coingecko#ariadne"}, "******************************************": {"name": "PAD", "decimals": "18", "symbol": "PAD", "to": "coingecko#smartpad-2"}, "******************************************": {"name": "Tokemak", "decimals": "18", "symbol": "TOKE", "to": "coingecko#tokemak"}, "******************************************": {"name": "SushiToken", "decimals": "18", "symbol": "SUSHI", "to": "coingecko#sushi"}, "******************************************": {"name": "FLOKI", "decimals": "9", "symbol": "FLOKI", "to": "coingecko#floki"}, "******************************************": {"name": "PoolTogether aUSDC Ticket", "decimals": "6", "symbol": "PTaUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "ETHWIN SPONSOR", "decimals": "18", "symbol": "SPETHWIN", "to": "coingecko#staked-ether"}, "******************************************": {"name": "Stake DAO CRV", "decimals": "18", "symbol": "sdCRV", "to": "coingecko#stake-dao-crv"}, "******************************************": {"name": "IDLE", "decimals": "18", "symbol": "IDLE", "to": "coingecko#idle"}, "******************************************": {"name": "UwU", "decimals": "18", "symbol": "UwU", "to": "coingecko#uwu-lend"}, "******************************************": {"name": "fUSDC", "decimals": "8", "symbol": "UwU", "to": "coingecko#flux-usdc"}, "******************************************": {"decimals": "18", "symbol": "rUSD", "to": "coingecko#fx-rusd"}, "******************************************": {"decimals": "8", "symbol": "WAVES", "to": "coingecko#waves"}}, "klaytn": {"******************************************": {"decimals": "18", "symbol": "oWEMIX", "to": "coingecko#wemix-token"}, "******************************************": {"name": "DON token for KLAYTN", "decimals": "18", "symbol": "oDON", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "IJM", "to": "bsc:******************************************"}, "******************************************": {"name": "Tether USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "popkorn USD", "decimals": "18", "symbol": "pUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "KASH", "to": "ethereum:******************************************"}, "0x210bc03f49052169d5588a52c317f71cf2078b85": {"name": "Orbit Bridge Klaytn BUSD Token", "decimals": "18", "symbol": "oBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "klay-token", "decimals": "18", "symbol": "KLAY", "to": "coingecko#klay-token"}, "0xd7a4d10070a4f7bc2a015e78244ea137398c3b74": {"name": "Wrapped Klay", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "0xfd844c2fca5e595004b17615f891620d1cb9bbb2": {"name": "Wrapped KLAY", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "0xff3e7cf0c007f919807b32b30a4a9e7bd7bc4121": {"name": "Wrapped KLAY v10", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "0xe4f05a66ec68b54a58b17c22107b02e0232cc817": {"name": "Wrapped Klay", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "0xf6f6b8bd0ac500639148f8ca5a590341a97de0de": {"name": "Wrapped Klaytn", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "******************************************": {"name": "Wrapped Klay", "decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "******************************************": {"name": "Orbit Bridge Klaytn Ripple", "decimals": "6", "symbol": "oXRP", "to": "coingecko#ripple"}, "******************************************": {"name": "KlaySwap Protocol", "decimals": "18", "symbol": "KSP", "to": "coingecko#klayswap-protocol"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Wrapped BNB", "decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Wrapped AVAX", "decimals": "18", "symbol": "WAVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"name": "Wrapped SOL", "decimals": "9", "symbol": "SOL", "to": "coingecko#solana"}, "******************************************": {"name": "Wrapped aUSDC (Wormhole)", "decimals": "6", "symbol": "aUSDC (Wormhole)", "to": "coingecko#aave-usdc"}, "******************************************": {"name": "Wrapped aUSDC", "decimals": "6", "symbol": "aUSDC", "to": "coingecko#aave-usdc"}, "0x2eadfda6d830547b5168ba88c13d24156a026ce5": {"name": "Wrapped aUSDT", "decimals": "6", "symbol": "aUSDT", "to": "coingecko#aave-usdt"}, "0x98aedff55dcc2e7a7d1899b325d1680527dd2742": {"name": "Wrapped aUSDT (Wormhole)", "decimals": "6", "symbol": "aUSDT (Wormhole)", "to": "coingecko#aave-usdt"}, "0x2ff5371dad5c6ef76d55213b7c5a519f6654ba17": {"name": "Wrapped aDAI", "decimals": "18", "symbol": "aDAI", "to": "coingecko#aave-dai"}, "0xe9a88c33abf71c902f7581321d05e6516cbca761": {"name": "Wrapped aDAI (Wormhole)", "decimals": "18", "symbol": "aDAI (Wormhole)", "to": "coingecko#aave-dai"}, "0xd2137fdf10bd9e4e850c17539eb24cfe28777753": {"name": "Krome Stablecoin", "decimals": "18", "symbol": "USDK", "to": "coingecko#krome-stablecoin-bad"}, "0x127a75b751ba810e459121af6207d83841c586b7": {"name": "Orbit Bridge Klaytn Meshswap Protocol", "decimals": "18", "symbol": "oMESH", "to": "coingecko#meshswap-protocol"}, "0x4b96dbf8f42c8c296573933a6616dcafb80ca461": {"name": "Orbit Bridge Klaytn Toncoin", "decimals": "9", "symbol": "oTON", "to": "coingecko#the-open-network"}, "******************************************": {"name": "ATOM", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "0xe06597d02a2c3aa7a9708de2cfa587b128bd3815": {"name": "NEOPIN Token", "decimals": "18", "symbol": "NPT", "to": "coingecko#neopin"}}, "aurora": {"0x6bb0c4d909a84d118b5e6c4b17117e79e621ae94": {"decimals": "18", "symbol": "WNEAR", "to": "aurora:******************************************"}, "******************************************": {"decimals": "18", "symbol": "WstNEAR", "to": "aurora:******************************************"}, "******************************************": {"decimals": "18", "symbol": "WMETA", "to": "aurora:******************************************"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "nUSD", "decimals": "18", "symbol": "nUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Polygon", "decimals": "6", "symbol": "sUSDC", "to": "polygon:******************************************"}, "******************************************": {"name": "Synthetic Binance-Peg BUSD Token from BSC", "decimals": "18", "symbol": "sBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "heco": {"******************************************": {"name": "Heco-Peg BETH Token", "decimals": "18", "symbol": "BETH", "to": "bsc:******************************************"}, "******************************************": {"name": "Elk", "decimals": "18", "symbol": "ELK", "to": "avax:******************************************"}, "0xhecozzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz": {"name": "HECO", "decimals": "18", "symbol": "HECO", "to": "ethereum:******************************************"}, "******************************************": {"name": "Heco-Peg DAIHECO Token", "decimals": "18", "symbol": "DAI-HECO", "to": "ethereum:******************************************"}, "******************************************": {"name": "Heco-Peg USDCHECO Token", "decimals": "6", "symbol": "USDC-HECO", "to": "ethereum:******************************************"}, "******************************************": {"name": "Heco-Peg COMP Token", "decimals": "18", "symbol": "COMP", "to": "ethereum:******************************************"}, "******************************************": {"name": "LendHub DOGE", "decimals": "8", "symbol": "lDOGE", "to": "ethereum:******************************************"}, "******************************************": {"name": "Heco-Peg DOGE <PERSON>", "decimals": "8", "symbol": "DOGE", "to": "bsc:******************************************"}, "******************************************": {"name": "Heco-Peg TUSD Token", "decimals": "18", "symbol": "TUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Heco-Peg XRP Token", "decimals": "6", "symbol": "XRP", "to": "bsc:******************************************"}, "******************************************": {"name": "Heco-Peg ADA Token", "decimals": "6", "symbol": "ADA", "to": "bsc:******************************************"}, "******************************************": {"name": "APYSwap", "decimals": "18", "symbol": "APYS", "to": "coingecko#apyswap"}}, "oasis": {"******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Tether <PERSON> (Celer)", "decimals": "6", "symbol": "ceUSDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped ROSE", "decimals": "18", "symbol": "wROSE", "to": "coingecko#oasis-network"}, "******************************************": {"name": "Wrapped ROSE", "decimals": "18", "symbol": "WROSE", "to": "coingecko#oasis-network"}, "******************************************": {"name": "TULIP", "decimals": "18", "symbol": "TULIP", "to": "coingecko#oasis-network"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USD Coin (Celer)", "decimals": "6", "symbol": "ceUSDC", "to": "coingecko#usd-coin"}}, "harmony": {"******************************************": {"name": "Avalanche", "decimals": "18", "symbol": "AVAX", "to": "avax:******************************************"}, "******************************************": {"name": "Reverse Token", "decimals": "18", "symbol": "RVRS", "to": "ethereum:0x123"}, "******************************************": {"name": "Wrapped ONE", "decimals": "18", "symbol": "WONE", "to": "coingecko#harmony"}, "******************************************": {"name": "xJewels", "decimals": "18", "symbol": "xJEWEL", "to": "coingecko#xjewel"}, "******************************************": {"name": "Viper", "decimals": "18", "symbol": "VIPER", "to": "coingecko#viper"}}, "avax": {"******************************************": {"decimals": "18", "symbol": "MAI", "to": "coingecko#mimatic"}, "******************************************": {"decimals": "18", "symbol": "sGLP", "to": "avax:******************************************"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "agEUR", "to": "coingecko#ageur"}, "******************************************": {"decimals": "18", "symbol": "QI", "to": "coingecko#benqi"}, "0xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx": {"name": "AVAX", "decimals": "8", "symbol": "AVAX", "to": "avax:******************************************"}, "******************************************": {"name": "Staked GLP", "decimals": "18", "symbol": "sGLP", "to": "avax:******************************************"}, "******************************************": {"name": "Benqi AVAX", "decimals": "8", "symbol": "qiAVAX", "to": "avax:******************************************"}, "******************************************": {"name": "fsGLP", "symbol": "fsGLP", "decimals": 18, "to": "avax:******************************************"}, "******************************************": {"name": "fGLP", "symbol": "fGLP", "decimals": 18, "to": "avax:******************************************"}, "0x9debca6ea3af87bf422cea9ac955618ceb56efb4": {"name": "DMMDAI", "decimals": "18", "symbol": "mDAI", "to": "coingecko#avalanche-2"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "xJOE", "to": "avax:******************************************"}, "******************************************": {"name": "Locked VTX", "decimals": "18", "symbol": "LVTX", "to": "avax:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Ethereum", "decimals": "6", "symbol": "sUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic Binance-Peg BUSD Token from BSC", "decimals": "18", "symbol": "sBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Moo Aave AVAX", "decimals": "18", "symbol": "mooAaveAVAX", "to": "avax:******************************************"}, "******************************************": {"name": "Moo Aave USDC.e", "decimals": "18", "symbol": "mooAaveUSDC.e", "to": "avax:******************************************"}, "******************************************": {"name": "Moo Aave USDT.e", "decimals": "18", "symbol": "mooAaveUSDT.e", "to": "avax:******************************************"}, "******************************************": {"name": "Moo Aave DAI.e", "decimals": "18", "symbol": "mooAaveDAI.e", "to": "avax:******************************************"}, "******************************************": {"name": "Moo PangolinV2 PNG", "decimals": "18", "symbol": "mooPangolinV2PNG", "to": "avax:******************************************"}, "******************************************": {"name": "Stake DAO Curve.fi avDAI/avUSDC/avUSDT", "decimals": "18", "symbol": "sdav3CRV", "to": "ethereum:******************************************"}, "******************************************": {"name": "BXHToken", "decimals": "18", "symbol": "BXH", "to": "heco:******************************************"}, "******************************************": {"name": "Wrapped AVAX", "decimals": "18", "symbol": "WAVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "KONG", "to": "coingecko#kong"}, "******************************************": {"decimals": "18", "symbol": "TECH", "to": "coingecko#tech"}}, "moonbeam": {"******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}, "******************************************": {"name": "Moonriver", "decimals": "18", "symbol": "MOVR", "to": "moonriver:******************************************"}, "******************************************": {"name": "Wrapped Glimmer", "decimals": "18", "symbol": "WGLMR", "to": "moonbeam:******************************************"}, "******************************************": {"name": "Glimmer", "decimals": "18", "symbol": "GLMR", "to": "moonbeam:******************************************"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "xcUSDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Tether <PERSON> (Celer)", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin (Celer)", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Wrapped <PERSON><PERSON> (Ce<PERSON>)", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "BEPRO Network", "decimals": "18", "symbol": "BEPRO", "to": "coingecko#bepro-network"}, "******************************************": {"name": "Liquid staked DOT", "decimals": "10", "symbol": "stDOT", "to": "coingecko#polkadot"}}, "polygon": {"******************************************": {"decimals": 6, "symbol": "cUSDCv3", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "OUSG", "to": "ethereum:******************************************"}, "******************************************": {"name": "Polygon", "decimals": "18", "symbol": "MATIC", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "iZi", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Ethereum", "decimals": "6", "symbol": "sUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "moUSD", "decimals": "18", "symbol": "moUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Synthetic Binance-Peg BUSD Token from BSC", "decimals": "18", "symbol": "sBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Synthetic USD Coin from Avalanche", "decimals": "6", "symbol": "sUSDC.e", "to": "avax:******************************************"}, "******************************************": {"name": "deBridge USD Coin", "decimals": "6", "symbol": "deUSDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "YIN Finance", "decimals": "18", "symbol": "YIN", "to": "ethereum:******************************************"}, "******************************************": {"name": "Ocean Token (PoS)", "decimals": "18", "symbol": "mOCEAN", "to": "ethereum:******************************************"}, "******************************************": {"name": "XSGD (PoS)", "decimals": "6", "symbol": "XSGD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped TrueCAD", "decimals": "18", "symbol": "wTCAD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped TrueAUD", "decimals": "18", "symbol": "wTAUD", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped TrueGBP", "decimals": "18", "symbol": "wTGBP", "to": "ethereum:******************************************"}, "******************************************": {"name": "Polygon LEO", "decimals": "3", "symbol": "pLEO", "to": "bsc:******************************************"}, "******************************************": {"name": "Polygon SPS", "decimals": "8", "symbol": "pSPS", "to": "bsc:******************************************"}, "******************************************": {"name": "rUSD", "decimals": "18", "symbol": "rUSD", "to": "coingecko#rusd"}, "******************************************": {"name": "sMVLP", "decimals": "18", "symbol": "sMVLP", "to": "polygon:******************************************"}, "******************************************": {"name": "CelsiusX Wrapped ADA", "decimals": "18", "symbol": "cxADA", "to": "coingecko#cardano"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#matic-network"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#matic-network"}, "******************************************": {"name": "APYSwap", "decimals": "18", "symbol": "APYS", "to": "coingecko#apyswap"}, "******************************************": {"name": "Binance Token", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "IRON Stableswap 3USD", "decimals": "18", "symbol": "IS3USD", "to": "coingecko#tether"}, "******************************************": {"name": "BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "The Employment Commons Work Token (PoS)", "decimals": "18", "symbol": "WORK", "to": "ethereum:0x123"}, "******************************************": {"name": "IDLE Polygon", "decimals": "18", "symbol": "IDLE", "to": "coingecko#idle"}, "******************************************": {"decimals": "18", "symbol": "ZEEP", "to": "coingecko#zeepr"}}, "rollux": {"******************************************": {"decimals": "18", "symbol": "SYS", "to": "coingecko#syscoin"}, "******************************************": {"decimals": "18", "symbol": "PSYS", "to": "coingecko#pegasys"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "WSYS", "to": "coingecko#syscoin"}}, "filecoin": {"******************************************": {"decimals": "18", "symbol": "wFIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "wpFIL", "to": "coingecko#filecoin"}}, "arbitrum": {"******************************************": {"decimals": "18", "symbol": "SVUSD", "to": "coingecko#savvy-usd"}, "******************************************": {"decimals": "18", "symbol": "AURA", "to": "coingecko#aura-finance"}, "******************************************": {"decimals": "18", "symbol": "GRAI", "to": "coingecko#grai"}, "******************************************": {"decimals": "18", "symbol": "rETH", "to": "coingecko#reth"}, "******************************************": {"decimals": "9", "symbol": "wSOL", "to": "coingecko#solana"}, "******************************************": {"decimals": "18", "symbol": "esGMD", "to": "arbitrum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "xGND", "to": "arbitrum:******************************************"}, "******************************************": {"decimals": 6, "symbol": "cUSDCv3", "to": "coingecko#usd-coin"}, "******************************************": {"name": "<PERSON><PERSON> (Arbitrum)", "decimals": "18", "symbol": "ARBY", "to": "polygon:******************************************"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "sGLP", "decimals": "18", "symbol": "sGLP", "to": "arbitrum:******************************************"}, "******************************************": {"name": "sGLP", "decimals": "18", "symbol": "sGLP", "to": "arbitrum:******************************************"}, "******************************************": {"name": "dfsGLP", "decimals": "18", "symbol": "dfsGLP", "to": "arbitrum:******************************************"}, "******************************************": {"name": "renBTC", "decimals": "8", "symbol": "renBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "xDollar", "decimals": "18", "symbol": "XDO", "to": "polygon:******************************************"}, "******************************************": {"name": "DeFIL-V2", "decimals": "18", "symbol": "DFL", "to": "ethereum:******************************************"}, "******************************************": {"name": "Fei USD", "decimals": "18", "symbol": "FEI", "to": "ethereum:******************************************"}, "******************************************": {"name": "nUSD", "decimals": "18", "symbol": "nUSD", "to": "ethereum:******************************************"}, "******************************************": {"name": "ApeX Token", "decimals": "18", "symbol": "APEX", "to": "ethereum:******************************************"}, "******************************************": {"name": "Fee + Staked GLP", "decimals": "18", "symbol": "fsGLP", "to": "arbitrum:******************************************"}, "******************************************": {"name": "Fee GLP", "decimals": "18", "symbol": "fGLP", "to": "arbitrum:******************************************"}, "******************************************": {"name": "Liquid", "decimals": "18", "symbol": "LIQD", "to": "coingecko#liquid-finance"}, "******************************************": {"name": "DSU", "decimals": "18", "symbol": "DSU", "to": "coingecko#digital-standard-unit"}, "******************************************": {"decimals": "18", "symbol": "PAXG", "to": "coingecko#pax-gold"}}, "telos": {"******************************************": {"decimals": "18", "symbol": "WTLOS", "to": "coingecko#telos"}, "******************************************": {"name": "Synthetic Binance-Peg BUSD Token from BSC", "decimals": "18", "symbol": "sBUSD", "to": "bsc:******************************************"}, "******************************************": {"name": "Telos", "decimals": "18", "symbol": "TLOS", "to": "coingecko#telos"}, "******************************************": {"name": "Staked Telos", "decimals": "18", "symbol": "STLOS", "to": "coingecko#telos"}, "******************************************": {"name": "Wrapped TLOS", "decimals": "18", "symbol": "WTLOS", "to": "telos:******************************************"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Bitcoin", "decimals": "8", "symbol": "BTC.b", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "6", "symbol": "syUSDC", "to": "coingecko#usd-coin"}}, "solana": {"9EaLkQrbjmbbuZG9Wdpo8qfNUEjHATJFSycEmw6f1rGX": {"name": "Parrot SOL", "decimals": "9", "symbol": "pSOL", "to": "solana:So11111111111111111111111111111111111111112"}, "METAewgxyPbgwsseH8T16a39CQ5VyVxZi9zXiDPY18m": {"name": "Metaplex", "decimals": "6", "symbol": "META", "to": "coingecko#metaplex"}, "DUALa4FC2yREwZ59PHeu1un4wis36vHRv5hWVBmzykCJ": {"decimals": "6", "symbol": "DUAL", "to": "coingecko#dual-finance"}, "JET6zMJWkCN9tpRT2v2jfAmm5VnQFDpUBCyaKojmGtz": {"decimals": "9", "symbol": "JET", "to": "coingecko#jet"}, "CvB1ztJvpYQPvdPBePtRzjL4aQidjydtUz61NWgcgQtP": {"decimals": "6", "symbol": "EPCT", "to": "coingecko#epics-token"}, "BWXrrYFhT7bMHmNBFoQFWdsSgA3yXoAnMhDK6Fn1eSEn": {"decimals": "9", "symbol": "HADES", "to": "coingecko#hades"}, "J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn": {"name": "Jito Staked SOL", "decimals": "9", "symbol": "JITOSOL", "to": "coingecko#jito-staked-sol"}, "6LNeTYMqtNm1pBFN8PfhQaoLyegAH8GD32WmHU9erXKN": {"name": "Aptos Coin (Wormhole)", "decimals": "8", "symbol": "APT", "to": "coingecko#aptos"}, "EjmyN6qEC1Tf1JxiG1ae7UTJhUxSwk1TCWNWqxWV4J6o": {"name": "<PERSON> (Wormhole)", "decimals": "8", "symbol": "DAI", "to": "ethereum:******************************************"}, "eqKJTf1Do4MDPyKisMYqVaUFpkEFAs3riGF3ceDH2Ca": {"name": "Wrapped USDC (Allbridge from Polygon)", "decimals": "6", "symbol": "apUSDC", "to": "ethereum:******************************************"}, "FCqfQSujuPxy6V42UvafBhsysWtEq1vhjfMN1PUbgaxA": {"name": "USD Coin (Wormhole)", "decimals": "8", "symbol": "USDC", "to": "ethereum:******************************************"}, "DdFPRnccQqLD4zCHrBqdY95D6hvw6PLWp9DEXj1fLCL9": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "aeUSDC", "to": "ethereum:******************************************"}, "8Yv9Jz4z7BUHP68dz8E8m3tMe6NKgpMUKn8KVqrPA6Fr": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "aaUSDC", "to": "ethereum:******************************************"}, "Grk6b4UMRWkgyq4Y6S1BnNRF4hRgtnMFp7Sorkv6Ez4u": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "afUSDC", "to": "ethereum:******************************************"}, "8XSsNvaKU9FDhYWAv7Yc7qSNwuJSzVrXBNEk7AFiWF69": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "abUSDC", "to": "ethereum:******************************************"}, "8qJSyQprMC57TWKaYEmetUR3UUiTP2M3hXdcvFhkZdmv": {"name": "ethereum:******************************************", "decimals": "8", "symbol": "USDT", "to": "ethereum:******************************************"}, "E77cpQ4VncGmcAXX16LHFFzNBEBb2U7Ar7LBmZNfCgwL": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "abUSDT", "to": "ethereum:******************************************"}, "Bn113WT6rbdgwrm12UJtnmNqGqZjY4it2WoUQuQopFVn": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "aeUSDT", "to": "ethereum:******************************************"}, "FwEHs3kJEdMa2qZHv7SgzCiFXUQPEycEXksfBkwmS8gj": {"name": "ethereum:******************************************", "decimals": "9", "symbol": "aaUSDT", "to": "ethereum:******************************************"}, "5RpUwQ8wtdPCZHhu6MERp2RGrpobsbZ6MH5dDHkUjs2": {"name": "bsc:******************************************", "decimals": "8", "symbol": "BUSD", "to": "bsc:******************************************"}, "PUhuAtMHsKavMTwZsLaDeKy2jb7ciETHJP7rhbKLJGY": {"name": "usn", "decimals": "9", "symbol": "USN", "to": "coingecko#usn"}, "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263": {"name": "bonk", "decimals": "5", "symbol": "Bonk", "to": "coingecko#bonk"}, "DEkqHyPN7GMRJ5cArtQFAWefqbZb33Hyf6s5iCwjEonT": {"decimals": "9", "symbol": "USDe", "to": "coingecko#ethena-usde"}, "4MmJVdwYN8LwvbGeCowYjSx7KoEi6BJWg8XXnW4fDDp6": {"decimals": "6", "symbol": "TBILL", "to": "coingecko#openeden-tbill"}}, "astar": {"******************************************": {"decimals": "12", "symbol": "AUSD", "to": "coingecko#acala-dollar-acala"}, "******************************************": {"name": "astar", "decimals": "18", "symbol": "ASTR", "to": "coingecko#astar"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ORU", "to": "coingecko#orcus-oru"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "MUUU", "to": "coingecko#muuu"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "NIKA", "to": "coingecko#alnair-finance-nika"}, "******************************************": {"name": "oUSD", "decimals": "18", "symbol": "oUSD", "to": "coingecko#origin-dollar"}, "0xdd90e5e87a2081dcf0391920868ebc2ffb81a1af": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "MATIC", "to": "coingecko#wmatic"}, "******************************************": {"name": "Kagla DAO Token", "decimals": "18", "symbol": "KGL", "to": "coingecko#kagla-finance"}, "******************************************": {"name": "<PERSON>", "decimals": "18", "symbol": "LAY", "to": "coingecko#starlay-finance"}, "******************************************": {"name": "ArthSwap Token", "decimals": "18", "symbol": "ARSW", "to": "coingecko#arthswap"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Binance Coin", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Shiden Network", "decimals": "18", "symbol": "SDN", "to": "coingecko#shiden"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped Astar", "decimals": "18", "symbol": "WASTR", "to": "coingecko#astar"}, "******************************************": {"name": "Starfish Token", "decimals": "18", "symbol": "SEAN", "to": "coingecko#starfish-finance"}, "******************************************": {"name": "Starlay interest bearing USDT", "decimals": "6", "symbol": "lUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped Astar", "decimals": "18", "symbol": "WASTR", "to": "coingecko#astar"}, "******************************************": {"name": "Astar Note", "decimals": "18", "symbol": "nASTR", "to": "coingecko#astar"}, "******************************************": {"name": "Wrapped Astar", "decimals": "18", "symbol": "WASTR", "to": "coingecko#astar"}, "******************************************": {"name": "Wrapped ASTR", "decimals": "18", "symbol": "WASTR", "to": "coingecko#astar"}, "******************************************": {"name": "EmiDAO Token", "decimals": "18", "symbol": "ESW", "to": "coingecko#emiswap"}, "******************************************": {"name": "Starlay interest bearing USDC", "decimals": "6", "symbol": "lUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0x6de33698e9e9b787e09d3bd7771ef63557e148bb": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "0x4dd9c468a44f3fef662c35c1e9a6108b70415c2c": {"name": "Starlay interest bearing DAI", "decimals": "18", "symbol": "lDAI", "to": "coingecko#dai"}, "0xdbd71969ac2583a9a20af3fb81fe9c20547f30f3": {"name": "avault Arthswap BAI-USDC LP", "decimals": "18", "symbol": "aBaiUsdc", "to": "coingecko#dai"}, "0x9914bff0437f914549c673b34808af6020e2b453": {"name": "avault Arthswap DAI-USDC LP", "decimals": "18", "symbol": "aDaiUsdc", "to": "coingecko#dai"}, "0x347e53263f8fb843ec605a1577ec7c8c0cac7a58": {"name": "avault Arthswap BUSD-USDC LP", "decimals": "18", "symbol": "aBusdUsdc", "to": "coingecko#dai"}, "0x02dac4898b2c2ca9d50ff8d6a7726166cf7bcfd0": {"name": "avault Arthswap USDT-USDC LP", "decimals": "18", "symbol": "aUsdtUsdc", "to": "coingecko#dai"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "0xb7ab962c42a8bb443e0362f58a5a43814c573ffb": {"name": "Starlay interest bearing BUSD", "decimals": "18", "symbol": "lBUSD", "to": "coingecko#binance-usd"}, "0x733ebcc6df85f8266349defd0980f8ced9b45f35": {"name": "BAI Stablecoin", "decimals": "18", "symbol": "BAI", "to": "coingecko#bai-stablecoin"}, "0x5271d85ce4241b310c0b34b7c2f1f036686a6d7c": {"name": "ATID", "decimals": "18", "symbol": "ATID", "to": "coingecko#astriddao-token"}, "0xffffffffffffffffffffffffffffffffffffffff": {"name": "<PERSON><PERSON>t", "decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "0x431d5dff03120afa4bdf332c61a6e1766ef37bdb": {"name": "JPY Coin", "decimals": "18", "symbol": "JPYC", "to": "coingecko#jpy-coin"}, "0xFfFfFfff00000000000000010000000000000008": {"name": "Voucher DOT", "decimals": "10", "symbol": "vDOT", "to": "coingecko#voucher-dot"}}, "cardano": {"ADA": {"name": "cardano", "decimals": "6", "symbol": "ADA", "to": "coingecko#cardano"}}, "omax": {"******************************************": {"name": "omax-token", "decimals": "18", "symbol": "OMAX", "to": "coingecko#omax-token"}, "0x373e4b4e4d328927bc398a9b50e0082c6f91b7bb": {"name": "Wrapped OMAX", "decimals": "18", "symbol": "WOMAX", "to": "coingecko#omax-token"}}, "wemix": {"******************************************": {"name": "Wemix", "decimals": "18", "symbol": "WEMIX", "to": "coingecko#wemix-token"}, "0xc1be9a4d5d45beeacae296a7bd5fadbfc14602c4": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "0x2b58644b9f210ebb8fbf4c27066f9d1d97b03cbc": {"name": "Wrapped REFLECT", "decimals": "18", "symbol": "wRFT", "to": "coingecko#wrapped-reflect"}, "0x7d72b22a74a216af4a002a1095c8c707d6ec1c5f": {"name": "Wrapped Wemix", "decimals": "18", "symbol": "WWEMIX", "to": "coingecko#wemix-token"}, "******************************************": {"name": "WEMIX$", "decimals": "18", "symbol": "WEMIX$", "to": "coingecko#wemix-dollar"}, "******************************************": {"name": "Staked WEMIX", "decimals": "18", "symbol": "stWEMIX", "to": "coingecko#staked-wemix"}, "******************************************": {"name": "Klaytn", "decimals": "18", "symbol": "KLAY", "to": "coingecko#klay-token"}, "******************************************": {"name": "ETH", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "WBTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "defichain": {"DFI": {"name": "defichain", "decimals": "0", "symbol": "DFI", "to": "coingecko#defichain"}, "DUSD": {"name": "decentralized-usd", "decimals": "0", "symbol": "DUSD", "to": "coingecko#decentralized-usd"}, "ETH": {"name": "ethereum", "decimals": "0", "symbol": "ETH", "to": "coingecko#ethereum"}, "BTC": {"name": "bitcoin", "decimals": "0", "symbol": "BTC", "to": "coingecko#bitcoin"}, "USDC": {"name": "usd-coin", "decimals": "0", "symbol": "USDC", "to": "coingecko#usd-coin"}, "USDT": {"name": "tether", "decimals": "0", "symbol": "USDT", "to": "coingecko#tether"}}, "step": {"******************************************": {"name": "Wrapped FITFI", "decimals": "18", "symbol": "WFITFI", "to": "coingecko#step-app-fitfi"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}}, "functionx": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BAVA", "to": "coingecko#baklava"}, "******************************************": {"name": "fx-coin", "decimals": "18", "symbol": "FX", "to": "coingecko#fx-coin"}, "******************************************": {"name": "Wrapped Function X", "decimals": "18", "symbol": "WFX", "to": "coingecko#fx-coin"}, "******************************************": {"name": "Pundi X Token", "decimals": "18", "symbol": "PUNDIX", "to": "coingecko#pundi-x-2"}, "******************************************": {"name": "PURSE TOKEN", "decimals": "18", "symbol": "PURSE", "to": "coingecko#pundi-x-purse"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "clv": {"******************************************": {"name": "Wrapped CLV", "decimals": "18", "symbol": "WCLV", "to": "coingecko#clover-finance"}, "******************************************": {"name": "Wrapped CLV", "decimals": "18", "symbol": "WCLV", "to": "coingecko#clover-finance"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "tron": {"TRFe3hT5oYhjSZ6f3ji5FJ7YCfrkWnHRvh": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "TGkxzkDKyMeq2T7edKnyjZoFypyzjkkssq": {"decimals": "18", "symbol": "wstUSDT", "to": "coingecko#wrapped-staked-usdt"}, "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "TPYmHEhy5n8TCEfYGqW2rPxsghSfzghPDn": {"name": "usdd", "decimals": "18", "symbol": "USDD", "to": "coingecko#usdd"}, "TVHH59uHVpHzLDMFFpUgCx2dNAQqCzPhcR": {"name": "justmoney-2", "decimals": "8", "symbol": "JM", "to": "coingecko#justmoney-2"}, "TN3W4H6rK2ce4vX9YnFQHwKENnHjoxb3m9": {"name": "bitcoin", "decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "TR3DLthpnDdCGabhVDbD3VMsiJoCXY3bZd": {"name": "litecoin", "decimals": "8", "symbol": "LTC", "to": "coingecko#litecoin"}, "THbVQp8kMjStKNnf2iCY6NEzThKMK5aBHg": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "8", "symbol": "DOGE", "to": "coingecko#dogecoin"}, "THb4CqiFdwNHsWsQCs4JhzwjMWys4aqCbF": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "TNUC9Qb1rRpS5CbWLmNMxXBjyFoydXjWFR": {"decimals": "6", "symbol": "WTRX", "to": "coingecko#tron"}, "TSSMHYeV2uE9qYH95DqyoCuNCzEL1NvU3S": {"decimals": "18", "symbol": "SUN", "to": "coingecko#sun-token"}, "TKkeiboTkxXKJpbmVFbv4a8ov5rAfRDMf9": {"decimals": "18", "symbol": "SUN", "to": "coingecko#sun-token"}, "TAoA331n3iKDkR62kAZ4H2n3vNL7y3d8x9": {"decimals": "6", "symbol": "BCM", "to": "coingecko#bemchain"}, "TAFjULxiVgT4qWk6UZwjqwZXTSaGaqnVp4": {"decimals": "18", "symbol": "BTT", "to": "coingecko#bittorrent"}, "TKfjV9RNKJJCqPvBtK8L7Knykh7DNWvnYt": {"decimals": "6", "symbol": "WBTT", "to": "coingecko#bittorrent"}, "TZ5jA9F5zGRgi9qk9ATMu6D7wyEpnxQGJh": {"decimals": "18", "symbol": "CYFM", "to": "coingecko#cyberfm"}, "TCFLL5dx5ZJdKnWuesXxi1VPwjLVmWZZy9": {"decimals": "18", "symbol": "JST", "to": "coingecko#just"}, "TDyvndWuvX5xTBwHPYJi7J3Yq8pq8yh62h": {"decimals": "18", "symbol": "HT", "to": "coingecko#huobi-token"}, "TDBNKiYQ8yfJtT5MDP3byu7f1npJuG2DBN": {"decimals": "6", "symbol": "LUMI", "to": "coingecko#lumi-credits"}, "TFczxzPhnThNSqr5by8tvxsdCFRRz6cPNq": {"decimals": "6", "symbol": "NFT", "to": "coingecko#apenft"}, "TT8VkSkW6igkiRsV5WiJgLrsbVwY5bLLjA": {"decimals": "8", "symbol": "JM", "to": "coingecko#justmoney-2"}, "TU3kjFuhtEo42tsCBtfYUAZxoqQ4yuSLQ5": {"decimals": "18", "symbol": "STRX", "to": "coingecko#staked-trx"}, "TUpMhErZL2fhh4sVNULAbNKLokS4GjC1F4": {"decimals": "18", "symbol": "TUSD", "to": "coingecko#true-usd"}, "TYX2iy3i3793YgKU5vqKxDnLpiBMSa5EdV": {"decimals": "6", "symbol": "USTX", "to": "coingecko#upstabletoken"}, "TMz2SWatiAtZVVcH2ebpsbVtYwUPT9EdjH": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7": {"decimals": "6", "symbol": "WIN", "to": "coingecko#wink"}}, "lachain": {"0x3a898d596840c6b6b586d722bfadcc8c4761bf41": {"name": "Wrapped LA", "decimals": "18", "symbol": "wLA", "to": "coingecko#latoken"}}, "theta": {"******************************************": {"name": "theta-fuel", "decimals": "18", "symbol": "TFUEL", "to": "coingecko#theta-fuel"}, "0x4dc08b15ea0e10b96c41aec22fab934ba15c983e": {"name": "Wrapped TFuel", "decimals": "18", "symbol": "WTFUEL", "to": "coingecko#theta-fuel"}, "0x1336739b05c7ab8a526d40dcc0d04a826b5f8b03": {"name": "TDrop Token", "decimals": "18", "symbol": "TDROP", "to": "coingecko#thetadrop"}}, "zyx": {"0xc9e1aea009b0bae9141f3dc7523fb42fd48c8656": {"name": "Wrapped ZYX", "decimals": "18", "symbol": "WZYX", "to": "coingecko#zyx"}}, "ubiq": {"0x1fa6a37c64804c0d797ba6bc1955e50068fbf362": {"name": "Wrapped UBQ", "decimals": "18", "symbol": "WUBQ", "to": "coingecko#ubiq"}}, "cosmos": {"uatom": {"name": "cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}}, "terra2": {"uluna": {"name": "terra-luna-2", "decimals": "6", "symbol": "LUNA", "to": "coingecko#terra-luna-2"}, "terra1nsuqsk6kh58ulczatwev87ttq2z6r3pusulg9r24mfj2fvtzd4uq3exn26": {"name": "astroport-fi", "decimals": "6", "symbol": "ASTRO", "to": "coingecko#astroport-fi"}}, "terra": {"uluna": {"name": "terra-luna", "decimals": "6", "symbol": "LUNC", "to": "coingecko#terra-luna"}, "uusd": {"name": "uusd", "decimals": "6", "symbol": "USTC", "to": "coingecko#terrausd"}, "terra15k5r9r8dl8r7xlr29pry8a9w7sghehcnv5mgp6": {"name": "lunaverse", "decimals": "6", "symbol": "lunaverse", "to": "coingecko#lunaverse"}, "terra1dy9kmlm4anr92e42mrkjwzyvfqwz66un00rwr5": {"name": "valkyrie-protocol", "decimals": "6", "symbol": "valkyrie-protocol", "to": "coingecko#valkyrie-protocol"}, "terra12897djskt9rge8dtmm86w654g7kzckkd698608": {"name": "white whale", "decimals": "6", "symbol": "WHALE", "to": "coingecko#white-whale"}, "terra14z56l0fp2lsf86zy3hty2z47ezkhnthtr9yq76": {"name": "anchor-protocol", "decimals": "6", "symbol": "anchor-protocol", "to": "coingecko#anchor-protocol"}, "terra15gwkyepfc6xgca5t5zefzwy42uts8l2m4g40k6": {"name": "mirror-protocol", "decimals": "6", "symbol": "mirror-protocol", "to": "coingecko#mirror-protocol"}, "terra17y9qkl8dfkeg4py7n0g5407emqnemc3yqk5rup": {"name": "stader-lunax", "decimals": "6", "symbol": "stader-lunax", "to": "coingecko#stader-lunax"}, "terra1hzh9vpxhsk8253se0vv5jj6etdvxu3nv8z07zu": {"name": "anchorust", "decimals": "6", "symbol": "anchorust", "to": "coingecko#anchorust"}, "terra1kc87mu460fwkqte29rquh4hc20m54fxwtsx7gp": {"name": "bonded-luna", "decimals": "6", "symbol": "bonded-luna", "to": "coingecko#bonded-luna"}, "terra1dzhzukyezv0etz22ud940z7adyv7xgcjkahuun": {"name": "anchor-beth-token", "decimals": "6", "symbol": "anchor-beth-token", "to": "coingecko#anchor-beth-token"}, "terra1z3e2e4jpk4n0xzzwlkgcfvc95pc5ldq0xcny58": {"name": "avalanche-2", "decimals": "6", "symbol": "avalanche-2", "to": "coingecko#avalanche-2"}, "terra1vwz7t30q76s7xx6qgtxdqnu6vpr3ak3vw62ygk": {"name": "luart", "decimals": "6", "symbol": "luart", "to": "coingecko#luart"}, "terra1xj49zyqrwpv5k928jwfpfy2ha668nwdgkwlrg3": {"name": "astroport", "decimals": "6", "symbol": "astroport", "to": "coingecko#astroport"}}, "crescent": {"ubcre": {"name": "liquid-staking-crescent", "decimals": "6", "symbol": "BCRE", "to": "coingecko#liquid-staking-crescent"}, "ucre": {"name": "crescent-network", "decimals": "6", "symbol": "CRE", "to": "coingecko#crescent-network"}}, "juno": {"ujuno": {"name": "juno-network", "decimals": "6", "symbol": "JUNO", "to": "coingecko#juno-network"}}, "stargaze": {"ustars": {"name": "Stargaze", "decimals": "6", "symbol": "STARS", "to": "coingecko#stargaze"}}, "osmosis": {"uion": {"name": "ion", "decimals": "6", "symbol": "ION", "to": "coingecko#ion"}, "uosmo": {"name": "osmo", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "factory:osmo1pfyxruwvtwk00y8z06dh2lqjdj82ldvy74wzm3:WOSMO": {"name": "Wrapped Osmo", "decimals": "6", "symbol": "WOSMO", "to": "coingecko#osmosis"}}, "kujira": {"factory:kujira1n3fr5f56r2ce0s37wdvwrk98yhhq3unnxgcqus8nzsfxvllk0yxquurqty:ampKUJI": {"name": "amp<PERSON><PERSON>", "decimals": "6", "symbol": "ampKUJI", "to": "coingecko#eris-staked-kuji"}, "factory:kujira1643jxg8wasy5cfcn7xm8rd742yeazcksqlg4d7:umnta": {"name": "mnta", "decimals": "6", "symbol": "MNTA", "to": "coingecko#mantadao"}, "ukuji": {"name": "kujira", "decimals": "6", "symbol": "KUJI", "to": "coingecko#kujira"}, "factory:kujira1qk00h5atutpsv900x202pxx42npjr9thg58dnqpa72f2p7m2luase444a7:uusk": {"name": "usk", "decimals": "6", "symbol": "USK", "to": "coingecko#usk"}, "factory:kujira1swkuyt08z74n5jl7zr6hx0ru5sa2yev5v896p6:local": {"name": "local-money", "decimals": "6", "symbol": "LOCAL", "to": "coingecko#local-money"}, "kujira:factory/kujira1sc6a0347cc5q3k890jj0pf3ylx2s38rh4sza4t/ufuzn": {"name": "fuzn", "decimals": "6", "symbol": "FUZN", "to": "coingecko#fuzion"}, "factory:kujira1aaudpfr9y23lt9d45hrmskphpdfaq9ajxd3ukh:unstk": {"name": "nstk", "decimals": "6", "symbol": "NSTK", "to": "coingecko#unstake-fi"}}, "injective": {"inj": {"name": "injective-protocol", "decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol"}, "******************************************": {"name": "injective-protocol", "decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol"}, "factory:inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk:inj1q6zlut7gtkzknkk773jecujwsdkgq882akqksk": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "factory:inj14ejqjyq8um4p3xfqj74yld5waqljf88f9eneuk:inj18luqttqyckgpddndh8hvaq25d5nfwjc78m56lc": {"decimals": "18", "symbol": "HINJ", "to": "coingecko#hydro-staked-inj"}, "******************************************": {"name": "Wrapped INJ", "decimals": "18", "symbol": "WINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"name": "Wrapped INJ", "decimals": "18", "symbol": "WINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "umee": {"uumee": {"name": "<PERSON><PERSON>", "decimals": "6", "symbol": "UMEE", "to": "coingecko#umee"}}, "orai": {"orai": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "6", "symbol": "ORAI", "to": "coingecko#oraichain-token"}, "orai12hzjxfh77wl572gdzct2fxv2arxcwh6gykc7qh": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "orai1gzvndtzceqwfymu2kqhta2jn6gmzxvzqwdgvjw": {"name": "milky-token", "decimals": "6", "symbol": "milky-token", "to": "coingecko#milky-token"}, "orai1nd4r053e3kgedgld2ymen8l9yrw8xpjyaal7j5": {"name": "kawaii-islands", "decimals": "6", "symbol": "kawaii-islands", "to": "coingecko#kawaii-islands"}, "orai10ldgzued6zjp0mkqwsv2mux3ml50l97c74x8sg": {"name": "airight", "decimals": "6", "symbol": "airight", "to": "coingecko#airight"}, "orai1lus0f0rhx8s03gdllx2n6vhkmf0536dv57wfge": {"name": "oraidex", "decimals": "6", "symbol": "oraidex", "to": "coingecko#oraidex"}, "orai1065qe48g7aemju045aeyprflytemx7kecxkf5m7u5h5mphd0qlcs47pclp": {"name": "scorai", "decimals": "6", "symbol": "scorai", "to": "coingecko#scorai"}}, "comdex": {"ucmdx": {"name": "comdex", "decimals": "6", "symbol": "CMDX", "to": "coingecko#comdex"}, "ucmst": {"name": "composite", "decimals": "6", "symbol": "CMST", "to": "coingecko#composite"}}, "celo": {"******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Celo Dollar", "decimals": "18", "symbol": "cUSD", "to": "coingecko#celo-dollar"}, "******************************************": {"name": "Celo Euro", "decimals": "18", "symbol": "cEUR", "to": "coingecko#celo-euro"}, "******************************************": {"name": "Celo Real", "decimals": "18", "symbol": "cREAL", "to": "coingecko#celo-real-creal"}, "******************************************": {"name": "Moola interest bearing CELO", "decimals": "18", "symbol": "mCELO", "to": "coingecko#mcelo"}, "******************************************": {"name": "Moola interest bearing CUSD", "decimals": "18", "symbol": "mcUSD", "to": "coingecko#mceur"}, "******************************************": {"name": "Moola interest bearing CEUR", "decimals": "18", "symbol": "mCEUR", "to": "coingecko#moola-celo-dollars"}, "******************************************": {"name": "USD Coin (Wormhole)", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0x2a3684e9dc20b857375ea04235f2f7edbe818fa7": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0xb70e0a782b058bfdb0d109a3599bec1f19328e36": {"name": "Avalanche USDC.e via Allbridge", "decimals": "18", "symbol": "aaUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "asUSDC", "decimals": "18", "symbol": "asUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "cUSDC", "decimals": "18", "symbol": "cUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "cUSDT", "decimals": "18", "symbol": "cUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "Tether USD (Wormhole)", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "UST Terra", "decimals": "18", "symbol": "atUST", "to": "coingecko#terrausd"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Toucan Protocol: Nature Carbon Tonne", "decimals": "18", "symbol": "NCT", "to": "coingecko#toucan-protocol-nature-carbon-tonne"}, "******************************************": {"name": "cMCO2", "decimals": "18", "symbol": "cMCO2", "to": "coingecko#moss-carbon-credit"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "near": {"token.jumbo_exchange.near": {"name": "jumbo-exchange", "decimals": "18", "symbol": "JUMBO", "to": "coingecko#jumbo-exchange"}, "token.paras.near": {"name": "paras", "decimals": "18", "symbol": "PARAS", "to": "coingecko#paras"}, "marmaj.tkn.near": {"name": "marmaj", "decimals": "18", "symbol": "MARMAJ", "to": "coingecko#marmaj"}, "linear-protocol.near": {"name": "linear-protocol", "decimals": "24", "symbol": "LINA", "to": "coingecko#linear-protocol"}, "token.pembrock.near": {"name": "pembrock", "decimals": "18", "symbol": "pem", "to": "coingecko#pembrock"}, "token.burrow.near": {"name": "burrow", "decimals": "18", "symbol": "BRRR", "to": "coingecko#burrow"}, "token.marmaj.near": {"name": "marmaj", "decimals": "18", "symbol": "MARMAJ", "to": "coingecko#marmaj"}}, "multivac": {"******************************************": {"name": "Wrapped MultiVAC", "decimals": "18", "symbol": "WMTV", "to": "coingecko#multivac"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "tomochain": {"******************************************": {"name": "Wrapped TOMO", "decimals": "18", "symbol": "WTOMO", "to": "coingecko#tomochain"}}, "ethereumclassic": {"******************************************": {"name": "ethereum-classic", "decimals": "18", "symbol": "ETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"name": "Wrapped ETC", "decimals": "18", "symbol": "WETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"name": "Wrapped ETC", "decimals": "18", "symbol": "WETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"decimals": "6", "symbol": "USC", "to": "coingecko#usd-coin"}}, "hoo": {"******************************************": {"name": "hoo-token", "decimals": "18", "symbol": "HOO", "to": "coingecko#hoo-token"}, "******************************************": {"name": "Wrapped HOO", "decimals": "18", "symbol": "wHOO", "to": "coingecko#hoo-token"}}, "cube": {"******************************************": {"name": "cube-network", "decimals": "18", "symbol": "CUBE", "to": "coingecko#cube-network"}, "******************************************": {"name": "Wrapped CUBE", "decimals": "18", "symbol": "WCUBE", "to": "coingecko#cube-network"}, "******************************************": {"name": "Cube-Peg ETH Token", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Cube-Peg BTC Token", "decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Cube-Peg USDT Token", "decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Cube-Peg USDC Token", "decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Cube-Peg DAI Token", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "O3 Swap Token", "decimals": "18", "symbol": "O3", "to": "coingecko#o3-swap"}}, "elastos": {"******************************************": {"name": "elastos", "decimals": "18", "symbol": "ELA", "to": "coingecko#elastos"}, "******************************************": {"name": "Wrapped ELA", "decimals": "18", "symbol": "WELA", "to": "coingecko#elastos"}, "******************************************": {"name": "BUSD on ESC", "decimals": "18", "symbol": "bnbBUSD", "to": "coingecko#binance-usd"}}, "energyweb": {"******************************************": {"name": "Wrapped Energy Web Token", "decimals": "18", "symbol": "WEWT", "to": "coingecko#energy-web-token"}}, "milkomeda_a1": {"******************************************": {"name": "algorand", "decimals": "18", "symbol": "ALGO", "to": "coingecko#algorand"}, "******************************************": {"name": "Wrapped Algo", "decimals": "18", "symbol": "WALGO", "to": "coingecko#algorand"}, "******************************************": {"name": "USDC-31566704", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USDt-312769", "decimals": "6", "symbol": "USDt", "to": "coingecko#tether"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "BLUES", "decimals": "18", "symbol": "BLUES", "to": "coingecko#blueshift"}}, "ultron": {"******************************************": {"decimals": "18", "symbol": "wULX", "to": "coingecko#ultron"}, "******************************************": {"name": "ultron", "decimals": "18", "symbol": "ULX", "to": "coingecko#ultron"}, "******************************************": {"name": "wULX", "decimals": "18", "symbol": "wULX", "to": "coingecko#ultron"}, "******************************************": {"name": "Wrapped Ethereum", "decimals": "18", "symbol": "wETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped Bitcoin", "decimals": "18", "symbol": "wBTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Ultron Tether", "decimals": "6", "symbol": "uUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "Ultron USD Coin", "decimals": "6", "symbol": "uUSDC", "to": "coingecko#usd-coin"}}, "bitgert": {"******************************************": {"name": "bitrise-token", "decimals": "18", "symbol": "BRISE", "to": "coingecko#bitrise-token"}, "******************************************": {"name": "Wrapped BRISE", "decimals": "18", "symbol": "WBRISE", "to": "coingecko#bitrise-token"}, "******************************************": {"name": "LunaGens", "decimals": "18", "symbol": "LUNG", "to": "coingecko#lunagens"}, "******************************************": {"name": "<PERSON>", "decimals": "18", "symbol": "YPC", "to": "coingecko#youngparrot"}}, "echelon": {"******************************************": {"name": "echelon", "decimals": "18", "symbol": "ECH", "to": "coingecko#echelon"}, "******************************************": {"name": "Wrapped Echelon", "decimals": "18", "symbol": "WECH", "to": "coingecko#echelon"}}, "rei": {"******************************************": {"decimals": "18", "symbol": "REI", "to": "coingecko#rei-network"}, "******************************************": {"name": "Wrapped REI", "decimals": "18", "symbol": "WREI", "to": "coingecko#rei-network"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "<PERSON>lda WETH", "decimals": "18", "symbol": "fWETH", "to": "coingecko#ethereum"}}, "tombchain": {"******************************************": {"name": "Tomb", "decimals": "18", "symbol": "TOMB", "to": "coingecko#tomb"}, "******************************************": {"name": "LIF3", "decimals": "18", "symbol": "LIF3", "to": "coingecko#lif3"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FTM", "to": "coingecko#fantom"}, "******************************************": {"name": "TSHARE", "decimals": "18", "symbol": "TSHARE", "to": "coingecko#tomb-shares"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "LSHARE", "decimals": "18", "symbol": "LSHARE", "to": "coingecko#lif3-lshare"}}, "rsk": {"******************************************": {"name": "Wrapped RBTC", "decimals": "18", "symbol": "WRBTC", "to": "coingecko#rootstock"}, "******************************************": {"name": "Wrapped BTC", "decimals": "18", "symbol": "WRBTC", "to": "coingecko#rootstock"}, "******************************************": {"name": "ETHs", "decimals": "18", "symbol": "ETHs", "to": "coingecko#ethereum"}, "******************************************": {"name": "rUSDT", "decimals": "18", "symbol": "rUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "Dollar on Chain", "decimals": "18", "symbol": "DOC", "to": "coingecko#dollar-on-chain"}}, "polis": {"******************************************": {"name": "Wrapped Polis", "decimals": "18", "symbol": "WPOLIS", "to": "coingecko#polis"}}, "sui": {"0x06864a6f921804860930db6ddbe2e16acdf8504495ea7481637a1c8b9a8fe54b::cetus::CETUS": {"name": "<PERSON><PERSON>", "decimals": "9", "symbol": "CETUS", "to": "coingecko#cetus-protocol"}, "0x1d58e26e85fbf9ee8596872686da75544342487f95b1773be3c9a49ab1061b19::suia_token::SUIA_TOKEN": {"name": "SUIA", "decimals": "9", "symbol": "SUIA", "to": "coingecko#suia"}, "0xcf72ec52c0f8ddead746252481fb44ff6e8485a39b803825bde6b00d77cdb0bb::coin::COIN": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0xe4239cd951f6c53d9c41e25270d80d31f925ad1655e5ba5b543843d4a66975ee::SUIP::SUIP": {"name": "SuiPad", "decimals": "9", "symbol": "SUIP", "to": "coingecko#suipad"}, "0x94e7a8e71830d2b34b3edaa195dc24c45d142584f06fa257b73af753d766e690::celer_usdt_coin::CELER_USDT_COIN": {"decimals": "6", "symbol": "CELER_USDT_COIN", "to": "coingecko#tether"}, "0x94e7a8e71830d2b34b3edaa195dc24c45d142584f06fa257b73af753d766e690::celer_usdc_coin::CELER_USDC_COIN": {"decimals": "6", "symbol": "CELER_USDC_COIN", "to": "coingecko#usd-coin"}, "0xdbe380b13a6d0f5cdedd58de8f04625263f113b3f9db32b3e1983f49e2841676::coin::COIN": {"decimals": "8", "symbol": "WMATIC", "to": "coingecko#matic-network"}, "0xa198f3be41cda8c07b3bf3fee02263526e535d682499806979a111e88a5a8d0f::coin::COIN": {"decimals": "8", "symbol": "CELO", "to": "coingecko#celo"}, "0x027792d9fed7f9844eb4839566001bb6f6cb4804f66aa2da6fe1ee242d896881::coin::COIN": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "0x1e8b532cca6569cab9f9b9ebc73f8c13885012ade714729aa3b450e0339ac766::coin::COIN": {"decimals": "8", "symbol": "WAVAX", "to": "coingecko#wrapped-avax"}, "0x6081300950a4f1e2081580e919c210436a1bed49080502834950d31ee55a2396::coin::COIN": {"decimals": "8", "symbol": "WFTM", "to": "coingecko#fantom"}, "0x66f87084e49c38f76502d17f87d17f943f183bb94117561eb573e075fdc5ff75::coin::COIN": {"decimals": "8", "symbol": "WGLMR", "to": "coingecko#moonbeam"}, "0xe32d3ebafa42e6011b87ef1087bbc6053b499bf6f095807b9013aff5a6ecd7bb::coin::COIN": {"decimals": "6", "symbol": "USDCarb", "to": "coingecko#usd-coin"}, "0x909cba62ce96d54de25bec9502de5ca7b4f28901747bbf96b76c2e63ec5f1cba::coin::COIN": {"decimals": "8", "symbol": "USDCbnb", "to": "coingecko#usd-coin"}, "0x5d4b302506645c37ff133b98c4b50a5ae14841659738d6d733d59d0d217a93bf::coin::coin": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************000000000000000000000002::sui::sui": {"name": "SUI", "decimals": "9", "symbol": "SUI", "to": "coingecko#sui"}, "0x02::sui::sui": {"name": "SUI", "decimals": "9", "symbol": "SUI", "to": "coingecko#sui"}, "0x2::sui::sui": {"name": "SUI", "decimals": "9", "symbol": "SUI", "to": "coingecko#sui"}, "0xc060006111016b8a020ad5b33834984a437aaa7d3c74c18e09a95d48aceab08c::coin::coin": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "0xaf8cd5edc19c4512f4259f0bee101a40d41ebed738ade5874359610ef8eeced5::coin::coin": {"name": "WETH", "decimals": "8", "symbol": "WETH", "to": "coingecko#ethereum"}, "0xb231fcda8bbddb31f2ef02e6161444aec64a514e2c89279584ac9806ce9cf037::coin::coin": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0xb7844e289a8410e50fb3ca48d69eb9cf29e27d223ef90353fe1bd8e27ff8f3f8::coin::coin": {"name": "Wrapped SOL", "decimals": "8", "symbol": "SOL", "to": "coingecko#solana"}, "0xb848cce11ef3a8f62eccea6eb5b35a12c4c2b1ee1af7755d02d7bd6218e8226f::coin::coin": {"name": "Wrapped BNB", "decimals": "8", "symbol": "WBNB", "to": "coingecko#binance-coin"}, "0x6dae8ca14311574fdfe555524ea48558e3d1360d1607d1c7f98af867e3b7976c::flx::FLX": {"name": "FlowX", "decimals": "8", "symbol": "FLX", "to": "coingecko#flowx-finance"}}, "kekchain": {"******************************************": {"name": "kekchain", "decimals": "18", "symbol": "KEK", "to": "coingecko#kekchain"}, "******************************************": {"name": "Wrapped KEK", "decimals": "18", "symbol": "wKEK", "to": "coingecko#kekchain"}, "0x54bd9d8d758ac3717b37b7dc726877a23aff1b89": {"name": "Wrapped KEK", "decimals": "18", "symbol": "wKEK", "to": "coingecko#kekchain"}}, "aptos": {"0x7fd500c11216f0fe3095d0c4b8aa4d64a4e2e04f83758462f2b127255643615::thl_coin::thl": {"name": "thala", "decimals": "8", "symbol": "THL", "to": "coingecko#thala"}, "0x1::aptos_coin::AptosCoin": {"name": "aptos", "decimals": "8", "symbol": "APT", "to": "coingecko#aptos"}, "0x5e156f1207d0ebfa19a9eeff00d62a282278fb8719f4fab3a586a0a2c0fffbea::coin::T": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0xa2eda21a58856fda86451436513b867c97eecb4ba099da5775520e0f7492e852::coin::T": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "0xae478ff7d83ed072dbc5e264250e67ef58f57c99d89b447efd8a0a2e8b2be76e::coin::T": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "0xcc8a89c8dce9693d354449f1f73e60e14e347417854f029db5bc8e7454008abb::coin::T": {"name": "ethereum", "decimals": "8", "symbol": "ETH", "to": "coingecko#ethereum"}, "0xc91d826e29a3183eb3b6f6aa3a722089fdffb8e9642b94c5fcd4c48d035c0080::coin::T": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0x1000000fa32d122c18a6a31c009ce5e71674f22d06a581bb0a15575e6addadcc::usda::USDA": {"name": "usd-coin", "decimals": "6", "symbol": "USD-A", "to": "coingecko#usd-coin"}, "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDC": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDT": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::WETH": {"name": "ethereum", "decimals": "6", "symbol": "ETH", "to": "coingecko#ethereum"}, "0xdd89c0e695df0692205912fb69fc290418bed0dbe6e4573d744a6d5e6bab6c13::coin::T": {"name": "solana", "decimals": "8", "symbol": "SOL", "to": "coingecko#solana"}, "0x84d7aeef42d38a5ffc3ccef853e1b82e4958659d16a7de736a29c55fbbeb0114::staked_aptos_coin::StakedAptosCoin": {"name": "aptos", "decimals": "8", "symbol": "APT", "to": "coingecko#aptos"}, "0xd11107bdf0d6d7040c6c0bfbdecb6545191fdf13e8d8d259952f53e1713f61b5::staked_coin::StakedAptos": {"name": "aptos", "decimals": "8", "symbol": "APT", "to": "coingecko#aptos"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::BnbCoin": {"name": "binancecoin", "decimals": "8", "symbol": "BNB", "to": "coingecko#binancecoin"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::BusdCoin": {"name": "binance-usd", "decimals": "8", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::UsdcCoin": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::UsdtCoin": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::DaiCoin": {"name": "dai", "decimals": "8", "symbol": "DAI", "to": "coingecko#dai"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::WethCoin": {"name": "ethereum", "decimals": "8", "symbol": "ETH", "to": "coingecko#ethereum"}, "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::WbtcCoin": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "0xd0b4efb4be7c3508d9a26a9b5405cf9f860d0b9e5fe2f498b90e68b8d2cedd3e::aptos_launch_token::AptosLaunchToken": {"name": "aptos-launch-token", "decimals": "8", "symbol": "ALT", "to": "coingecko#aptos-launch-token"}, "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::amapt_token::AmnisApt": {"name": "amnis-apt", "decimals": "8", "symbol": "AMAPT", "to": "coingecko#amnis-aptos"}, "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::stapt_token::StakedApt": {"name": "Staked Aptos Coin", "decimals": "8", "symbol": "STAPT", "to": "coingecko#amnis-staked-aptos-coin"}}, "dogechain": {"******************************************": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "DOGE", "to": "coingecko#dogecoin"}, "******************************************": {"name": "Wrapped WDOGE", "decimals": "18", "symbol": "WWDOGE", "to": "coingecko#dogecoin"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "DC", "to": "coingecko#dogechain"}, "0xbfbb7b1d22ff521a541170cafe0c9a7f20d09c3b": {"name": "Egod", "decimals": "0", "symbol": "$SAVIOR", "to": "coingecko#egod-the-savior"}, "******************************************": {"name": "DogeTools", "decimals": "18", "symbol": "DTools", "to": "coingecko#dogetools"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "Frax", "decimals": "18", "symbol": "FRAX", "to": "coingecko#frax"}, "******************************************": {"name": "Frax Share", "decimals": "18", "symbol": "FXS", "to": "coingecko#frax-share"}, "******************************************": {"name": "BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "QuickSwap", "decimals": "18", "symbol": "QUICK", "to": "coingecko#quickswap"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Wrapped ETH", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Tether USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "FRAX", "decimals": "18", "symbol": "FRAX", "to": "coingecko#frax"}, "******************************************": {"name": "Binance Coin", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}}, "canto": {"******************************************": {"name": "canto", "decimals": "18", "symbol": "CANTO", "to": "coingecko#canto"}, "******************************************": {"name": "wCanto", "decimals": "18", "symbol": "WCANTO", "to": "coingecko#canto"}, "******************************************": {"name": "USDC via channel 0", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USDT via channel 0", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Note", "decimals": "18", "symbol": "NOTE", "to": "coingecko#note"}, "******************************************": {"name": "ETH via channel 0", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Atom via channel 2", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "******************************************": {"name": "CANTO INU", "decimals": "18", "symbol": "cINU", "to": "coingecko#canto-inu"}, "******************************************": {"name": "Velocimeter FLOW", "decimals": "18", "symbol": "flow", "to": "coingecko#velocimeter-flow"}, "******************************************": {"name": "CantoBonk", "decimals": "9", "symbol": "cbonk", "to": "coingecko#cantobonk"}, "******************************************": {"name": "MultiBTC", "decimals": "5", "symbol": "multibtc", "to": "coingecko#multibtc"}, "******************************************": {"name": "Impermax", "decimals": "18", "symbol": "ibex", "to": "coingecko#impermax-2"}}, "starknet": {"0x053c91253bc9682c04929ca02ed00b3e423f6710d2ee7e0d5ebb06f3ecf368a8": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "0x068f5c6a61780768455de69077e07e89787839bf8166decfbf92b645209c0fb8": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "0x03fe2b97c1fd336e750087d68b9b867997fd64a2661ff3ca5a7c771641e8e7ac": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "0x00da114221cb83fa859dbdb4c44beeaa0bb37c7537ad5ae66fe5e0efd20e6eb3": {"name": "dai", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "0x05574eb6b8789a91466f902c380d978e472db68170ff82a5b650b95a58ddf4ad": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "0x049d36570d4e46f48e99674bd3fcc84644ddd6b96f7c741b1562b82f9e004dc7": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "0x042b8f0484674ca266ac5d08e4ac6a3fe65bd3129795def2dca5c34ecc5f96d2": {"name": "Starknet Wrapped Staked Ether", "decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "0x04619e9ce4109590219c5263787050726be63382148538f3f936c22aa87d2fc2": {"name": "Nostra Staked STRK", "decimals": "18", "symbol": "nstSTRK", "to": "coingecko#staked-strk"}, "0x4619e9ce4109590219c5263787050726be63382148538f3f936c22aa87d2fc2": {"name": "Nostra Staked STRK", "decimals": "18", "symbol": "nstSTRK", "to": "coingecko#staked-strk"}}, "ontology_evm": {"******************************************": {"name": "Wrapped ONG", "decimals": "18", "symbol": "WONG", "to": "coingecko#ong"}}, "zeniq": {"******************************************": {"name": "bsc:******************************************", "decimals": "18", "symbol": "ZENIQ", "to": "bsc:******************************************"}, "******************************************": {"name": "Wrapped Zeniq", "decimals": "18", "symbol": "WZENIQ", "to": "bsc:******************************************"}}, "algorand": {"1": {"name": "algorand", "decimals": "6", "symbol": "ALGO", "to": "coingecko#algorand"}, "163650": {"name": "arcc", "decimals": "6", "symbol": "ARCC", "to": "coingecko#arcc"}, "312769": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "2751733": {"name": "realio-network", "decimals": "7", "symbol": "RIO", "to": "coingecko#realio-network"}, "6547014": {"name": "meld-gold", "decimals": "5", "symbol": "MCAU", "to": "coingecko#meld-gold"}, "27165954": {"name": "planetwatch", "decimals": "6", "symbol": "PLANETS", "to": "coingecko#planetwatch"}, "31566704": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "137020565": {"name": "buying", "decimals": "2", "symbol": "BUY", "to": "coingecko#buying"}, "137594422": {"name": "headline", "decimals": "6", "symbol": "HDL", "to": "coingecko#headline"}, "226701642": {"name": "yieldly", "decimals": "6", "symbol": "YLDY", "to": "coingecko#yieldly"}, "230946361": {"name": "algogems", "decimals": "6", "symbol": "GEMS", "to": "coingecko#algogems"}, "239444645": {"name": "<PERSON><PERSON><PERSON>", "decimals": "0", "symbol": "KFL", "to": "coingecko#kaafila"}, "283820866": {"name": "xfinite-entertainment-token", "decimals": "9", "symbol": "XET", "to": "coingecko#xfinite-entertainment-token"}, "287867876": {"name": "opulous", "decimals": "10", "symbol": "OPUL", "to": "coingecko#opulous"}, "297995609": {"name": "choice-coin", "decimals": "2", "symbol": "CHOICE", "to": "coingecko#choice-coin"}, "300208676": {"name": "smile-coin", "decimals": "6", "symbol": "SMILE", "to": "coingecko#smile-coin"}, "342889824": {"name": "board", "decimals": "6", "symbol": "BOARD", "to": "coingecko#board"}, "386192725": {"name": "gobtc", "decimals": "8", "symbol": "GOBTC", "to": "coingecko#gobtc"}, "386195940": {"name": "goeth", "decimals": "8", "symbol": "GOETH", "to": "coingecko#goeth"}, "403499324": {"name": "nexus-asa", "decimals": "0", "symbol": "NXS", "to": "coingecko#nexus-asa"}, "441139422": {"name": "algomint", "decimals": "6", "symbol": "GOMINT", "to": "coingecko#algomint"}, "444035862": {"name": "zone", "decimals": "6", "symbol": "ZONE", "to": "coingecko#zone"}, "463554836": {"name": "algofund", "decimals": "6", "symbol": "ALGF", "to": "coingecko#algofund"}, "465865291": {"name": "algostable", "decimals": "6", "symbol": "STBL", "to": "coingecko#algostable"}, "470842789": {"name": "defly", "decimals": "6", "symbol": "DEFLY", "to": "coingecko#defly"}, "511484048": {"name": "algostake", "decimals": "2", "symbol": "STKE", "to": "coingecko#algostake"}, "559219992": {"name": "octorand", "decimals": "6", "symbol": "OCTO", "to": "coingecko#octorand"}, "571576867": {"name": "cosmic-champs", "decimals": "6", "symbol": "COSG", "to": "coingecko#cosmic-champs"}, "607591690": {"name": "glitter-finance", "decimals": "6", "symbol": "XGLI", "to": "coingecko#glitter-finance"}, "657291910": {"name": "carbon-credit", "decimals": "0", "symbol": "CCT", "to": "coingecko#carbon-credit"}, "663905154": {"name": "bnext-b3x", "decimals": "6", "symbol": "B3X", "to": "coingecko#bnext-b3x"}, "684649988": {"name": "gard", "decimals": "6", "symbol": "GARD", "to": "coingecko#gard"}, "692085161": {"name": "algodao", "decimals": "6", "symbol": "ADAO", "to": "coingecko#algodao"}, "700965019": {"name": "vestige", "decimals": "6", "symbol": "VEST", "to": "coingecko#vestige"}, "744665252": {"name": "ptokens-btc-2", "decimals": "8", "symbol": "PBTC", "to": "coingecko#ptokens-btc-2"}, "792313023": {"name": "wrapped-sol", "decimals": "9", "symbol": "WSOL", "to": "coingecko#wrapped-sol"}, "793124631": {"name": "governance-algo", "decimals": "6", "symbol": "GALGO", "to": "coingecko#governance-algo"}, "841126810": {"name": "algostable", "decimals": "6", "symbol": "STBL", "to": "coingecko#algostable"}, "871930188": {"name": "bring", "decimals": "6", "symbol": "ANOIR", "to": "coingecko#bring"}}, "shiden": {"******************************************": {"name": "Wrapped <PERSON>den", "decimals": "18", "symbol": "WSDN", "to": "coingecko#shiden"}, "******************************************": {"decimals": "18", "symbol": "SDN", "to": "coingecko#shiden"}, "******************************************": {"name": "EmiDAO Token", "decimals": "18", "symbol": "ESW", "to": "coingecko#emiswap"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Binance-Peg BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Standard", "decimals": "18", "symbol": "STND", "to": "coingecko#standard-protocol"}}, "tezos": {"KT1PnUZCp3u2KzWr93pn4DD7HAJnm3rWVrgn": {"decimals": "6", "symbol": "WTZ", "to": "coingecko#tezos"}, "tezos": {"name": "tezos", "decimals": "0", "symbol": "XTZ", "to": "coingecko#tezos"}, "KT1UpeXdK6AJbX58GJ92pLZVCucn2DR8Nu4b": {"name": "tezos", "decimals": "6", "symbol": "XTZ", "to": "coingecko#tezos"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-19": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-17": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-11": {"name": "matic-network", "decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-10": {"name": "chainlink", "decimals": "18", "symbol": "LINK", "to": "coingecko#chainlink"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-1": {"name": "binance-usd", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-20": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-5": {"name": "dai", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ": {"name": "aave", "decimals": "18", "symbol": "AAVE", "to": "coingecko#aave"}, "KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-18": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "KT1XRPEPXbZK25r3Htzp2o1x7xdMMmfocKNW-2": {"name": "wrapped-bitcoin", "decimals": "12", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-0": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-1": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-2": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-3": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-4": {"name": "matic-network", "decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-5": {"name": "chainlink", "decimals": "18", "symbol": "LINK", "to": "coingecko#chainlink"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-6": {"name": "dai", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-7": {"name": "binance-usd", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "hpb": {"******************************************": {"name": "Wrapped HPB", "decimals": "18", "symbol": "WHPB", "to": "coingecko#high-performance-blockchain"}}, "godwoken": {"******************************************": {"name": "Wrapped CKB", "decimals": "18", "symbol": "WCKB", "to": "coingecko#nervos-network"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "godwoken_v1": {"******************************************": {"name": "Wrapped CKB", "decimals": "18", "symbol": "WCKB", "to": "coingecko#nervos-network"}, "******************************************": {"name": "pCKB", "decimals": "18", "symbol": "pCKB", "to": "coingecko#nervos-network"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "waves": {"5UYBPpq4WoU5n4MwpFkgJnW3Fq4B1u3ukpK33ik4QerR": {"name": "binancecoin", "decimals": "8", "symbol": "BNB", "to": "coingecko#binancecoin"}, "DG2xFkPdDwKUoBkzGAhQtLpSGzfXLiCYPEzeKH2Ad24p": {"name": "neutrino", "decimals": "6", "symbol": "USDN", "to": "coingecko#neutrino"}, "Atqv59EYzjFGuitKVnMRk6H8FukjoV3ktPorbEys25on": {"name": "waves-exchange", "decimals": "8", "symbol": "WX", "to": "coingecko#waves-exchange"}, "474jTeYx2r2Va35794tCScAXWJG9hU2HcgxzMowaZUnu": {"name": "ethereum", "decimals": "8", "symbol": "ETH", "to": "coingecko#ethereum"}, "********************************************": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "8LQW8f7P5d5PZM7GtZEBgaqRPGSzS3DfPuiXrURJ4AJS": {"name": "bitcoin", "decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "WAVES": {"name": "waves", "decimals": "8", "symbol": "WAVES", "to": "coingecko#waves"}, "2Fh9m3dNQXycHdnytEaETN3P1gDT7ij5U4HjMqQBeaqN": {"name": "ftx-token", "decimals": "8", "symbol": "FTT", "to": "coingecko#ftx-token"}, "4GZH8rk5vDmMXJ81Xqfm3ovFaczqMnQ11r7aELiNxWBV": {"name": "fantom", "decimals": "8", "symbol": "FTM", "to": "coingecko#fantom"}, "********************************************": {"name": "curve-dao-token", "decimals": "8", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "GVxGPBtgVWMW1wHiFnfaCakbJ6sKgZgowJgW5Dqrd7JH": {"name": "shiba-inu", "decimals": "2", "symbol": "SHIB", "to": "coingecko#shiba-inu"}, "HcHacFH51pY91zjJa3ZiUVWBww54LnsL4EP3s7hVGo9L": {"name": "matic-network", "decimals": "8", "symbol": "MATIC", "to": "coingecko#matic-network"}, "4YmM7mj3Av4DPvpNpbtK4jHbpzYDcZuY6UUnYpqTbzLj": {"name": "uniswap", "decimals": "8", "symbol": "UNI", "to": "coingecko#uniswap"}, "6QUVF8nVVVvM7do7JT2eJ5o5ehnZgXUg13ysiB9JiQrZ": {"name": "terra-luna", "decimals": "8", "symbol": "LUNA", "to": "coingecko#terra-luna"}, "7TMu26hAs7B2oW6c5sfx45KSZT7GQA3TZNYuCav8Dcqt": {"name": "aave", "decimals": "8", "symbol": "AAVE", "to": "coingecko#aave"}, "E4rss7qLUcawCvD2uMrbLeTMPGkX15kS3okWCbUhLNKL": {"name": "maker", "decimals": "8", "symbol": "MKR", "to": "coingecko#maker"}, "HLckRcg7hJ3Syf3PrGftFijKqQMJipf81WY3fwvHCJbe": {"name": "crypto-com-chain", "decimals": "8", "symbol": "CRO", "to": "coingecko#crypto-com-chain"}, "8zUYbdB8Q6mDhpcXYv52ji8ycfj4SDX4gJXS7YY3dA4R": {"name": "dai", "decimals": "6", "symbol": "DAI", "to": "coingecko#dai"}, "8DLiYZjo3UUaRBTHU7Ayoqg4ihwb6YH1AfXrrhdjQ7K1": {"name": "binance-usd", "decimals": "6", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "47cyc68FWJszCWEwMWVsD9CadjS2M1XtgANuRGbEW8UH": {"name": "cosmos", "decimals": "8", "symbol": "ATOM", "to": "coingecko#cosmos"}, "2bbGhKo5C31iEiB4CwGuqMYwjD7gCA9eXmm51fe2v8vT": {"name": "chainlink", "decimals": "8", "symbol": "LINK", "to": "coingecko#chainlink"}, "BLRxWVJWaVuR2CsCoTvTw2bDZ3sQLeTbCofcJv7dP5J4": {"name": "yearn-finance", "decimals": "8", "symbol": "YFI", "to": "coingecko#yearn-finance"}, "A1uMqYTzBdakuSNDv7CruWXP8mRZ4EkHwmip2RCauyZH": {"name": "the-graph", "decimals": "8", "symbol": "GRT", "to": "coingecko#the-graph"}, "2thtesXvnVMcCnih9iZbJL3d2NQZMfzENJo8YFj6r5jU": {"name": "terrausd", "decimals": "6", "symbol": "UST", "to": "coingecko#terrausd"}, "2GBgdhqMjUPqreqPziXvZFSmDiQVrxNuGxR1z7ZVsm4Z": {"name": "a<PERSON><PERSON>n", "decimals": "8", "symbol": "APE", "to": "coingecko#apecoin"}, "Aug9ccbPApb1hxXSue8fHuvbyMf1FV1BYBtLUuS5LZnU": {"name": "decentraland", "decimals": "8", "symbol": "MANA", "to": "coingecko#decentraland"}, "ATQdLbehsMrmHZLNFhUm1r6s14NBT5JCFcSJGpaMrkAr": {"name": "axie-infinity", "decimals": "8", "symbol": "AXS", "to": "coingecko#axie-infinity"}, "8YyrMfuBdZ5gtMWkynLTveRvGb6LJ4Aff9rpz46UUMW": {"name": "the-sandbox", "decimals": "8", "symbol": "SAND", "to": "coingecko#the-sandbox"}, "EfwRV6MuUCGgAUchdsF4dDFnSpKrDW3UYshdaDy4VBeB": {"name": "enjincoin", "decimals": "8", "symbol": "ENJ", "to": "coingecko#enjincoin"}, "5zoDNRdwVXwe7DveruJGxuJnqo7SYhveDeKb8ggAuC34": {"name": "wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "DSbbhLsSTeDg5Lsiufk2Aneh3DjVqJuPr2M9uU1gwy5p": {"name": "vires-finance", "decimals": "8", "symbol": "VIRES", "to": "coingecko#vires-finance"}, "4LHHvYGNKJUg5hj65aGD5vgScvCBmLpdRFtjokvCjSL8": {"name": "waves-enterprise", "decimals": "8", "symbol": "WEST", "to": "coingecko#waves-enterprise"}, "6nSpVyNH7yM69eg446wrQR94ipbbcmZMU1ENPwanC97g": {"name": "neutrino-system-base-token", "decimals": "8", "symbol": "NSBT", "to": "coingecko#neutrino-system-base-token"}, "Ehie5xYpeN8op1Cctc6aGUrqx8jq3jtf1DSjXDbfm7aT": {"name": "swop", "decimals": "6", "symbol": "SWOP", "to": "coingecko#swop"}, "7LMV3s1J4dKpMQZqge5sKYoFkZRLojnnU49aerqos4yg": {"name": "enno-cash", "decimals": "8", "symbol": "ENNO", "to": "coingecko#enno-cash"}, "9sQutD5HnRvjM1uui5cVC4w9xkMPAfYEV8ymug3Mon2Y": {"name": "signaturechain", "decimals": "8", "symbol": "SIGN", "to": "coingecko#signaturechain"}, "DHgwrRvVyqJsepd32YbBqUeDH4GJ1N984X8QoekjgH8J": {"name": "waves-community-token", "decimals": "2", "symbol": "WCT", "to": "coingecko#waves-community-token"}, "HZk1mbfuJpmxU1Fs4AX5MWLVYtctsNcg6e2C6VKqK8zk": {"name": "litecoin", "decimals": "8", "symbol": "LTC", "to": "coingecko#litecoin"}, "6XtHjpXbs9RRJP2Sr9GUyVqzACcby9TkThHXnjVC5CDJ": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "YiNbofFzC17jEHHCMwrRcpy9MrrjabMMLZxg8g5xmf7": {"decimals": "8", "symbol": "WAVES", "to": "coingecko#waves"}, "********************************************": {"decimals": "8", "symbol": "ETH", "to": "coingecko#ethereum"}, "2Fge5HEBRD3XTeg7Xg3FW5yiB9HVJFQtMXiWMQo72Up6": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "66a1br3BrkoaJgP7yEar9hJcSTvJPoH6PYBLqscXcMGo": {"decimals": "8", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "QGDb5VHmjUMfHPAvRJ4g36nmU5qYByYyYzReJN71nad": {"decimals": "8", "symbol": "LINK", "to": "coingecko#chainlink"}, "2x8CpnEDNw2nsuyvEptEmEbVrkxh9regRDNrqTWThJTZ": {"decimals": "8", "symbol": "MKR", "to": "coingecko#maker"}, "78ePJGDo2H6cZUDYsAMzqxe2iSRNgz4QBnYYg58ZxdgH": {"decimals": "8", "symbol": "UNI", "to": "coingecko#uniswap"}, "AhGJvjtYmRG2pKwXvTh8N6sX1M2wNTpkjxaWKQfzJe7q": {"decimals": "8", "symbol": "MATIC", "to": "coingecko#matic-network"}, "EW1uGLVo21Wd9i2Rhq8o4VKDTCQTGCGXE8DqayHGrLg8": {"decimals": "8", "symbol": "BBTC", "to": "coingecko#binance-bitcoin"}, "FmsB2B21fVVetWvZm7Q48cC2Bvs2hEZtft49TBn3guV1": {"decimals": "8", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "5Ga8eJdR5PoBWLC2xaq6F6PAGCM5hWVNhuyycgsNn4jR": {"decimals": "6", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "Fwvk46RZ4iBg4L9GzwjQ7jwVsEScn4aPD32V6wftTLHQ": {"decimals": "6", "symbol": "TRX", "to": "coingecko#tron"}, "C1iWsKGqLwjHUndiQ7iXpdmPum9PeCDFfyXBdJJosDRS": {"decimals": "8", "symbol": "EGG", "to": "coingecko#waves-ducks"}, "GAzAEjApmjMYZKPzri2g2VUXNvTiQGF7KDYZFFsP3AEq": {"decimals": "8", "symbol": "PETE", "to": "coingecko#pete"}, "2thsACuHmzDMuNezPM32wg9a3BwUzBWDeSKakgz3cw21": {"decimals": "8", "symbol": "PWR", "to": "coingecko#power-token"}, "HEB8Qaw9xrWpWs8tHsiATYGBWDBtP2S7kcPALrMu43AS": {"decimals": "8", "symbol": "PUZZLE", "to": "coingecko#puzzle-swap"}, "GjwAHMjqWzYR4LgoNy91CxUKAGJN79h2hseZoae4nU8t": {"decimals": "8", "symbol": "UNIT0", "to": "coingecko#unit0"}}, "songbird": {"******************************************": {"name": "Wrapped Songbird", "decimals": "18", "symbol": "WSGB", "to": "coingecko#songbird"}, "******************************************": {"name": "Experimental Finance Token", "decimals": "18", "symbol": "EXFI", "to": "coingecko#flare-finance"}, "******************************************": {"name": "Canary Dollar", "decimals": "18", "symbol": "CAND", "to": "coingecko#canary-dollar"}, "******************************************": {"decimals": "6", "symbol": "exUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "exETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "exXDC", "to": "coingecko#xdce-crowd-sale"}}, "energi": {"******************************************": {"name": "Wrapped NRG", "decimals": "18", "symbol": "WNRG", "to": "coingecko#energi"}, "******************************************": {"name": "Wrapped NRG", "decimals": "18", "symbol": "WNRG", "to": "coingecko#energi"}}, "flare": {"******************************************": {"decimals": "18", "symbol": "FLR", "to": "coingecko#flare-networks"}, "******************************************": {"name": "Wrapped Flare", "decimals": "18", "symbol": "WFLR", "to": "coingecko#flare-networks"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}}, "nahmii": {"******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "15", "symbol": "NII", "to": "coingecko#nahmii"}}, "curio": {"******************************************": {"name": "Curio Governance Token", "decimals": "18", "symbol": "CGT", "to": "coingecko#curio-governance"}}, "gochain": {"******************************************": {"name": "Wrapped GO", "decimals": "18", "symbol": "WGO", "to": "coingecko#gochain"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Fast.Finance", "decimals": "18", "symbol": "FAST", "to": "coingecko#fast-finance"}}, "dfk": {"******************************************": {"decimals": "18", "symbol": "JEWEL", "to": "coingecko#defi-kingdoms"}, "******************************************": {"name": "Wrapped JEWEL", "decimals": "18", "symbol": "WJEWEL", "to": "coingecko#defi-kingdoms"}, "******************************************": {"name": "xJEWEL", "decimals": "18", "symbol": "xJEWEL", "to": "coingecko#xjewel"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "FTM", "to": "coingecko#fantom"}}, "smartbch": {"******************************************": {"name": "bitcoin-cash", "decimals": "18", "symbol": "BCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"name": "Wrapped BCH", "decimals": "18", "symbol": "WBCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"name": "LAWTOKEN", "decimals": "18", "symbol": "LAW", "to": "coingecko#law"}, "******************************************": {"name": "flexUSD", "decimals": "18", "symbol": "flexUSD", "to": "coingecko#flex-usd"}, "******************************************": {"name": "Tropical Finance Token", "decimals": "18", "symbol": "DAIQUIRI", "to": "coingecko#tropical-finance"}, "******************************************": {"name": "BlockNG-Peg USDT Token", "decimals": "18", "symbol": "bcUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "CashCats", "decimals": "2", "symbol": "$CATS", "to": "coingecko#cashcats"}}, "palm": {"******************************************": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "ERC20 tradable version of ETH on the Palm network", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "syscoin": {"******************************************": {"name": "Wrapped SYS", "decimals": "18", "symbol": "WSYS", "to": "coingecko#syscoin"}}, "vision": {"******************************************": {"name": "Vision USDT", "decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Vision BTC", "decimals": "18", "symbol": "VBTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Vision ETH", "decimals": "18", "symbol": "VETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Vision BNB", "decimals": "18", "symbol": "VBNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Wrapped Vision", "decimals": "6", "symbol": "WVS", "to": "coingecko#vision-metaverse"}}, "kava": {"erc20:tether:usdt": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "bnb": {"decimals": "8", "symbol": "BNB", "to": "coingecko#binancecoin"}, "btcb": {"decimals": "8", "symbol": "BTCB", "to": "coingecko#bitcoin"}, "busd": {"decimals": "8", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "hard": {"decimals": "6", "symbol": "HARD", "to": "coingecko#kava-lend"}, "swp": {"decimals": "6", "symbol": "SWP", "to": "coingecko#kava-swap"}, "ukava": {"decimals": "6", "symbol": "KAVA", "to": "coingecko#kava"}, "bkava": {"decimals": "6", "symbol": "BKAVA", "to": "coingecko#kava"}, "xrpb": {"decimals": "8", "symbol": "XRP", "to": "coingecko#ripple"}, "usdx": {"decimals": "6", "symbol": "USDX", "to": "coingecko#usdx"}, "hbtc": {"decimals": "8", "symbol": "HBTC", "to": "coingecko#huobi-btc"}, "erc20:axelar:usdc": {"decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#usd-coin"}, "erc20:axelar:wbtc": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "erc20:multichain:usdc": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "erc20:multichain:usdt": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "erc20:multichain:wbtc": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "erc20:multichain:dai": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "kava", "decimals": "18", "symbol": "KAVA", "to": "coingecko#kava"}, "******************************************": {"name": "BLUES", "decimals": "18", "symbol": "BLUES", "to": "coingecko#blueshift"}, "******************************************": {"name": "TAROT", "decimals": "18", "symbol": "TAROT", "to": "coingecko#tarot"}, "******************************************": {"name": "LQDR", "decimals": "18", "symbol": "LQDR", "to": "coingecko#liquiddriver"}, "******************************************": {"name": "Wrapped Kava", "decimals": "18", "symbol": "WKAVA", "to": "coingecko#kava"}, "******************************************": {"name": "Binance-Peg BUSD Token", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Knit USD", "decimals": "18", "symbol": "USDk", "to": "ethereum:******************************************"}, "******************************************": {"name": "Binance", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Axelar Wrapped USDC", "decimals": "6", "symbol": "axlUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Axelar Wrapped USDT", "decimals": "6", "symbol": "axlUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "<PERSON><PERSON> Wrapped DAI", "decimals": "18", "symbol": "axlDAI", "to": "coingecko#dai"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#bitcoin"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#kava"}, "******************************************": {"name": "SushiToken", "decimals": "18", "symbol": "SUSHI", "to": "coingecko#sushi"}, "******************************************": {"name": "Knit Finance", "decimals": "18", "symbol": "KFT", "to": "ethereum:******************************************"}, "******************************************": {"name": "Knit Bitrise Token", "decimals": "18", "symbol": "kBRISE", "to": "coingecko#bitrise-token"}, "******************************************": {"name": "PREMIO", "decimals": "18", "symbol": "PREMIO", "to": "coingecko#premio"}, "******************************************": {"name": "TetherUSDt", "decimals": "6", "symbol": "USDt", "to": "coingecko#tether"}}, "sx": {"******************************************": {"name": "Wrapped SX", "decimals": "18", "symbol": "WSX", "to": "coingecko#sx-network"}, "******************************************": {"name": "Wrapped ETH", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Wrapped MATIC", "decimals": "18", "symbol": "WMATIC", "to": "coingecko#matic-network"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "SharkToken", "decimals": "18", "symbol": "SHARK", "to": "coingecko#shark"}}, "meter": {"******************************************": {"name": "meter", "decimals": "18", "symbol": "MTR", "to": "coingecko#meter"}, "******************************************": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "MTRG", "to": "coingecko#meter"}, "******************************************": {"name": "Wrapped MTR", "decimals": "18", "symbol": "WMTR", "to": "coingecko#meter"}, "******************************************": {"name": "Wrapped USDC from Ethereum", "decimals": "6", "symbol": "USDC.eth", "to": "coingecko#usd-coin"}, "******************************************": {"name": "<PERSON>er", "decimals": "18", "symbol": "MTR", "to": "coingecko#meter-stable"}, "******************************************": {"name": "Wrapped BUSD from BSC", "decimals": "18", "symbol": "BUSD.bsc", "to": "coingecko#binance-usd"}, "******************************************": {"name": "TFUEL from Theta wrapped by Meter Passport", "decimals": "18", "symbol": "TFUEL", "to": "coingecko#theta-fuel"}, "******************************************": {"name": "Wrapped USDT from Ethereum on Meter", "decimals": "6", "symbol": "USDT.eth", "to": "coingecko#tether"}}, "callisto": {"******************************************": {"name": "Wrapped CLO", "decimals": "18", "symbol": "WCLO", "to": "coingecko#callisto"}, "******************************************": {"name": "Bulls USD", "decimals": "18", "symbol": "BUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "ccETC", "decimals": "18", "symbol": "ccETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"name": "ccETH", "decimals": "18", "symbol": "ccETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "ccBNB", "decimals": "18", "symbol": "ccBNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "SOY Finance token", "decimals": "18", "symbol": "SOY", "to": "coingecko#soy-finance"}}, "thundercore": {"******************************************": {"decimals": "8", "symbol": "TT-WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "TT-BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Wrapped Thunder Token", "decimals": "18", "symbol": "WTT", "to": "coingecko#thunder-token"}, "******************************************": {"name": "TT-USDT", "decimals": "6", "symbol": "TT-USDT", "to": "coingecko#tether"}, "******************************************": {"name": "TT-ETH", "decimals": "18", "symbol": "TT-ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "TT-USDC", "decimals": "6", "symbol": "TT-USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "ThunderCore Binance USD", "decimals": "18", "symbol": "TT-BUSD", "to": "coingecko#binance-usd"}}, "conflux": {"******************************************": {"name": "Wrapped Conflux", "decimals": "18", "symbol": "WCFX", "to": "coingecko#conflux-token"}, "******************************************": {"name": "Wrapped BTC", "decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "Ethereum Token", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "USDC", "decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Nucleon Governance Token", "decimals": "18", "symbol": "NUT", "to": "coingecko#nucleon-space"}, "******************************************": {"name": "Tether USD", "decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}}, "muuchain": {"******************************************": {"name": "Wrapped MUU", "decimals": "18", "symbol": "WMUU", "to": "coingecko#muu-inu"}}, "iotex": {"******************************************": {"name": "iotex", "decimals": "18", "symbol": "IOTX", "to": "coingecko#iotex"}, "******************************************": {"name": "Wrapped IOTX", "decimals": "18", "symbol": "WIOTX", "to": "coingecko#iotex"}, "******************************************": {"name": "MCN.VENTURES", "decimals": "18", "symbol": "MCN", "to": "coingecko#mcn-ventures"}, "******************************************": {"name": "Elk", "decimals": "18", "symbol": "ELK", "to": "coingecko#elk-finance"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "ioUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "6", "symbol": "USDT-matic", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "ioUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Matic USD Coin", "decimals": "6", "symbol": "USDC-matic", "to": "coingecko#usd-coin"}, "******************************************": {"name": "BSC Tether USD", "decimals": "18", "symbol": "USDT_b", "to": "coingecko#tether"}, "******************************************": {"name": "BSC USD Coin", "decimals": "18", "symbol": "USDC_b", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD-bsc", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "ioBUSD", "to": "coingecko#iobusd"}, "******************************************": {"name": "Matic DAI stablecoin", "decimals": "18", "symbol": "DAI-matic", "to": "coingecko#dai"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "ioDAI", "to": "coingecko#dai"}, "******************************************": {"name": "xDollar Interverse Money", "decimals": "18", "symbol": "XIM", "to": "coingecko#xdollar-stablecoin"}, "******************************************": {"name": "Cyclone Protocol", "decimals": "18", "symbol": "CYC", "to": "coingecko#cyclone-protocol"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "ioETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "ioWBTC", "to": "coingecko#wrapped-btcoin"}, "******************************************": {"name": "Game Fantasy Token", "decimals": "18", "symbol": "GFT", "to": "coingecko#game-fantasy-token"}, "******************************************": {"name": "Metanyx", "decimals": "18", "symbol": "METX", "to": "coingecko#metanyx"}, "******************************************": {"name": "iMagicToken", "decimals": "18", "symbol": "iMAGIC", "to": "coingecko#imagictoken"}, "******************************************": {"name": "Binance Coin", "decimals": "18", "symbol": "BNB-bsc", "to": "coingecko#binancecoin"}, "******************************************": {"name": "ZoomToken", "decimals": "18", "symbol": "ZOOM", "to": "coingecko#zoomswap"}, "******************************************": {"name": "Wrapped Matic", "decimals": "18", "symbol": "WMATIC", "to": "coingecko#matic-network"}, "******************************************": {"name": "Parrot <PERSON>", "decimals": "18", "symbol": "iPEGG", "to": "coingecko#parrot-egg"}, "0x99b2b0efb56e62e36960c20cd5ca8ec6abd5557a": {"name": "Crosschain IOTX", "decimals": "18", "symbol": "CIOTX", "to": "coingecko#crosschain-iotx"}}, "tlchain": {"0x422b6cdf97c750a0edcddc39c88f25379e59e96e": {"name": "Wrapped TLC", "decimals": "18", "symbol": "WTLC", "to": "coingecko#tlchain"}}, "ronin": {"******************************************": {"name": "Wrapped Ronin", "decimals": "18", "symbol": "WRON", "to": "coingecko#ronin"}, "******************************************": {"name": "Axie Infinity Shard", "decimals": "18", "symbol": "AXS", "to": "coingecko#axie-infinity"}, "******************************************": {"name": "Smooth Love Potion", "decimals": "0", "symbol": "SLP", "to": "coingecko#smooth-love-potion"}, "******************************************": {"name": "<PERSON><PERSON> Wrapped Ether", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "arbitrum_nova": {"******************************************": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "WETH", "decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "ethpow": {"******************************************": {"name": "ethereum-pow-iou", "decimals": "18", "symbol": "ETHW", "to": "coingecko#ethereum-pow-iou"}, "******************************************": {"name": "Wrapped Ether", "decimals": "18", "symbol": "ETHW", "to": "coingecko#ethereum-pow-iou"}, "******************************************": {"name": "Billion Happiness", "decimals": "18", "symbol": "BHC", "to": "coingecko#billionhappiness"}, "******************************************": {"name": "Wrapped ETHPoW", "decimals": "18", "symbol": "WETHW", "to": "coingecko#ethereum-pow-iou"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Tether USD", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"name": "Binance USD", "decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"name": "Wrapped BTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"name": "Dai Stablecoin", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}}, "xdc": {"******************************************": {"name": "xdce-crowd-sale", "decimals": "18", "symbol": "XDC", "to": "coingecko#xdce-crowd-sale"}, "******************************************": {"name": "USDT@xinfin", "decimals": "6", "symbol": "xUSDT", "to": "coingecko#tether"}, "******************************************": {"name": "Wrapped XDC", "decimals": "18", "symbol": "WXDC", "to": "coingecko#xdce-crowd-sale"}, "******************************************": {"name": "XSP Token", "decimals": "18", "symbol": "XSP", "to": "coingecko#xswap-protocol"}, "******************************************": {"name": "STORX", "decimals": "18", "symbol": "SRX", "to": "coingecko#storx"}, "******************************************": {"name": "NOTA", "decimals": "6", "symbol": "USNOTA", "to": "coingecko#nota"}, "******************************************": {"name": "Plugin", "decimals": "18", "symbol": "PLI", "to": "coingecko#plugin"}}, "elrond": {"******************************************": {"name": "elrond-erd-2", "decimals": "18", "symbol": "EGLD", "to": "coingecko#elrond-erd-2"}}, "ton": {"EQBynBO23ywHy_CgarY9NK9FTz0yDsG82PtcbSTQgGoXwiuA": {"decimals": "6", "symbol": "jUSDT", "to": "coingecko#tether"}, "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "EQB-MPwrd1G6WKNkLz_VnV6WqBDd142KMQv-g1O-8QUA3728": {"decimals": "6", "symbol": "jUSDC", "to": "coingecko#usd-coin"}, "EQDcBkGHmC4pTf34x3Gm05XvepO5w60DNxZ-XT4I6-UGG5L5": {"decimals": "8", "symbol": "jWBTC", "to": "coingecko#wrapped-bitcoin"}, "EQDo_ZJyQ_YqBzBwbVpMmhbhIddKtRP99HugZJ14aFscxi7B": {"decimals": "18", "symbol": "jDAI", "to": "coingecko#dai"}, "EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c": {"name": "the-open-network", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}, "******************************************": {"name": "the-open-network", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}, "Ef8zMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzM0vF": {"name": "the-open-network", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}, "EQDQoc5M3Bh8eWFephi9bClhevelbZZvWhkqdo80XuY_0qXv": {"name": "the-open-network", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}, "EQBPAVa6fjMigxsnHF33UQ3auufVrg2Z8lBZTY9R-isfjIFr": {"name": "the-open-network", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}, "EQAW42HutyDem98Be1f27PoXobghh81umTQ-cGgaKVmRLS7-": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "EQC61IQRl0_la95t27xhIpjxZt32vl1QQVF2UgTNuvD18W-4": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "EQC_1YoM8RBixN95lz7odcF3Vrkc_N8Ne7gQi7Abtlet_Efi": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "dexit": {"******************************************": {"name": "dexit-finance", "decimals": "18", "symbol": "DXT", "to": "coingecko#dexit-finance"}, "******************************************": {"name": "Wrapped DXT", "decimals": "18", "symbol": "WDXT", "to": "coingecko#dexit-finance"}}, "empire": {"******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "ibc": {"A7A5C44AA67317F1B3FFB27BAFC89C9CC04F61306F6F834F89A74B8F82D252A1": {"decimals": "6", "symbol": "SOMM", "to": "coingecko#sommelier"}, "FE98AAD68F02F03565E9FA39A5E627946699B2B07115889ED812D8BA639576A9": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "590CE97A3681BC2058FED1F69B613040209DF3F17B7BD31DFFB8671C4D2CD99B": {"decimals": "8", "symbol": "SHD", "to": "coingecko#shade-protocol"}, "6CDD4663F2F09CD62285E2D45891FC149A3568E316CE3EBBE201A71A78A69388": {"decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "926432AE1C5FA4F857B36D970BE7774C7472079506820B857B75C5DE041DD7A3": {"decimals": "6", "symbol": "JKL", "to": "coingecko#jackal-protocol"}, "B9E4FD154C92D3A23BEA029906C4C5FF2FE74CB7E3A058290B77197A263CF88B": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "13C5990F84FA5D472E1F8BB1BAAEA8774DA5F24128EC02B119107AD21FB52A61": {"decimals": "18", "symbol": "axlWETH", "to": "coingecko#ethereum"}, "31ED168F5E93D988FCF223B1298113ACA818DB7BED8F7B73764C5C9FAC293609": {"name": "lion-dao", "decimals": 6, "symbol": "ROAR", "to": "coingecko#lion-dao"}, "53796B3762678CD80784A7DD426EB45B89C024BE3D45224CC83FDE3DED7DA0A1": {"name": "fanfury", "decimals": 6, "symbol": "FURY", "to": "coingecko#fanfury"}, "D20559F0071F4BFDFF519D0C12B77AFE2A4481D44214BD92808B0C36B1E223C9": {"name": "graviton", "decimals": 6, "symbol": "GRAV", "to": "coingecko#graviton"}, "21F041CFE99994E0D027D0C5F72A9EB6224CBCAF5A6AD5DDB75F67A781D46C68": {"name": "white-whale", "decimals": 6, "symbol": "WHALE", "to": "coingecko#white-whale"}, "7023F9629A70F8112764D959D04F52EA3115A0AED3CEE59694799FD8C91A97FA": {"name": "akash-network", "decimals": 6, "symbol": "AKT", "to": "coingecko#akash-network"}, "A64467480BBE4CCFC3CF7E25AD1446AA9BDBD4F5BCB9EF6038B83D6964C784E6": {"name": "matic-network", "decimals": 18, "symbol": "wMATIC", "to": "coingecko#matic-network"}, "950993C6DA64F5A60A48D65A18CAB2D8190DE2DC1B861E70E8B03C61F7D5FBDC": {"name": "archway", "decimals": 18, "symbol": "ARCH", "to": "coingecko#archway"}, "96179F5B44CCC15E03AB43D7118E714B4D5CE8F187F7D8A60F2A514299761EA9": {"name": "arbitrum", "decimals": 18, "symbol": "ARB", "to": "coingecko#arbitrum"}, "5A3DCF59BC9EC5C0BB7AA0CA0279FC2BB126640CB8B8F704F7BC2DC42495041B": {"name": "injective-protocol", "decimals": 18, "symbol": "INJ", "to": "coingecko#injective-protocol"}, "A2146858B5E3CFE759E32F47CA54591F8E27FAEDFF731D30B448E5AB25CA8EC5": {"name": "bittensor", "decimals": 9, "symbol": "wTAO", "to": "coingecko#bittensor"}, "B572E6F30E7C33D78A50D8B4E973A9C118C30F848DF31A95FAA5E4C7450A8BD0": {"name": "wrapped-steth", "decimals": 18, "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "301DAF9CB0A9E247CD478533EF0E21F48FF8118C4A51F77C8BC3EB70E5566DBC": {"name": "wrapped-bitcoin", "decimals": 8, "symbol": "wBTC", "to": "coingecko#wrapped-bitcoin"}, "C140AFD542AE77BD7DCC83F13FDD8C5E5BB8C4929785E6EC2F4C636F98F17901": {"decimals": "6", "symbol": "stATOM", "to": "coingecko#stride-staked-atom"}, "EA1D43981D5C9A1C4AAEA9C23BB1D4FA126BA9BC7020A25E0AE4AA841EA25DC5": {"decimals": "18", "symbol": "axlWETH", "to": "coingecko#ethereum"}, "D1542AA8762DB13087D8364F3EA6509FD6F009A34F00426AF9E4F9FA85CBBF1F": {"decimals": "8", "symbol": "axlWBTC", "to": "coingecko#wrapped-bitcoin"}, "F082B65C88E4B6D5EF1DB243CDA1D331D002759E938A0F5CD3FFDC5D53B3E349": {"decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#axlusdc"}, "C0E66D1C81D8AAF0E6896E05190FDFBC222367148F86AC3EA679C28327A763CD": {"decimals": "6", "symbol": "AXL", "to": "coingecko#axelar"}, "0B1D3A663E6D41C7B1CFC78DB41C829A3B8329D396B47737445AFF5E0E9DA125": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "AKT", "to": "coingecko#akash-network"}, "223420B0E8CF9CC47BCAB816AB3A20AE162EED27C1177F4B2BC270C83E11AD8D": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "88178A7DF538EF97AC4585C949304C3DE4FC04AA91F8F1372281E921D3F44A0D": {"name": "JUNO", "decimals": "18", "symbol": "JUNO", "to": "coingecko#juno-network"}, "452372B8214E22C625E98958C5EDFB939C48E6590E40B711E9D30206EF8EDC9B": {"name": "Terra-2", "decimals": "18", "symbol": "Luna", "to": "coingecko#terra-luna-2"}, "A05909C8EEE180BAECC46D91CB29229204C35100F3BD8F0C1F6FF5D72A741FB6": {"decimals": "6", "symbol": "STKATOM", "to": "coingecko#stkatom"}, "0306D6B66EAA2EDBB7EAD23C0EC9DDFC69BB43E80B398035E90FBCFEF3FD1A87": {"name": "Stride Staked Atom", "decimals": "6", "symbol": "STATOM", "to": "coingecko#stride-staked-atom"}, "F97BDCE220CCB52139C73066E36C45EC7EDCEEF1DAFF891A34F4FBA195A2E6E8": {"name": "Stride Staked Osmo", "decimals": "6", "symbol": "STOSMO", "to": "coingecko#stride-staked-osmo"}, "CA1261224952DF089EFD363D8DBB30A8AB6D8CD181E60EE9E68E432F8DE14FE3": {"name": "inter-stable-token", "decimals": "6", "symbol": "IST", "to": "coingecko#inter-stable-token"}, "5A76568E079A31FA12165E4559BA9F1E9D4C97F9C2060B538C84DCD503815E30": {"name": "injective-protocol", "decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol"}, "6F4968A73F90CF7DE6394BF937D6DF7C7D162D74D839C13F53B41157D315E05F": {"name": "terrausd", "decimals": "6", "symbol": "UST", "to": "coingecko#terrausd"}, "C4CFF46FD6DE35CA4CF4CE031E643C8FDC9BA4B99AE598E9B0ED98FE3A2319F9": {"name": "cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "A2E2EEC9057A4A1C2C0A6A4C78B0239118DF5F278830F50B4A6BDD7A66506B78": {"name": "cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "9C4DCD21B48231D0BC2AC3D1B74A864746B37E4292694C93C617324250D002FC": {"name": "osmosis", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "4627AD2524E3E0523047E35BB76CC90E37D9D57ACF14F0FCBCEB2480705F3CB8": {"name": "terra-luna", "decimals": "6", "symbol": "LUNA", "to": "coingecko#terra-luna"}, "C950356239AD2A205DE09FDF066B1F9FF19A7CA7145EA48A5B19B76EE47E52F7": {"name": "graviton", "decimals": "6", "symbol": "GRAV", "to": "coingecko#graviton"}, "DBF5FA602C46392DE9F4796A0FC7D02F3A8A3D32CA3FAA50B761D4AA6F619E95": {"name": "gravity-bridge-weth", "decimals": "18", "symbol": "G-WETH", "to": "coingecko#gravity-bridge-weth"}, "CD01034D6749F20AAC5330EF4FD8B8CA7C40F7527AB8C4A302FBD2A070852EE1": {"name": "gravity-bridge-usdc", "decimals": "6", "symbol": "G-USDC", "to": "coingecko#gravity-bridge-usdc"}, "F1806958CA98757B91C3FA1573ECECD24F6FA3804F074A6977658914A49E65A3": {"name": "a<PERSON><PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "AXLWETH", "to": "coingecko#axlweth"}, "BFF0D3805B50D93E2FA5C0B2DDF7E0B30A631076CD80BC12A48C0E95404B4A41": {"name": "axlusdc", "decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#axlusdc"}, "EAC38D55372F38F1AFD68DF7FE9EF762DCF69F26520643CF3F9D292A738D8034": {"name": "axlusdc", "decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#axlusdc"}, "11F940BCDFD7CFBFD7EDA13F25DA95D308286D441209D780C9863FD4271514EB": {"name": "agoric", "decimals": "6", "symbol": "BLD", "to": "coingecko#agoric"}, "295548A78785A1007F232DE286149A6FF512F180AF5657780FC89C009E2C348F": {"name": "axlusdc", "decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#axlusdc"}, "D189335C6E4A68B513C10AB227BF1C1D38C746766278BA3EEB4FB14124F1D858": {"name": "axlusdc", "decimals": "6", "symbol": "AXLUSDC", "to": "coingecko#axlusdc"}, "27394FB092D2ECCD56123C74F36E4C1F926001CEADA9CA97EA622B25F41E5EB2": {"name": "cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "961FA3E54F5DCCA639F37A7C45F7BBE41815579EF1513B5AFBEFCFEB8F256352": {"name": "cosmos", "decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "799FDD409719A1122586A629AE8FCA17380351A51C1F47A80A1B8E7F2A491098": {"name": "akash-network", "decimals": "6", "symbol": "AKT", "to": "coingecko#akash-network"}, "B8AF5D92165F35AB31F3FC7C7B444B9D240760FA5D406C49D24862BD0284E395": {"name": "terra-luna", "decimals": "6", "symbol": "LUNC", "to": "coingecko#terra-luna"}, "DA59C009A0B3B95E0549E6BF7B075C8239285989FF457A8EDDBB56F10B2A6986": {"name": "terra-luna-2", "decimals": "6", "symbol": "LUNA", "to": "coingecko#terra-luna-2"}, "0471F1C4E7AFD3F07702BEF6DC365268D64570F7C1FDC98EA6098DD6DE59817B": {"name": "osmosis", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "47BD209179859CDE4A2806763D7189B6E6FE13A17880FE2B42DE1E6C1E329E23": {"name": "osmosis", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "EA7DF7F779C7F14E07172E5713E07356B55F01496CA649DDE46CF8FBF1A8466D": {"name": "osmosis", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "F3AA7EF362EC5E791FE78A0F4CCC69FEE1F9A7485EB1A8CAB3F6601C00522F10": {"name": "evmos", "decimals": "18", "symbol": "EVMOS", "to": "coingecko#evmos"}, "EFF323CC632EC4F747C61BCE238A758EFDB7699C3226565F7C20DA06509D59A5": {"name": "juno-network", "decimals": "6", "symbol": "JUNO", "to": "coingecko#juno-network"}, "167E3D88D71B7D2F6308D3EF93FC3DD51932B2D9672D72B71418F61CBC5F5717": {"name": "juno-network", "decimals": "6", "symbol": "JUNO", "to": "coingecko#juno-network"}, "B448C0CA358B958301D328CCDC5D5AD642FC30A6D3AE106FF721DB315F3DDE5C": {"name": "terrausd", "decimals": "6", "symbol": "UST", "to": "coingecko#terrausd"}, "A358D7F19237777AF6D8AD0E0F53268F8B18AE8A53ED318095C14D6D7F3B2DB5": {"name": "secret", "decimals": "6", "symbol": "SCRT", "to": "coingecko#secret"}, "1B38805B1C75352B28169284F96DF56BDEBD9E8FAC005BDCC8CF0378C82AA8E7": {"name": "ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "4F393C3FCA4190C0A6756CE7F6D897D5D1BE57D6CCB80D0BC87393566A7B6602": {"name": "stargaze", "decimals": "6", "symbol": "STARS", "to": "coingecko#stargaze"}, "987C17B11ABC2B20019178ACE62929FE9840202CE79498E29FE8E5CB02B7C0A4": {"name": "stargaze", "decimals": "6", "symbol": "STARS", "to": "coingecko#stargaze"}, "3607EB5B5E64DD1C0E12E07F077FF470D5BC4706AFCBC98FE1BA960E5AE4CE07": {"name": "comdex", "decimals": "6", "symbol": "CMDX", "to": "coingecko#comdex"}, "EA3E1640F9B1532AB129A571203A0B9F789A7F14BB66E350DCBFA18E1A1931F0": {"name": "comdex", "decimals": "6", "symbol": "CMDX", "to": "coingecko#comdex"}, "9EC8A1701813BB7B73BFED2496009ABB2C8BF187E6CDFA788D77F68E08BC05CD": {"name": "composite", "decimals": "6", "symbol": "CMST", "to": "coingecko#composite"}, "F2331645B9683116188EF36FC04A809C28BD36B54555E8705A37146D0182F045": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "CBF67A2BCF6CAE343FDF251E510C8E18C361FC02B23430C121116E0811835DEF": {"name": "tether", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "B3504E092456BA618CC28AC671A71FB08C6CA0FD0BE7C8A5B5A3E2DD933CC9E4": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "E1616E7C19EA474C565737709A628D6F8A23FF9D3E9A7A6871306CF5E0A5341E": {"name": "usd-coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "903A61A498756EA560B85A85132D3AEE21B5DEDD41213725D22ABF276EA6945E": {"name": "axelar", "decimals": "6", "symbol": "AXL", "to": "coingecko#axelar"}, "C01154C2547F4CB10A985EA78E7CD4BA891C1504360703A37E1D7043F06B5E1F": {"name": "axelar", "decimals": "6", "symbol": "AXL", "to": "coingecko#axelar"}, "E7807A46C0B7B44B350DA58F51F278881B863EC4DCA94635DAB39E52C30766CB": {"name": "chihuahua-token", "decimals": "6", "symbol": "HUA", "to": "coingecko#chihuahua-token"}, "16618B7F7AC551F48C057A13F4CA5503693FBFF507719A85BC6876B8BD75F821": {"name": "evmos", "decimals": "18", "symbol": "EVMOS", "to": "coingecko#evmos"}, "B786E7CBBF026F6F15A8DA248E0F18C62A0F7A70CB2DABD9239398C8B5150ABB": {"name": "persistence", "decimals": "6", "symbol": "XPRT", "to": "coingecko#persistence"}, "624BA9DD171915A2B9EA70F69638B2CEA179959850C1A586F6C485498F29EDD4": {"name": "polkadot", "decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "3FDD002A3A4019B05A33D324B2F29748E77AF501BEA5C96D1F28B2D6755F9F25": {"name": "stride", "decimals": "6", "symbol": "STRD", "to": "coingecko#stride"}, "B37E4D9FB5B30F3E1E20A4B2DE2A005E584C5C822C44527546556AE2470B4539": {"name": "polkadot", "decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "DADB399E742FCEE71853E98225D13E44E90292852CD0033DF5CABAB96F80B833": {"name": "binancecoin", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "04CE51E6E02243E565AE676DD60336E48D455F8AAD0611FA0299A22FDAC448D6": {"name": "Ethereum", "decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "153B97FE395140EAAA2D7CAC537AF1804AEC5F0595CBC5F1603094018D158C0C": {"name": "WBTC", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "49788C29CD84E08D25CA7BE960BC1F61E88FEFC6333F58557D236D693398466A": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "C86651B4D30C1739BF8B061E36F4473A0C9D60380B52D01E56A6874037A5D060": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "B4DCACF7753C05040AF0A7BF2B583402C4B8C9B0A86FCECE32EF63CB7F0A46B3": {"name": "gravity-bridge-paxg", "decimals": "18", "symbol": "gPAXG", "to": "coingecko#pax-gold"}, "6B49A789937D4E50BF01F0F50DDEDF5C1103EDF01306B7021BDF23BDE65D99BA": {"name": "Stride Staked Osmo", "decimals": "6", "symbol": "STOSMO", "to": "coingecko#stride-staked-osmo"}, "ED07A3391A112B175915CD8FAF43A2DA8E4790EDE12566649D0C2F97716B8518": {"name": "Osmosis", "decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "C8B3026C2844D204F3A49F91058DC947F01F2FC36AFF17850FFC8701504BDDEE": {"name": "Stride Staked Atom", "decimals": "6", "symbol": "STATOM", "to": "coingecko#stride-staked-atom"}, "8184469200C5E667794375F5B0EC3B9ABB6FF79082941BF5D0F8FF59FEBA862E": {"name": "BNB", "decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "FAEC929814E0D916C019EB4B8BE58360EC3B6AB6A2B3185CB1EA0B54832DEE68": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "AB2652E28C9961D13CFF2EC5F53C7E2965C8D0CF37A33CC97D8EEF6212F315C8": {"name": "<PERSON><PERSON>t", "decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "D1CB2804AA5EB64D503F28E8DF3552F8A4D642802B66EFE50DE432B755F37AE1": {"name": "<PERSON><PERSON>", "decimals": "6", "symbol": "AXL", "to": "coingecko#axelar"}, "BA460328D9ABA27E643A924071FDB3836E4CE8084C6D2380F25EFAB85CF8EB11": {"name": "Inter Stable Token", "decimals": "6", "symbol": "IST", "to": "coingecko#inter-stable-token"}, "DBF6ABDB5F3D4267C039967515594453F4A31007FD838A566F563A01D2C2FB80": {"name": "mars-protocol", "decimals": "6", "symbol": "MARS", "to": "coingecko#mars-protocol-a7fcbcfb-fd61-4017-92f0-7ee9f9cc6da3"}, "10A0DD366A472B098DFD93FBAE62E65DA387F314872C4AD3AE43185154738D8D": {"name": "mars-protocol", "decimals": "6", "symbol": "MARS", "to": "coingecko#mars-protocol"}, "764D1629980B02BAFF3D25BEE4FB1E0C5E350AFA252FDF66E68E10D2179A826A": {"name": "okt-chain", "decimals": "18", "symbol": "OKT", "to": "coingecko#okt-chain"}, "E32F6485CDAE995FC492010770936119D0BF90F5226C9AED727988DF03B5F569": {"name": "stkAtom", "decimals": "6", "symbol": "STKATOM", "to": "coingecko#stkatom"}, "D64F87FAE0B35C1954DD7921BA7A2939705DE77CBF72B8002F2E3552EDE4DE52": {"name": "stride-staked-<PERSON><PERSON>", "decimals": "6", "symbol": "STATOM", "to": "coingecko#stride-staked-atom"}, "C814F0B662234E24248AE3B2FE2C1B54BBAF12934B757F6E7BC5AEC119963895": {"name": "juno-network", "decimals": "6", "symbol": "JUNO", "to": "coingecko#juno-network"}, "531804EA11DBF043843E20CC6498B5C4C2B19726A33891CAB919545DD3D612E0": {"name": "jak<PERSON>", "decimals": "6", "symbol": "JKL", "to": "coingecko#jackal-protocol"}, "2AB7D3FAE795E5DD7ACDE80D215DE076AE4DF64367E2D4B801B595F504002A9E": {"name": "akash-network", "decimals": "6", "symbol": "AKT", "to": "coingecko#akash-network"}, "5EDC10227E40B52D893F5A26C107E2A338C4A643615C10B356DC62B5F4FE1DB1": {"name": "crypto-com-chain", "decimals": "8", "symbol": "CRO", "to": "coingecko#cronos"}, "DA8D591FFA8836FDF3AD0F9F8AF4EAA77D9D4F23DA3D10DFD1FC3B9A3644B26D": {"name": "<PERSON><PERSON>", "decimals": "6", "symbol": "UMEE", "to": "coingecko#umee"}, "4DF678EF85F1FD3CEFF41429E14B22E5D7730B00230688E6783AF06112415620": {"name": "canto", "decimals": "18", "symbol": "CANTO", "to": "coingecko#canto"}, "7C80C41847B0A6BC37D66C13B2EDAF716217BE894FEC3FF11E76FE02D4E7BAA3": {"name": "persistence", "decimals": "6", "symbol": "XPRT", "to": "coingecko#persistence"}, "0634D0993744740D675AD01E81156EAC945AEAAE17C074918DC7FF52F41B263E": {"name": "axelar", "decimals": "6", "symbol": "AXL", "to": "coingecko#axelar"}, "1620B95419728A7DEF482DEB9462DD6B9FA120BCB49CCCF74209A56AB9835E59": {"name": "gravity-bridge-wrapped-bitcoin", "decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "4171A6F59F8A708D807E03B43FA0E16EC7041C189557B7A8E519757424367D41": {"name": "stride-staked-EVMOS", "decimals": "18", "symbol": "STEVMOS", "to": "coingecko#stride-staked-evmos"}, "61DF64ADF65230540C14C63D64897BE08A3DC9A516A91425913F01240E2F432F": {"name": "comdex", "decimals": "6", "symbol": "CMDX", "to": "coingecko#comdex"}, "73E97EB411B29C6F989C35D277D1A7FC65083572F102AC6BD101884EE9FB2C9F": {"name": "evmos", "decimals": "18", "symbol": "EVMOS", "to": "coingecko#evmos"}, "A39C7A94E7955808E135BD13B36DFF8DF722F46E2B90CEB4F88488C0F4B5ABE5": {"name": "stride-staked-UMEE", "decimals": "6", "symbol": "STUMEE", "to": "coingecko#umee"}, "49D820DFDE9F885D7081725A58202ABA2F465CAEE4AFBC683DFB79A8E013E83E": {"name": "injective-protocol", "decimals": 18, "symbol": "INJ", "to": "coingecko#injective-protocol"}, "E67ADA2204A941CD4743E70771BA08E24885E1ADD6FD140CE1F9E0FEBB68C6B2": {"name": "fantom", "decimals": 18, "symbol": "wFTM", "to": "coingecko#fantom"}, "FC59D6840A41252352263CEA2B832BB86D68D03CBA194263CB9F3C15946796FB": {"name": "chainlink", "decimals": 18, "symbol": "LINK", "to": "coingecko#chainlink"}, "E5CA126979E2FFB4C70C072F8094D07ECF27773B37623AD2BF7582AD0726F0F3": {"name": "wrapped-solana", "decimals": 8, "symbol": "whSOL", "to": "coingecko#solana"}}, "bitindi": {"******************************************": {"name": "<PERSON><PERSON><PERSON>", "decimals": "18", "symbol": "BNI", "to": "coingecko#bitindi-chain"}, "0x15E162205421dc3A47b15A1A740FbF5EAbB77921": {"name": "Wrapped Bitindi", "decimals": "18", "symbol": "BNI", "to": "coingecko#bitindi-chain"}}, "enuls": {"******************************************": {"name": "Nuls", "decimals": "18", "symbol": "NULS", "to": "coingecko#nuls"}, "******************************************": {"name": "Wrapped NULS", "decimals": "18", "symbol": "WNULS", "to": "coingecko#nuls"}}, "europa": {"******************************************": {"name": "Paxos Dollar", "decimals": "18", "symbol": "USDP", "to": "ethereum:******************************************"}, "******************************************": {"name": "WBTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "SKALE", "decimals": "18", "symbol": "SKL", "to": "ethereum:******************************************"}, "******************************************": {"name": "<PERSON><PERSON>", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"name": "RUBY", "decimals": "18", "symbol": "RUBY", "to": "ethereum:******************************************"}, "******************************************": {"name": "DAI", "decimals": "18", "symbol": "DAI", "to": "ethereum:******************************************"}}, "core": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "Core", "decimals": "18", "symbol": "CORE", "to": "coingecko#coredaoorg"}, "******************************************": {"name": "Wrapped Core", "decimals": "18", "symbol": "WCORE", "to": "core:******************************************"}, "******************************************": {"name": "Wrapped Core", "decimals": "18", "symbol": "WCORE", "to": "core:******************************************"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ZEEP", "to": "coingecko#zeepr"}, "******************************************": {"decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.b", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "BTCB", "to": "coingecko#binance-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.m", "to": "coingecko#solv-btc"}}, "rpg": {"******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"name": "RPG", "decimals": "18", "symbol": "RPG", "to": "coingecko#RPGdaoorg"}, "******************************************": {"name": "Wrapped RPG", "decimals": "18", "symbol": "WRPG", "to": "rpg:******************************************"}, "******************************************": {"name": "Wrapped RPG", "decimals": "18", "symbol": "WRPG", "to": "rpg:******************************************"}}, "crab": {"******************************************": {"name": "CRAB", "decimals": "18", "symbol": "CRAB", "to": "coingecko#darwinia-network-native-token"}, "******************************************": {"name": "Wrapped CRAB", "decimals": "18", "symbol": "WCRAB", "to": "crab:******************************************"}}, "lung": {"******************************************": {"name": "LUNG", "decimals": "18", "symbol": "LUNG", "to": "coingecko#lunagens"}, "******************************************": {"name": "Wrapped LUNG", "decimals": "18", "symbol": "WLUNG", "to": "lung:******************************************"}}, "stacks": {"SP3Y2ZSH8P7D50B0VBTSX11S7XSG24M1VB9YFQA4K.token-aeusdc::aeUSDC": {"decimals": "6", "symbol": "aeUSDC", "to": "coingecko#usd-coin"}, "SP3Y2ZSH8P7D50B0VBTSX11S7XSG24M1VB9YFQA4K.token-aeusdc": {"decimals": "6", "symbol": "aeUSDC", "to": "coingecko#usd-coin"}, "SP3DX3H4FEYZJZ586MFBS25ZW3HZDMEW92260R2PR.Wrapped-Bitcoin::wrapped-bitcoin": {"decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "SP3DX3H4FEYZJZ586MFBS25ZW3HZDMEW92260R2PR.Wrapped-Bitcoin": {"decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "SP1Z92MPDQEWZXW36VX71Q25HKF5K2EPCJ304F275.wstx-token-v4a": {"decimals": "6", "symbol": "STX", "to": "coingecko#blockstack"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.age000-governance-token::alex": {"decimals": "8", "symbol": "ALEX", "to": "coingecko#alexgo"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.auto-alex::auto-alex": {"decimals": "8", "symbol": "ALEX", "to": "coingecko#alexgo"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.age000-governance-token": {"decimals": "8", "symbol": "ALEX", "to": "coingecko#alexgo"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.auto-alex": {"decimals": "8", "symbol": "ALEX", "to": "coingecko#alexgo"}, "******************************************": {"name": "STX", "decimals": "6", "symbol": "STX", "to": "coingecko#blockstack"}, "SPSCWDV3RKV5ZRN1FQD84YE1NQFEDJ9R1F4DYQ11.newyorkcitycoin-token-v2::newyorkcitycoin": {"name": "nycccoin", "decimals": "6", "symbol": "NYC", "to": "coingecko#nycccoin"}, "SP2H8PY27SEZ03MWRKS5XABZYQN17ETGQS3527SA5.newyorkcitycoin-token::newyorkcitycoin": {"name": "nycccoin", "decimals": "0", "symbol": "NYC", "to": "coingecko#nycccoin"}, "SP466FNC0P7JWTNM2R9T199QRZN1MYEDTAR0KP27.miamicoin-token::miamicoin": {"name": "miamicoin", "decimals": "0", "symbol": "MIA", "to": "coingecko#miamicoin"}, "SP1H1733V5MZ3SZ9XRW9FKYGEZT0JDGEB8Y634C7R.miamicoin-token-v2::miamicoin": {"name": "miamicoin", "decimals": "6", "symbol": "MIA", "to": "coingecko#miamicoin"}, "SP4SZE494VC2YC5JYG7AYFQ44F5Q4PYV7DVMDPBG.ststx-token::ststx": {"decimals": "6", "symbol": "stSTX", "to": "coingecko#stacking-dao"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.token-susdt::bridged-usdt": {"decimals": "8", "symbol": "USDT", "to": "coingecko#tether"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.token-abtc::bridged-btc": {"decimals": "8", "symbol": "aBTC", "to": "coingecko#bitcoin"}, "SP2C2YFP12AJZB4MABJBAJ55XECVS7E4PMMZ89YZR.wstx-token::wstx": {"decimals": "6", "symbol": "wSTX", "to": "coingecko#blockstack"}, "SP1Y5YSTAHZ88XYK1VPDH24GY0HPX5J4JECTMY4A1.wstx": {"decimals": "6", "symbol": "WSTX", "to": "coingecko#blockstack"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.token-abtc": {"decimals": "8", "symbol": "aBTC", "to": "coingecko#bitcoin"}, "SP2XD7417HGPRTREMKF748VNEQPDRR0RMANB7X1NK.token-abtc::bridged-btc": {"decimals": "8", "symbol": "aBTC", "to": "coingecko#xlink-bridged-btc-stacks"}, "SP102V8P0F7JX67ARQ77WEA3D3CFB5XW39REDT0AM.token-alex::alex": {"decimals": "8", "symbol": "ALEX", "to": "coingecko#alexgo"}}, "map": {"******************************************": {"name": "MAP Protocol", "decimals": "18", "symbol": "MAP", "to": "coingecko#marcopolo"}, "******************************************": {"name": "Wrapped MAPO", "decimals": "18", "symbol": "WMAPO", "to": "coingecko#marcopolo"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "18", "symbol": "ROUP", "to": "coingecko#roup"}, "******************************************": {"decimals": "18", "symbol": "WROUP", "to": "coingecko#roup"}, "******************************************": {"decimals": "18", "symbol": "ORD", "to": "coingecko#ordinals"}, "******************************************": {"decimals": "18", "symbol": "WORD", "to": "coingecko#ordinals"}, "******************************************": {"decimals": "18", "symbol": "RATS", "to": "coingecko#rats"}, "******************************************": {"decimals": "18", "symbol": "WRATS", "to": "coingecko#rats"}, "******************************************": {"decimals": "18", "symbol": "CSAS", "to": "coingecko#comsat"}, "******************************************": {"decimals": "18", "symbol": "WCSAS", "to": "coingecko#comsat"}, "******************************************": {"decimals": "18", "symbol": "BTCS", "to": "coingecko#btcs"}, "******************************************": {"decimals": "18", "symbol": "WBTCS", "to": "coingecko#btcs"}, "******************************************": {"decimals": "18", "symbol": "MMSS", "to": "coingecko#mmss"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "MBTC", "to": "coingecko#merlin-chain-bridged-wrapped-btc-merlin"}, "******************************************": {"decimals": "18", "symbol": "iUSD", "to": "coingecko#izumi-bond-usd"}}, "loop": {"******************************************": {"name": "LOOP", "decimals": "18", "symbol": "LOOP", "to": "coingecko#loopnetwork"}, "******************************************": {"name": "Wrapped LOOP", "decimals": "18", "symbol": "WLOOP", "to": "coingecko#loopnetwork"}}, "bone": {"******************************************": {"name": "BONE", "decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"name": "Wrapped BONE", "decimals": "18", "symbol": "WBONE", "to": "coingecko#bone-shibaswap"}}, "era": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"name": "WBTC", "decimals": "8", "symbol": "WBTC", "to": "ethereum:******************************************"}, "******************************************": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "6", "symbol": "USD+", "to": "coingecko#usd"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "iUSD", "to": "coingecko#izumi-bond-usd"}, "******************************************": {"name": "ETH", "decimals": "18", "symbol": "ETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "Wrapped ETH", "decimals": "18", "symbol": "WETH", "to": "ethereum:******************************************"}, "******************************************": {"name": "USDC", "decimals": "6", "symbol": "USDC", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}}, "meta": {"******************************************": {"name": "META", "decimals": "18", "symbol": "META", "to": "coingecko#metadium"}}, "oas": {"******************************************": {"decimals": "18", "symbol": "OAS", "to": "coingecko#oasys"}, "******************************************": {"decimals": "18", "symbol": "WOAS", "to": "coingecko#oasys"}}, "eos_evm": {"******************************************": {"decimals": "18", "symbol": "EOS", "to": "coingecko#eos"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "grove": {"******************************************": {"decimals": "18", "symbol": "GRV", "to": "coingecko#grove"}, "******************************************": {"decimals": "18", "symbol": "WGRV", "to": "coingecko#grove"}}, "polygon_zkevm": {"******************************************": {"decimals": "18", "symbol": "stMATIC", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "sfrxETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "frxETH", "to": "ethereum:******************************************"}}, "pulse": {"******************************************": {"decimals": "8", "symbol": "HEX", "to": "coingecko#hex-pulsechain"}, "******************************************": {"decimals": "18", "symbol": "PLSX", "to": "coingecko#pulsex"}, "******************************************": {"decimals": "18", "symbol": "PLS", "to": "coingecko#pulsechain"}, "******************************************": {"decimals": "18", "symbol": "WPLS", "to": "coingecko#pulsechain"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "xpla": {"axpla": {"decimals": "18", "symbol": "XPLA", "to": "coingecko#xpla"}}, "fxcore": {"FX": {"decimals": "18", "symbol": "FX", "to": "coingecko#fx-coin"}, "usdt": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "onus": {"******************************************": {"decimals": "18", "symbol": "ONUS", "to": "coingecko#onus"}, "******************************************": {"decimals": "18", "symbol": "WONUS", "to": "coingecko#onus"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "PHUX", "to": "coingecko#phux-governance-token"}, "******************************************": {"decimals": "18", "symbol": "PHIAT", "to": "pulse:******************************************"}, "******************************************": {"decimals": "18", "symbol": "wePHIAT", "to": "coingecko#ephiat"}, "******************************************": {"decimals": "8", "symbol": "pHEX", "to": "coingecko#hex-pulsechain"}, "******************************************": {"decimals": "8", "symbol": "weHEX", "to": "coingecko#hex-pulsechain"}, "******************************************": {"decimals": "9", "symbol": "pHDRN", "to": "coingecko#hedron"}, "******************************************": {"decimals": "9", "symbol": "wHDRN", "to": "coingecko#hedron"}, "******************************************": {"decimals": "18", "symbol": "PLSX", "to": "coingecko#pulsex"}}, "fusion": {"******************************************": {"decimals": "18", "symbol": "FSN", "to": "coingecko#fsn"}}, "neon_evm": {"0xb14760c064a1b9eaf9ec5a8a421971e40a51b59c": {"decimals": "18", "symbol": "WNEON", "to": "coingecko#neon"}, "******************************************": {"decimals": "18", "symbol": "WNEON", "to": "coingecko#neon"}}, "tenet": {"******************************************": {"decimals": "18", "symbol": "WTENET", "to": "coingecko#tenet-1b000f7b-59cb-4e06-89ce-d62b32d362b9"}}, "mantle": {"******************************************": {"decimals": "18", "symbol": "MNT", "to": "coingecko#mantle"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WMNT", "to": "coingecko#mantle"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "USDY", "to": "ethereum:******************************************"}}, "linea": {"******************************************": {"decimals": "18", "symbol": "STG", "to": "coingecko#stargate-finance"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "AVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}}, "base": {"******************************************": {"decimals": "18", "symbol": "STG", "to": "coingecko#stargate-finance"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USD+", "to": "coingecko#usd"}, "******************************************": {"decimals": "18", "symbol": "DAI+", "to": "coingecko#overnight-dai"}, "******************************************": {"decimals": "6", "symbol": "wUSD+", "to": "coingecko#usd"}}, "aura": {"uaura": {"decimals": "6", "symbol": "AURA", "to": "coingecko#aura-network"}, "aura1fgfnuru6krewgt9zzu7nzercz007928uzrw2t2tl6hl3ec50me2q3ankr2": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "op_bnb": {"******************************************": {"decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}}, "sei": {"usei": {"decimals": "6", "symbol": "SEI", "to": "coingecko#sei-network"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:4tLQqCLaoKKfNFuPjA9o39YbKUwhR1F8N29Tz3hEbfP2": {"decimals": "8", "symbol": "WETH", "to": "coingecko#ethereum"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:9hJDBDaxqQQhF5HhaPUykeLncBa38XQ5uoNxN3tPQu5r": {"decimals": "8", "symbol": "WETHar", "to": "coingecko#ethereum"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:9tTHn18vLnfyBvrQaia6N15zwrfRCAebZDshoPZ39ahN": {"decimals": "8", "symbol": "WETHbs", "to": "coingecko#ethereum"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:********************************************": {"decimals": "6", "symbol": "USDCop", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:DUVFMY2neJdL8aE4d3stcpttDDm5aoyfGyVvm29iA9Yp": {"decimals": "6", "symbol": "USDCpo", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:Hq4tuDzhRBnxw3tFA5n6M52NVMVcC19XggbyDiJKCD6H": {"decimals": "6", "symbol": "USDCet", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:9fELvUhFo6yWL34ZaLgPbCPzdk9MD1tAzMycgH45qShH": {"decimals": "6", "symbol": "USDCso", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:7edDfnf4mku8So3t4Do215GNHwASEwCWrdhM5GqD51xZ": {"decimals": "6", "symbol": "USDCar", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:********************************************": {"decimals": "8", "symbol": "USDCbs", "to": "coingecko#usd-coin"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:HktfLoADCk9mnjv7XJiN4YXK9ayE6xinLzt8wzcsR2rY": {"decimals": "6", "symbol": "USDTet", "to": "coingecko#tether"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:871jbn9unTavWsAe83f2Ma9GJWSv6BKsyWYLiQ6z3Pva": {"decimals": "8", "symbol": "USDTbs", "to": "coingecko#tether"}, "factory:sei189adguawugk3e55zn63z8r9ll29xrjwca636ra7v7gxuzn98sxyqwzt47l:7omXa4gryZ5NiBmLep7JsTtTtANCVKXwT9vbN91aS1br": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "wSEI", "to": "coingecko#sei-network"}, "******************************************": {"decimals": "18", "symbol": "SEI", "to": "coingecko#sei-network"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "ISEI", "to": "coingecko#silo-staked-sei"}, "******************************************": {"decimals": "18", "symbol": "SEI", "to": "coingecko#sei-network"}}, "archway": {"aarch": {"decimals": "18", "symbol": "ARCH", "to": "coingecko#archway"}, "archway1yjdgfut7jkq5xwzyp6p5hs7hdkmszn34zkhun6mglu3falq3yh8sdkaj7j": {"decimals": "6", "symbol": "xJKL", "to": "coingecko#jackal-protocol"}, "archway1m273xq2fjmn993jm4kft5c49w2c70yfv5zypt3d92cqp4n5faefqqkuf0l": {"decimals": "6", "symbol": "xATOM", "to": "coingecko#cosmos"}, "archway1cutfh7m87cyq5qgqqw49f289qha7vhsg6wtr6rl5fvm28ulnl9ssg0vk0n": {"decimals": "18", "symbol": "xARCH", "to": "coingecko#archway"}}, "migaloo": {"uwhale": {"decimals": "6", "symbol": "WHALE", "to": "coingecko#white-whale"}}, "shibarium": {"******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}}, "secret": {"secret14mzwd0ps5q277l20ly2q3aetqe3ev4m4260gf4": {"decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "uscrt": {"decimals": "6", "symbol": "SCRT", "to": "coingecko#secret"}, "secret1k0jntykt7e4g3y88ltc60czgjuqdy4c9e8fzek": {"decimals": "6", "symbol": "SCRT", "to": "coingecko#secret"}, "secret1vnjck36ld45apf8u4fedxd5zy7f5l92y3w5qwq": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "secret1h6z05y90gwm4sqxzhz4pkyp36cna9xtp7q0urv": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "secret18wpjn83dayu4meu6wnn29khfkwdxs7kyrz9c8f": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "alv": {"******************************************": {"decimals": "18", "symbol": "ALV", "to": "coingecko#alvey-chain"}}, "dsc": {"******************************************": {"decimals": "18", "symbol": "DSC", "to": "coingecko#decimal"}}, "kroma": {"******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "manta": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "18", "symbol": "GOK", "to": "coingecko#gok"}, "******************************************": {"decimals": "6", "symbol": "TIA", "to": "coingecko#celestia"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "18", "symbol": "rETH", "to": "coingecko#rocket-pool-eth"}, "******************************************": {"decimals": "18", "symbol": "ZEEP", "to": "coingecko#zeepr"}}, "pg": {"******************************************": {"decimals": "18", "symbol": "WPG", "to": "coingecko#pego-network-2"}, "******************************************": {"decimals": "18", "symbol": "PG", "to": "coingecko#pego-network-2"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}}, "zeta": {"******************************************": {"decimals": "18", "symbol": "WZETA", "to": "coingecko#zetachain"}, "******************************************": {"decimals": "18", "symbol": "ZETA", "to": "coingecko#zetachain"}, "******************************************": {"decimals": "18", "symbol": "USDC.BSC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDC.ETH", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT.ETH", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "USDT.BSC", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "ETH.ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BNB.BSC", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "stZETA", "to": "coingecko#zetaearn-staked-zeta"}, "******************************************": {"decimals": "8", "symbol": "BTC.BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "PUFETH", "to": "coingecko#pufeth"}, "******************************************": {"decimals": "18", "symbol": "POL", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "DAI.ETH", "to": "coingecko#dai"}, "******************************************": {"decimals": "6", "symbol": "USDT.POL", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC.POL", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "PEPE.ETH", "to": "coingecko#pepe"}, "******************************************": {"decimals": "18", "symbol": "SHIB.ETH", "to": "coingecko#shiba-inu"}, "******************************************": {"decimals": "18", "symbol": "ULTI.BSC", "to": "coingecko#ultiverse"}, "******************************************": {"decimals": "18", "symbol": "ULTI.ETH", "to": "coingecko#ultiverse"}}, "fraxtal": {"******************************************": {"decimals": "18", "symbol": "frxETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "wfrxETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "sfrxETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "FXS", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "FRAX", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "sFRAX", "to": "coingecko:staked-frax"}, "******************************************": {"decimals": "18", "symbol": "frxBTC", "to": "coingecko:bitcoin"}, "******************************************": {"decimals": "18", "symbol": "FPI", "to": "coingecko:frax-price-index"}, "******************************************": {"decimals": "18", "symbol": "FPIS", "to": "coingecko:frax-price-index-share"}}, "islm": {"******************************************": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "******************************************": {"decimals": "18", "symbol": "axlWETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "WISLM", "to": "coingecko#islamic-coin"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "axlUSDT", "to": "coingecko#axelar-usdt"}}, "svm": {"******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}}, "iotaevm": {"******************************************": {"decimals": "18", "symbol": "IOTA", "to": "coingecko#iota"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}}, "kinto": {"******************************************": {"decimals": "6", "symbol": "USD+", "to": "coingecko#dinari-usd"}}, "stellar": {"CAS3J7GYLGXMF6TDJBBYYSE3HQ6BBSMLNUQ34T6TZMYMW2EVH34XOWMA": {"decimals": "7", "symbol": "XLM", "to": "coingecko#stellar"}, "CCW67TSZV3SSS2HXMBQ5JFGCKJNXKZM7UQUWUZPUTHXSTZLEO7SJMI75": {"decimals": "7", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "ace": {"******************************************": {"decimals": "18", "symbol": "ACE", "to": "coingecko#endurance"}, "******************************************": {"decimals": "18", "symbol": "ACE", "to": "coingecko#endurance"}}, "massa": {"AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9": {"decimals": "9", "symbol": "WMAS", "to": "coingecko#wrapped-massa"}, "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ": {"decimals": "6", "symbol": "USDC", "to": "coingecko#massa-bridged-usdc-massa"}, "AS1ZGF1upwp9kPRvDKLxFAKRebgg7b3RWDnhgV7VvdZkZsUL7Nuv": {"decimals": "18", "symbol": "DAI", "to": "coingecko#massa-bridged-dai-massa"}, "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY": {"decimals": "18", "symbol": "WETH", "to": "coingecko#wrapped-ether-massa"}, "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV": {"decimals": "18", "symbol": "usdt.b", "to": "coingecko#massa-bridged-usdt-massa"}}, "ham": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "etlk": {"******************************************": {"decimals": "18", "symbol": "XTZ", "to": "coingecko#tezos"}}, "soneium": {"******************************************": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "USDT0"}}}