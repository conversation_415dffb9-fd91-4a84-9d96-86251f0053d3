import * as compound from "./moneyMarkets/compound";
import * as aave from "./moneyMarkets/aave";
import * as euler from "./moneyMarkets/euler";
import * as uniswap from "./markets/uniswap";
import * as curve from "./markets/curve";
import * as balancer from "./markets/balancer";
import * as others from "./other/index";
import * as others2 from "./other/others2";
import * as graphCoins from "./markets/graphCoins";

export default {
  ...compound.adapters,
  ...aave.adapters,
  ...euler.adapters,
  ...uniswap.adapters,
  ...curve.adapters,
  ...balancer.adapters,
  ...others.adapters,
  ...others2.adapters,
  ...graphCoins.adapters,
  fraxtalGas: require("./other/fraxtalGas"),
  reservoirprotocol: require("./rwa/reservoir-protocol"),
  trize: require("./rwa/t-rize"),
  fortunafi: require("./rwa/fortunafi"),
  jupAg: require("./solana/jupAg"),
  midas: require("./rwa/midas"),
  stobox: require("./rwa/stobox"),
  alex: require("./markets/alex"),
  seamless: require("./other/seamless"),
  pyth: require("./oracles/pyth"),
  unknownTokensV3: require("./other/unknownTokensV3"),
  dinari: require("./rwa/dinari"),
  few: require("./other/few"),
  zarban: require("./other/zarban"),
  ociswap: require("./markets/ociswap"),
  optimBonds: require("./other/optimBonds"),
  tangleswap: require("./markets/tangleswap"),
  xexchange: require("./markets/xexchange"),
  // cetus: require("./markets/cetus"),
  aftermath: require("./markets/aftermath"),
  balanced: require("./markets/balanced"),
  tinyman: require("./markets/tinyman"),
  ston: require("./markets/ston"),
  silo: require("./moneyMarkets/silo"),
  hlp: require("./yield/hlp"),
  jeurx: require("./yield/jeurx"),
  digift: require("./rwa/digift"),
  gmxV2: require("./other/gmxV2"),
  timeswap: require("./yield/timeswap"),
  dforce: require("./moneyMarkets/dforce"),
  minswap: require("./markets/minswap2"),
  ergopad: require("./markets/ergopad"),
  sundaeswap: require("./markets/sundaeswap"),
  wingriders: require("./markets/wingriders"),
  // ondo: require("./yield/ondo"),
  yearn: require("./yield/yearn"),
  convex: require("./yield/convex"),
  alchemix: require("./yield/alchemix"),
  meanFinance: require("./yield/mean-finance"),
  misc4626: require("./yield/misc4626"),
  // spectra: require("./yield/misc4626"),
  balancer4626: require("./yield/balancer4626"),
  vesper: require("./yield/vesper"),
  yieldProtocol: require("./yield/yield-protocol"),
  levelFinance: require("./yield/level-finance"),
  quickperps: require("./yield/quickperps"),
  timeless: require("./yield/timeless"),
  beefy: require("./yield/beefy"),
  platypus: require("./markets/platypus"),
  hop: require("./markets/hop"),
  ankr: require("./liquidStaking/ankr"),
  stargate: require("./markets/stargate"),
  jarvis: require("./markets/jarvis"),
  xequity: require("./markets/0xequity"),
  chainlinkNFT: require("./nft/chainlink"),
  arrakis: require("./markets/arrakis"),
  aktionariat: require("./markets/aktionariat"),
  yieldYak: require("./yield/yield-yak"),
  tezos: require("./tezos"),
  nightshade: require("./markets/nightshade"),
  aaveDebt: require("./moneyMarkets/aave-debt"),
  saber: require("./solana/saber"),
  solend: require("./solana/solend"),
  reservoir: require("./nft/reservoir"),
  jpegd: require("./yield/jpegd"),
  glpDerivs: require("./yield/glpDerivs"),
  glv: require("./yield/glv"),
  pendle: require("./yield/pendle"),
  penpie: require("./yield/pendle"),
  phux: require("./markets/phux"),
  wombat: require("./markets/wombat"),
  wombatWrapped: require("./markets/wombat"),
  backed: require("./rwa/backed"),
  vela: require("./yield/vela"),
  chai: require("./yield/chai"),
  kuma: require("./rwa/kuma"),
  ondo: require("./rwa/ondo"),
  pareto: require("./rwa/pareto"),
  hashnote: require("./rwa/hashnote"),
  hiyield: require("./rwa/hiyield"),
  mux: require("./yield/mux"),
  maverick: require("./markets/maverick"),
  steer: require("./markets/steer"),
  derivs: require("./yield/derivs"),
  apiDerivs: require("./yield/apiDerivs"),
  pxeth: require("./liquidStaking/pxeth"),
  // sthApt: require("./liquidStaking/sthapt"),
  mod: require("./markets/thala"),
  ambitFinance: require("./yield/ambit-finance"),
  eigenpie: require("./yield/eigenpie"),
  bitcow: require("./markets/bitcow"),
  pythAgg: require("./oracles/pythAggregatorV3"),
  warlord: require("./other"),
  ifil: require("./liquidStaking/ifil"),
  uniV3: require("./markets/uniswap/v3"),
  liquity: require("./other/liquity"),
  fxProtocol: require("./yield/fx-protocol"),
  crosscurve: require("./markets/crosscurve"),
  samm: require("./markets/samm"),
  gamma: require("./yield/gamma"),
  thena: require("./markets/thena"),
  pancakeStable: require("./markets/pancakeStable"),
  etherfi: require("./yield/etherfi"),
  wcgUSD: require("./other/wcgUSD"),
  usdrif: require("./other/usdrif"),
  xailocker: require("./liquidStaking/xailocker"),
  sbtc: require("./other/sbtc"),
  aries: require("./moneyMarkets/aries"),
  pragma: require("./oracles/pragma"),
  parallelProtocol: require("./markets/parallelProtocol"),
  sanctum: require("./solana/sanctum"),
  spectra: require("./yield/spectra"),
  kamino: require("./solana/kamino"),
  fxsp: require("./yield/fxsp"),
  stakeDao: require("./yield/stakeDao"),
  kodiak: require("./markets/kodiak"),
  kSTRK: require("./other/kSTRK"),
  xSTRK: require("./other/xSTRK"),
  jtoDerivs: require("./solana/jtoDerivs"),
  pac: require("./rwa/thepac"),
  xu3o8: require("./rwa/xu3o8"),
  tonDerivs: require("./other/ton"),
  asseto: require("./rwa/asseto"),
  lsulp: require("./markets/radix"),
  sandglass: require("./yield/sandglass"),
  concentrator: require("./yield/concentrator"),
  liquidly: require("./yield/liquidly"),
  cyclex: require("./rwa/cyclex"),
  indexCoop: require("./yield/indexCoop"),
  siloV2: require("./yield/misc4626"),
  convexStaked: require("./yield/convexStaked"),
  folksFinance: require("./moneyMarkets/folks-finance"),
  xlpt: require("./markets/xlpt"),
  sundaeswapV3: require("./markets/sundaeswapv3"),
  tempest: require("./yield/tempest"),
  momentum: require("./markets/momentum"),
  pst: require("./solana/pst"),
  capyfi: require("./moneyMarkets/capyfi"),
  denario: require("./rwa/denario"),
  quipuswap: require("./markets/quipuswap"),
  reya: require("./yield/reya"),
  bluefin: require("./markets/bluefin"),
  harvest: require("./yield/harvest"),
  opensea: require("./nft/opensea"),
  brkteth: require("./yield/brkteth"),
  vbill: require("./rwa/vbill"),
};
