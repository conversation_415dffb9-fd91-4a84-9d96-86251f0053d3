import { getBalance } from "@defillama/sdk/build/eth/index";

export const wrappedGasTokens: { [key: string]: any } = {
  ethereum: "******************************************",
  optimism: "******************************************",
  base: "******************************************",
  arbitrum: "******************************************",
  fantom: "******************************************",
  avax: "******************************************",
  bsc: "******************************************",
  aurora: "******************************************",
  polygon: "******************************************",
  kava: "******************************************",
  sonic: "******************************************",
};
export async function getGasTokenBalance(
  chain: string,
  target: any,
  balances: any,
  block: number | undefined,
  gasTokenDummyAddress: string = "******************************************",
) {
  if (
    !balances
      .map((b: any) => b.input.target)
      .includes(wrappedGasTokens[chain]) &&
    !balances.map((b: any) => b.input.target).includes(gasTokenDummyAddress)
  )
    return balances;
  const gasTokenBalance = (
    await getBalance({
      target,
      chain: chain as any,
      block,
    })
  ).output;

  const i = balances.indexOf(
    balances.find(
      (b: any) => b == null || b.success == false || b.output == "0",
    ),
  );
  balances[i] = {
    input: {
      target: wrappedGasTokens[chain] || null,
      params: [target],
    },
    output: gasTokenBalance,
    success: true,
  };

  return balances;
}
