{"haven1": {"******************************************": {"decimals": "18", "symbol": "WH1", "to": "coingecko#haven1"}, "******************************************": {"decimals": "18", "symbol": "hETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "hUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "hUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "8", "symbol": "cbBTC", "to": "coingecko#coinbase-wrapped-btc"}, "******************************************": {"decimals": "18", "symbol": "hsETH", "to": "coingecko#hseth"}, "******************************************": {"decimals": "18", "symbol": "hcbETH", "to": "coingecko#coinbase-wrapped-staked-eth"}}, "supra": {"0x1::supra_coin::SupraCoin": {"decimals": "8", "symbol": "SUPRA", "to": "coingecko#supra"}, "0x8f7d16ade319b0fce368ca6cdb98589c4527ce7f5b51e544a9e68e719934458b::hyper_coin::DexlynUSDC": {"decimals": "6", "symbol": "dexUSDC", "to": "coingecko#usd-coin"}}, "ogpu": {"******************************************": {"decimals": "18", "symbol": "WOGPU", "to": "coingecko#open-gpu"}}, "berachain": {"******************************************": {"decimals": "18", "symbol": "hOHM", "to": "coingecko#origami-hohm"}, "******************************************": {"decimals": "18", "symbol": "osBGT", "to": "coingecko#infrafred-bgt"}, "******************************************": {"decimals": "18", "symbol": "BGT", "to": "coingecko#berachain-bera"}, "******************************************": {"decimals": "18", "symbol": "srUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "eBTC", "to": "coingecko#ether-fi-staked-btc"}, "******************************************": {"decimals": "18", "symbol": "rUSD", "to": "coingecko#reservoir-rusd"}, "******************************************": {"decimals": "18", "symbol": "SBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "ylfBTC", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "waBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ylrsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "ylstETH", "to": "coingecko#staked-ether"}, "******************************************": {"decimals": "9", "symbol": "OHM", "to": "coingecko#olympus"}, "******************************************": {"decimals": "18", "symbol": "USDe", "to": "coingecko#ethena-usde"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"decimals": "18", "symbol": "MIM", "to": "coingecko#magic-internet-money"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "STONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "xSolvBTC", "to": "coingecko#solv-protocol-solvbtc-bbn"}, "******************************************": {"decimals": "8", "symbol": "uniBTC", "to": "coingecko#universal-btc"}, "******************************************": {"decimals": "18", "symbol": "rswETH", "to": "coingecko#restaked-swell-eth"}, "******************************************": {"decimals": "8", "symbol": "FBTC", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "sUSDe", "to": "coingecko#ethena-staked-usde"}, "******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#wrapped-bera", "decimals": 18, "symbol": "BERA"}, "******************************************": {"to": "coingecko#wrapped-bera", "decimals": 18, "symbol": "WBERA"}, "******************************************": {"to": "coingecko#ethereum:******************************************", "decimals": 18, "symbol": "SBTC"}, "******************************************": {"to": "coingecko#ethereum:******************************************", "decimals": 18, "symbol": "LBTC"}, "******************************************": {"decimals": "8", "symbol": "WFBTC", "to": "coingecko#ignition-fbtc"}}, "alephium": {"19246e8c2899bc258a1156e08466e3cdd3323da756d8a543c7fc911847b96f00": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "kava": {"******************************************": {"decimals": "18", "symbol": "KAVA", "to": "coingecko#kava"}}, "celo": {"******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "cSOL", "to": "coingecko#solana"}, "******************************************": {"decimals": "18", "symbol": "cBNB", "to": "coingecko#binancecoin"}, "******************************************": {"to": "coingecko#polkamarkets", "decimals": 18, "symbol": "POLK"}}, "sui": {"0xb848cce11ef3a8f62eccea6eb5b35a12c4c2b1ee1af7755d02d7bd6218e8226f::coin::COIN": {"decimals": "8", "symbol": "BNB", "to": "coingecko#binancecoin"}, "0x9a5502414b5d51d01c8b5641db7436d789fa15a245694b24aa37c25c2a6ce001::scb::SCB": {"decimals": "5", "symbol": "SCB", "to": "coingecko#sacabam"}, "0xbde4ba4c2e274a60ce15c1cfff9e5c42e41654ac8b6d906a57efa4bd3c29f47d::hasui::HASUI": {"decimals": "9", "symbol": "HASUI", "to": "coingecko#haedal-staked-sui"}, "0x6db9a7bb22829898fd281879778a175120ebfc77eafc1f8ee341654cfc3f8dc2::burry::BURRY": {"decimals": "9", "symbol": "BURRY", "to": "coingecko#burrial"}, "0x549e8b69270defbfafd4f94e17ec44cdbdd99820b33bda2278dea3b9a32d3f55::cert::CERT": {"decimals": "9", "symbol": "VSUI", "to": "coingecko#volo-staked-sui"}, "0x76cb819b01abed502bee8a702b4c2d547532c12f25001c9dea795a5e631c26f1::fud::FUD": {"decimals": "5", "symbol": "FUD", "to": "coingecko#fud-the-pug"}, "0x5d1f47ea69bb0de31c313d7acf89b890dbb8991ea8e03c6c355171f84bb1ba4a::turbos::TURBOS": {"decimals": "9", "symbol": "TURBOS", "to": "coingecko#turbos-finance"}, "0xf325ce1300e8dac124071d3152c5c5ee6174914f8bc2161e88329cf579246efc::afsui::AFSUI": {"decimals": "9", "symbol": "afSUI", "to": "coingecko#aftermath-staked-sui"}, "0x3a5143bb1196e3bcdfab6203d1683ae29edd26294fc8bfeafe4aaa9d2704df37::coin::COIN": {"decimals": "8", "symbol": "APT", "to": "coingecko#aptos"}, "0x960b531667636f39e85867775f52f6b1f220a058c4de786905bdf761e06a56bb::usdy::USDY": {"decimals": "6", "symbol": "USDY", "to": "coingecko#ondo-finance-us-dollar-yield"}, "0xa99b8952d4f7d947ea77fe0ecdcc9e5fc0bcab2841d6e2a5aa00c3044e5544b5::navx::NAVX": {"decimals": "9", "symbol": "NAVX", "to": "coingecko#navi"}, "0xf4530aa5ef8af33c497ec38f54ff9dd45fad9157264efae9693eb62faf8667b5::coin::COIN": {"to": "coingecko#dinero-staked-eth", "decimals": 8, "symbol": "pxETH"}}, "neutron": {"untrn": {"decimals": "6", "symbol": "NTRN", "to": "coingecko#neutron-3"}, "neutron10dxyft3nv4vpxh5vrpn0xw8geej8dw3g39g7nqp8mrm307ypssksau29af": {"decimals": "6", "symbol": "AXV", "to": "coingecko#astrovault"}, "neutron1vjl4ze7gr32lar5s4fj776v70j4ml7mlt4aqln2hwgfhqjck8xwqfhx8vj": {"decimals": "6", "symbol": "xATOM", "to": "coingecko#astrovault-xatom"}, "factory:neutron1ffus553eet978k024lmssw0czsxwr97mggyv85lpcsdkft8v9ufsz3sa07:astro": {"decimals": "6", "symbol": "ASTRO", "to": "coingecko#astroport-fi"}}, "nibiru": {"******************************************": {"decimals": "18", "symbol": "NIBI", "to": "coingecko#nibiru"}, "unibi": {"decimals": "6", "symbol": "NIBI", "to": "coingecko#nibiru"}, "tf:nibi1vetfuua65frvf6f458xgtjerf0ra7wwjykrdpuyn0jur5x07awxsfka0ga:axv": {"decimals": "6", "symbol": "AXV", "to": "coingecko#astrovault"}, "nibi1cehpv50vl90g9qkwwny8mw7txw79zs6f7wsfe8ey7dgp238gpy4qhdqjhm": {"decimals": "6", "symbol": "xNIBI", "to": "coingecko#astrovault-xnibi"}, "0x0CaCF669f8446BeCA826913a3c6B96aCD4b02a97": {"decimals": "18", "symbol": "WNIBI", "to": "coingecko#nibiru"}, "0xcA0a9Fb5FBF692fa12fD13c0A900EC56Bb3f0a7b": {"decimals": "6", "symbol": "stNIBI", "to": "coingecko#nibiru"}, "tf:nibi1udqqx30cw8nwjxtl4l28ym9hhrp933zlq8dqxfjzcdhvl8y24zcqpzmh8m:ampNIBI": {"decimals": "6", "symbol": "stNIBI.nibi", "to": "coingecko#nibiru"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "MIM", "to": "coingecko#magic-internet-money"}, "******************************************": {"decimals": "18", "symbol": "USDC.arb", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "AXV", "to": "coingecko#astrovault"}, "******************************************": {"decimals": "18", "symbol": "WNIBI.omni", "to": "coingecko#nibiru"}}, "tron": {"tthzxnrlrw2brp9dctqu8i4wd9udcwedz3": {"decimals": "18", "symbol": "stUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "TRON", "to": "coingecko#tron"}, "TUPM7K8REVzD2UdV4R5fe5M8XbnR2DdoJ6": {"decimals": "18", "symbol": "HTX", "to": "coingecko#htx-dao"}}, "polygon_zkevm": {"******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}}, "polygon": {"******************************************": {"decimals": "18", "symbol": "uRON", "to": "coingecko#ronin"}, "******************************************": {"decimals": "18", "symbol": "uCOMP", "to": "coingecko#compound-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uZRO", "to": "coingecko#layerzero"}, "******************************************": {"decimals": "18", "symbol": "uPENGU", "to": "coingecko#pudgy-penguins"}, "******************************************": {"decimals": "18", "symbol": "uETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "18", "symbol": "uALEO", "to": "coingecko#aleo"}, "******************************************": {"decimals": "18", "symbol": "uREZ", "to": "coingecko#renzo"}, "******************************************": {"decimals": "18", "symbol": "uTAO", "to": "coingecko#bittensor"}, "******************************************": {"decimals": "18", "symbol": "uPOL", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uIP", "to": "coingecko#story"}, "******************************************": {"decimals": "18", "symbol": "u1INCH", "to": "coingecko#1inch"}, "******************************************": {"decimals": "18", "symbol": "uBONK", "to": "coingecko#bonk"}, "******************************************": {"decimals": "18", "symbol": "uTIA", "to": "coingecko#celestia"}, "******************************************": {"decimals": "18", "symbol": "uCRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"decimals": "18", "symbol": "uCRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "uWIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"decimals": "18", "symbol": "uENS", "to": "coingecko#ethereum-name-service"}, "******************************************": {"decimals": "18", "symbol": "uFIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "18", "symbol": "uFLR", "to": "coingecko#flare-networks"}, "******************************************": {"decimals": "18", "symbol": "uFLOKI", "to": "coingecko#floki"}, "******************************************": {"decimals": "18", "symbol": "uFLOW", "to": "coingecko#flow"}, "******************************************": {"decimals": "18", "symbol": "uJTO", "to": "coingecko#jito-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uMOVE", "to": "coingecko#movement"}, "******************************************": {"decimals": "18", "symbol": "uONDO", "to": "coingecko#ondo-finance"}, "******************************************": {"decimals": "18", "symbol": "uOP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "uPNUT", "to": "coingecko#peanut-the-squirrel"}, "0xd045be6AB98D17A161cfCfc118a8b428D70543Ff": {"decimals": "18", "symbol": "uSTRK", "to": "coingecko#starknet"}, "0x6ca225AE2C92c8A7E9c3162cFcAaA55aD0B09701": {"decimals": "18", "symbol": "uGMT", "to": "coingecko#stepn"}, "0x444Fa322DA64A49A32D29ccd3a1f4DF3De25cF52": {"decimals": "18", "symbol": "uSNX", "to": "coingecko#havven"}, "0x1B0DcC586323C0e10f8Be72EcC104048f25FD625": {"decimals": "18", "symbol": "uVET", "to": "coingecko#vechain"}, "0x3a6B4b4F2250B8CCe56cED4ca286a2ebe6F479A2": {"decimals": "18", "symbol": "uZK", "to": "coingecko#zksync"}, "0x44951C66dFe920baED34457A2cFA65a0c7ff2025": {"decimals": "18", "symbol": "uBLUR", "to": "coingecko#blur"}, "0x2f2041c267795a85B0De04443E7B947A6234fEe8": {"decimals": "18", "symbol": "uKSM", "to": "coingecko#kusama"}, "0xe3AE3EE16a89973D67b678aaD2c3bE865Dcc6880": {"decimals": "18", "symbol": "uLPT", "to": "coingecko#livepeer"}, "0x3ECb91ac996E8c55fe1835969A4967F95a07Ca71": {"decimals": "18", "symbol": "uROSE", "to": "coingecko#oasis-network"}, "0xD6A746236F15E18053Dd3ae8c27341B44CB08E59": {"decimals": "18", "symbol": "uMINA", "to": "coingecko#mina-protocol"}, "0xc5cDEb649ED1A7895b935ACC8EB5Aa0D7a8492BE": {"decimals": "18", "symbol": "uCHZ", "to": "coingecko#chiliz"}, "0x83f31af747189c2FA9E5DeB253200c505eff6ed2": {"decimals": "18", "symbol": "uZEC", "to": "coingecko#zcash"}, "0x9AF46F95a0a8be5C2E0a0274A8b153C72d617E85": {"decimals": "18", "symbol": "uAPE", "to": "coingecko#apecoin"}, "0x16275fD42439A6671b188bDc3949a5eC61932C48": {"decimals": "18", "symbol": "uEGLD", "to": "coingecko#elrond-erd-2"}, "0x508e751fdCf144910074Cc817a16757F608DB52A": {"decimals": "18", "symbol": "uMANA", "to": "coingecko#decentraland"}, "0x5A03841C2e2f5811f9E548cF98E88e878e55d99E": {"decimals": "18", "symbol": "uAXS", "to": "coingecko#axie-infinity"}, "0x05f191a4Aac4b358AB99DB3A83A8F96216ecb274": {"decimals": "18", "symbol": "uHNT", "to": "coingecko#helium"}, "0x31d664ebd97A50d5a2Cd49B16f7714AB2516Ed25": {"decimals": "18", "symbol": "uEOS", "to": "coingecko#eos"}, "0xD7D5c59457d66FE800dBA22b35e9c6C379D64499": {"decimals": "18", "symbol": "uXTZ", "to": "coingecko#tezos"}, "0x1B94330EEc66BA458a51b0b14f411910D5f678d0": {"decimals": "18", "symbol": "uSAND", "to": "coingecko#the-sandbox"}, "0x893ADcbdC7FcfA0eBb6d3803f01Df1eC199Bf7C5": {"decimals": "18", "symbol": "uQNT", "to": "coingecko#quant"}, "0x135Ff404bA56E167F58bc664156beAa0A0Fd95ac": {"decimals": "18", "symbol": "uLDO", "to": "coingecko#lido-dao"}, "0x8989377fd349ADFA99E6CE3Cb6c0D148DfC7F19e": {"decimals": "18", "symbol": "uJASMY", "to": "coingecko#jasmycoin"}, "0x3d00283AF5AB11eE7f6Ec51573ab62b6Fb6Dfd8f": {"decimals": "18", "symbol": "uGRT", "to": "coingecko#the-graph"}, "0x0935b271CA903ADA3FFe1Ac1353fC4A49E7EE87b": {"decimals": "18", "symbol": "uIMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "uINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"decimals": "18", "symbol": "uATOM", "to": "coingecko#cosmos-hub"}, "******************************************": {"decimals": "18", "symbol": "uFET", "to": "coingecko#fetch-ai"}, "******************************************": {"decimals": "18", "symbol": "uETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"decimals": "18", "symbol": "uAAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "uICP", "to": "coingecko#internet-computer"}, "******************************************": {"decimals": "18", "symbol": "uHBAR", "to": "coingecko#hedera"}, "******************************************": {"decimals": "18", "symbol": "uXLM", "to": "coingecko#stellar"}, "******************************************": {"decimals": "18", "symbol": "uTRUMP", "to": "coingecko#official-trump"}, "******************************************": {"decimals": "18", "symbol": "uPEPE", "to": "coingecko#pepe"}, "******************************************": {"decimals": "18", "symbol": "uRNDR", "to": "coingecko#render"}, "******************************************": {"decimals": "18", "symbol": "uMKR", "to": "coingecko#maker"}, "******************************************": {"decimals": "18", "symbol": "uSTX", "to": "coingecko#blockstack"}, "******************************************": {"decimals": "18", "symbol": "uUNI", "to": "coingecko#uniswap"}, "******************************************": {"decimals": "18", "symbol": "uMATIC", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "uLTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "uDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "uBCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"decimals": "18", "symbol": "uALGO", "to": "coingecko#algorand"}, "******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "5", "symbol": "USTBL", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "5", "symbol": "EUTBL", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "8", "symbol": "BTC.b", "to": "coingecko#bitcoin-avalanche-bridged-btc-b"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "9", "symbol": "USDR", "to": "coingecko#real-usd"}, "******************************************": {"decimals": "18", "symbol": "PAXG", "to": "coingecko#pax-gold"}, "******************************************": {"decimals": "18", "symbol": "BEPRO", "to": "coingecko#bepro-network"}}, "evmos": {"******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "era": {"******************************************": {"decimals": "18", "symbol": "AAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "9", "symbol": "SOL", "to": "coingecko#solana"}, "******************************************": {"decimals": "18", "symbol": "UNI", "to": "coingecko#uniswap"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}}, "shibarium": {"******************************************": {"decimals": "18", "symbol": "sKNINE", "to": "coingecko#k9-finance-dao"}}, "taraxa": {"******************************************": {"decimals": "18", "symbol": "TARA", "to": "coingecko#taraxa"}}, "ethereum": {"******************************************": {"decimals": "18", "symbol": "tBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "tUSDe", "to": "coingecko#ethena-staked-usde"}, "******************************************": {"decimals": "18", "symbol": "tETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "18", "symbol": "BENJI", "to": "coingecko#franklin-templeton-benji"}, "******************************************": {"decimals": "8", "symbol": "DSC", "to": "asset#polygon:******************************************"}, "******************************************": {"decimals": "8", "symbol": "DGC", "to": "asset#polygon:******************************************"}, "******************************************": {"decimals": "8", "symbol": "bfBTC", "to": "coingecko#bitcoin"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "liquidBeraETH"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "SolvBTC.BERA"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "liquidBeraBTC"}, "******************************************": {"to": "coingecko#aethir", "decimals": 18, "symbol": "stAethir"}, "******************************************": {"to": "coingecko#zentry", "decimals": 18, "symbol": "stZENT"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"decimals": "18", "symbol": "sUSD", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "stBTC", "to": "coingecko#lorenzo-stbtc"}, "******************************************": {"decimals": "8", "symbol": "brBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "sETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "8", "symbol": "oBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "6", "symbol": "fUSDC", "to": "coingecko#fluid-usdc"}, "******************************************": {"decimals": "6", "symbol": "zeUSD", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "8", "symbol": "NBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "wbC3M", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "wbIB01", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "amphrLRT", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BONE", "to": "coingecko#bone-shibaswap"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ezETH", "to": "coingecko#renzo-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "WUSDM", "to": "coingecko#wrapped-usdm"}, "******************************************": {"decimals": "6", "symbol": "21XRP", "to": "coingecko#ripple"}, "******************************************": {"decimals": "6", "symbol": "21ADA", "to": "coingecko#cardano"}, "******************************************": {"decimals": "8", "symbol": "21DOGE", "to": "coingecko#dogecoin"}, "******************************************": {"decimals": "10", "symbol": "21DOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "8", "symbol": "21BCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"decimals": "8", "symbol": "21LTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "stkcvxcrvUSDUSDT-f", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "stkcvxcrvUSDUSDC-f", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "pxCVX", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "CRETH2", "to": "coingecko#cream-eth2"}, "******************************************": {"decimals": "18", "symbol": "RSR", "to": "coingecko#reserve-rights-token"}, "******************************************": {"decimals": "18", "symbol": "NEST", "to": "coingecko#nest"}, "******************************************": {"decimals": "18", "symbol": "IAG", "to": "coingecko#iagon"}, "******************************************": {"decimals": "18", "symbol": "ethPNT", "to": "coingecko#pnetwork"}, "******************************************": {"decimals": "18", "symbol": "RDNT", "to": "coingecko#radiant"}, "******************************************": {"decimals": "6", "symbol": "ADA", "to": "coingecko#cardano"}, "******************************************": {"decimals": "8", "symbol": "wanBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "CFi", "to": "coingecko#cyberfi"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "10", "symbol": "wanDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "2", "symbol": "EURS", "to": "coingecko#stasis-eurs"}, "******************************************": {"decimals": "18", "symbol": "GEROV2", "to": "coingecko#gerowallet"}, "******************************************": {"decimals": "18", "symbol": "wanMOVR", "to": "coingecko#moonriver"}, "******************************************": {"decimals": "18", "symbol": "WAN", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "6", "symbol": "wanXRP", "to": "coingecko#ripple"}, "******************************************": {"decimals": "18", "symbol": "GYD", "to": "coingecko#gyroscope-gyd"}, "******************************************": {"decimals": "9", "symbol": "sVEC", "to": "coingecko#vector-reserve"}, "******************************************": {"decimals": "18", "symbol": "PUFF", "to": "coingecko#puff-the-dragon"}, "******************************************": {"decimals": "18", "symbol": "cSTONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "apxETH", "to": "coingecko#dinero-apxeth"}, "******************************************": {"decimals": "18", "symbol": "CVX1", "to": "coingecko#convex-finance"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-bedrock-eth", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-Avalon-eth", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-solv-babylon-eth", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-pump-eth"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "USDtb"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-cian-eth", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "WFBTC", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "fxSAVE", "to": "coingecko#fx-save"}, "******************************************": {"to": "coingecko#prometeus", "decimals": 18, "symbol": "PROM"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.ENA", "to": "coingecko#solv-btc"}, "******************************************": {"to": "coingecko#ripple-usd", "decimals": 18, "symbol": "RLUSD"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-lorenzo-eth"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 18, "symbol": "WBTC18"}, "******************************************": {"to": "coingecko#coinbase-wrapped-btc", "decimals": 18, "symbol": "cbBTC18"}}, "fantom": {"******************************************": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin-wormhole-from-ethereum"}, "******************************************": {"decimals": "18", "symbol": "LQRD", "to": "coingecko#liquiddriver"}, "******************************************": {"decimals": "18", "symbol": "LL", "to": "coingecko#lightlink"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}}, "meter": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "bsc": {"******************************************": {"decimals": "18", "symbol": "yUSD", "to": "coingecko#yieldfi-ytoken"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "rtCIC", "to": "coingecko#cicada-finance"}, "******************************************": {"decimals": "18", "symbol": "SY-clisBNB", "to": "coingecko#synclub-staked-bnb"}, "******************************************": {"decimals": "18", "symbol": "sUSDe", "to": "coingecko#ethena-staked-usde"}, "******************************************": {"decimals": "18", "symbol": "USDe", "to": "coingecko#ethena-usde"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.BNB", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "slisBNB CollateralListaDAODistributor", "to": "coingecko#synclub-staked-bnb"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "brBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "mwBETH", "to": "coingecko#wrapped-beacon-eth"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "METIS", "to": "coingecko#metis-token"}, "******************************************": {"decimals": "18", "symbol": "OOE", "to": "coingecko#openocean"}, "******************************************": {"decimals": "18", "symbol": "SHIB", "to": "coingecko#shiba-inu"}, "******************************************": {"name": "Staked FRAX", "decimals": "18", "symbol": "sFRAX", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.ENA", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "weETH", "to": "coingecko#wrapped-eeth"}, "******************************************": {"decimals": "18", "symbol": "STONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "xSolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "8", "symbol": "FBTC", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-Avalon-bsc", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-bedrock-eth", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 8, "symbol": "uniBTC"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDF"}, "******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "asBTC", "to": "coingecko#binance-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "uBTC", "to": "coingecko#bitcoin"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-pump-bsc"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "8", "symbol": "CoBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "8", "symbol": "bfBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "8", "symbol": "enzoBTC", "to": "coingecko#lorenzo-stbtc"}, "******************************************": {"to": "coingecko#prometeus", "decimals": 18, "symbol": "PROM"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-jupiter", "decimals": 18, "symbol": "SOLVBTC.JUP"}}, "stacks": {"SP4SZE494VC2YC5JYG7AYFQ44F5Q4PYV7DVMDPBG.ststxbtc-token-v2::ststxbtc": {"decimals": "6", "symbol": "stSTXbtc", "to": "coingecko#blockstack"}, "SP102V8P0F7JX67ARQ77WEA3D3CFB5XW39REDT0AM.token-wstx": {"decimals": "6", "symbol": "WSTX", "to": "coingecko#blockstack"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.token-wstx": {"decimals": "8", "symbol": "WSTX", "to": "coingecko#blockstack"}, "SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9.token-wbtc": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#bitcoin"}, "SP102V8P0F7JX67ARQ77WEA3D3CFB5XW39REDT0AM.token-wstx-v2": {"decimals": "8", "symbol": "WSTX", "to": "coingecko#blockstack"}, "sp2c2yfp12ajzb4mabjbaj55xecvs7e4pmmz89yzr.arkadiko-token::diko": {"decimals": "6", "symbol": "DIKO", "to": "coingecko#arkadiko-protocol"}, "SM26NBC8SFHNW4P1Y4DFH27974P56WN86C92HPEHH.token-vlqstx::vlqstx": {"to": "coingecko#blockstack", "decimals": 6, "symbol": "vlqSTX"}, "SM3VDXK3WZZSA84XXFKAFAF15NNZX32CTSG82JFQ4.sbtc-token::sbtc-token": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "sBTC"}}, "arbitrum": {"******************************************": {"decimals": "18", "symbol": "<PERSON><PERSON><PERSON><PERSON>", "to": "coingecko#equilibria-finance-ependle"}, "******************************************": {"decimals": "18", "symbol": "eATH", "to": "coingecko#aethir"}, "******************************************": {"decimals": "6", "symbol": "dUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "6", "symbol": "wM", "to": "coingecko#wrappedm-by-m0"}, "******************************************": {"decimals": "18", "symbol": "alETH", "to": "coingecko#alchemix-eth"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"decimals": "6", "symbol": "fUSDC", "to": "coingecko#fluid-usdc"}, "******************************************": {"decimals": "6", "symbol": "fUSDT", "to": "coingecko#fluid-usdc"}, "******************************************": {"decimals": "18", "symbol": "RDNT", "to": "coingecko#radiant-capital"}, "******************************************": {"decimals": "18", "symbol": "saETH", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "USDY", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "WQ", "to": "coingecko#q-protocol"}, "******************************************": {"decimals": "18", "symbol": "esGMX", "to": "coingecko#gmx"}, "******************************************": {"decimals": "18", "symbol": "S*ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "S*USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "S*USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "wstjUSDC", "to": "arbitrum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "ezETH", "to": "coingecko#renzo-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "PEAS", "to": "coingecko#peapods-finance"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "******************************************": {"decimals": "18", "symbol": "CADC", "to": "coingecko#cad-coin"}, "******************************************": {"decimals": "18", "symbol": "AAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "RDNT", "to": "coingecko#radiant"}, "******************************************": {"decimals": "18", "symbol": "CNG", "to": "coingecko#changer"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "WBNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "WAVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "OP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "GOA", "to": "coingecko#goat-protocol"}, "******************************************": {"decimals": "8", "symbol": "dlcBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "weETH", "to": "coingecko#wrapped-eeth"}, "******************************************": {"decimals": "18", "symbol": "ZEEP", "to": "coingecko#zeepr"}, "******************************************": {"decimals": "18", "symbol": "wUSDM", "to": "coingecko#mountain-protocol-usdm"}, "******************************************": {"decimals": "18", "symbol": "USDEX+", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDe", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "xSolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "ETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "18", "symbol": "DPI", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 8, "symbol": "uniBTC"}, "******************************************": {"decimals": "8", "symbol": "FBTC", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-bedrock-arb", "to": "coingecko#ignition-fbtc"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-pump-arb"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#umoja-ybtc", "decimals": 18, "symbol": "YBTC"}}, "tezos": {"tezos": {"decimals": "0", "symbol": "XTZ", "to": "coingecko#tezos"}}, "optimism": {"******************************************": {"decimals": "18", "symbol": "sUSDS", "to": "coingecko#susds"}, "******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "apxETH", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"decimals": "18", "symbol": "WQ", "to": "coingecko#q-protocol"}, "******************************************": {"decimals": "18", "symbol": "wrsETH", "to": "coingecko#wrapped-rseth"}, "******************************************": {"decimals": "18", "symbol": "wrsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "COMP", "to": "coingecko#compound-governance-token-governance-token"}, "******************************************": {"decimals": "18", "symbol": "QI", "to": "coingecko#qi-dao"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#renzo-restaked-eth", "decimals": 18, "symbol": "ezETH"}}, "rsk": {"******************************************": {"decimals": "18", "symbol": "xSolvBTC", "to": "coingecko#solv-protocol-solvbtc-bbn"}, "******************************************": {"to": "coingecko#solv-btc", "decimals": 18, "symbol": "SolvBTC"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "RBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BITP", "to": "coingecko#bitpro"}}, "zyx": {"******************************************": {"decimals": "18", "symbol": "WZYX", "to": "coingecko#zyx"}}, "goat": {"******************************************": {"decimals": "18", "symbol": "nETH", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "rnETH", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "UBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "DOGEB", "to": "coingecko#dogecoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "WGBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BTCB", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ArtBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ArtBTC", "to": "coingecko#bitcoin"}}, "avax": {"******************************************": {"decimals": "18", "symbol": "yUSD", "to": "coingecko#yieldfi-ytoken"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "avBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "rsAVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}}, "base": {"******************************************": {"decimals": "18", "symbol": "IMGN", "to": "coingecko#imgn-labs"}, "******************************************": {"decimals": "18", "symbol": "RLP", "to": "coingecko#resolv-rlp"}, "******************************************": {"decimals": "18", "symbol": "WSTUSR", "to": "coingecko#resolv-wstusr"}, "******************************************": {"decimals": "18", "symbol": "uRON", "to": "coingecko#ronin"}, "******************************************": {"decimals": "18", "symbol": "uCOMP", "to": "coingecko#compound-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uZRO", "to": "coingecko#layerzero"}, "******************************************": {"decimals": "18", "symbol": "uPENGU", "to": "coingecko#pudgy-penguins"}, "******************************************": {"decimals": "18", "symbol": "uETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "18", "symbol": "uALEO", "to": "coingecko#aleo"}, "******************************************": {"decimals": "18", "symbol": "uREZ", "to": "coingecko#renzo"}, "******************************************": {"decimals": "18", "symbol": "uTAO", "to": "coingecko#bittensor"}, "******************************************": {"decimals": "18", "symbol": "uPOL", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uIP", "to": "coingecko#story"}, "******************************************": {"decimals": "18", "symbol": "u1INCH", "to": "coingecko#1inch"}, "******************************************": {"decimals": "18", "symbol": "uBONK", "to": "coingecko#bonk"}, "******************************************": {"decimals": "18", "symbol": "uTIA", "to": "coingecko#celestia"}, "******************************************": {"decimals": "18", "symbol": "uCRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"decimals": "18", "symbol": "uCRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "uWIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"decimals": "18", "symbol": "uENS", "to": "coingecko#ethereum-name-service"}, "******************************************": {"decimals": "18", "symbol": "uFIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "18", "symbol": "uFLR", "to": "coingecko#flare-networks"}, "******************************************": {"decimals": "18", "symbol": "uFLOKI", "to": "coingecko#floki"}, "******************************************": {"decimals": "18", "symbol": "uFLOW", "to": "coingecko#flow"}, "******************************************": {"decimals": "18", "symbol": "uJTO", "to": "coingecko#jito-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uMOVE", "to": "coingecko#movement"}, "******************************************": {"decimals": "18", "symbol": "uONDO", "to": "coingecko#ondo-finance"}, "******************************************": {"decimals": "18", "symbol": "uOP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "uPNUT", "to": "coingecko#peanut-the-squirrel"}, "0xd045be6AB98D17A161cfCfc118a8b428D70543Ff": {"decimals": "18", "symbol": "uSTRK", "to": "coingecko#starknet"}, "0x6ca225AE2C92c8A7E9c3162cFcAaA55aD0B09701": {"decimals": "18", "symbol": "uGMT", "to": "coingecko#stepn"}, "0x444Fa322DA64A49A32D29ccd3a1f4DF3De25cF52": {"decimals": "18", "symbol": "uSNX", "to": "coingecko#havven"}, "0x1B0DcC586323C0e10f8Be72EcC104048f25FD625": {"decimals": "18", "symbol": "uVET", "to": "coingecko#vechain"}, "0x3a6B4b4F2250B8CCe56cED4ca286a2ebe6F479A2": {"decimals": "18", "symbol": "uZK", "to": "coingecko#zksync"}, "0x44951C66dFe920baED34457A2cFA65a0c7ff2025": {"decimals": "18", "symbol": "uBLUR", "to": "coingecko#blur"}, "0x2f2041c267795a85B0De04443E7B947A6234fEe8": {"decimals": "18", "symbol": "uKSM", "to": "coingecko#kusama"}, "0xe3AE3EE16a89973D67b678aaD2c3bE865Dcc6880": {"decimals": "18", "symbol": "uLPT", "to": "coingecko#livepeer"}, "0x3ECb91ac996E8c55fe1835969A4967F95a07Ca71": {"decimals": "18", "symbol": "uROSE", "to": "coingecko#oasis-network"}, "0xD6A746236F15E18053Dd3ae8c27341B44CB08E59": {"decimals": "18", "symbol": "uMINA", "to": "coingecko#mina-protocol"}, "0xc5cDEb649ED1A7895b935ACC8EB5Aa0D7a8492BE": {"decimals": "18", "symbol": "uCHZ", "to": "coingecko#chiliz"}, "0x83f31af747189c2FA9E5DeB253200c505eff6ed2": {"decimals": "18", "symbol": "uZEC", "to": "coingecko#zcash"}, "0x9AF46F95a0a8be5C2E0a0274A8b153C72d617E85": {"decimals": "18", "symbol": "uAPE", "to": "coingecko#apecoin"}, "0x16275fD42439A6671b188bDc3949a5eC61932C48": {"decimals": "18", "symbol": "uEGLD", "to": "coingecko#elrond-erd-2"}, "0x508e751fdCf144910074Cc817a16757F608DB52A": {"decimals": "18", "symbol": "uMANA", "to": "coingecko#decentraland"}, "0x5A03841C2e2f5811f9E548cF98E88e878e55d99E": {"decimals": "18", "symbol": "uAXS", "to": "coingecko#axie-infinity"}, "0x05f191a4Aac4b358AB99DB3A83A8F96216ecb274": {"decimals": "18", "symbol": "uHNT", "to": "coingecko#helium"}, "0x31d664ebd97A50d5a2Cd49B16f7714AB2516Ed25": {"decimals": "18", "symbol": "uEOS", "to": "coingecko#eos"}, "0xD7D5c59457d66FE800dBA22b35e9c6C379D64499": {"decimals": "18", "symbol": "uXTZ", "to": "coingecko#tezos"}, "0x1B94330EEc66BA458a51b0b14f411910D5f678d0": {"decimals": "18", "symbol": "uSAND", "to": "coingecko#the-sandbox"}, "0x893ADcbdC7FcfA0eBb6d3803f01Df1eC199Bf7C5": {"decimals": "18", "symbol": "uQNT", "to": "coingecko#quant"}, "0x135Ff404bA56E167F58bc664156beAa0A0Fd95ac": {"decimals": "18", "symbol": "uLDO", "to": "coingecko#lido-dao"}, "0x8989377fd349ADFA99E6CE3Cb6c0D148DfC7F19e": {"decimals": "18", "symbol": "uJASMY", "to": "coingecko#jasmycoin"}, "0x3d00283AF5AB11eE7f6Ec51573ab62b6Fb6Dfd8f": {"decimals": "18", "symbol": "uGRT", "to": "coingecko#the-graph"}, "0x0935b271CA903ADA3FFe1Ac1353fC4A49E7EE87b": {"decimals": "18", "symbol": "uIMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "uINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"decimals": "18", "symbol": "uATOM", "to": "coingecko#cosmos-hub"}, "******************************************": {"decimals": "18", "symbol": "uFET", "to": "coingecko#fetch-ai"}, "******************************************": {"decimals": "18", "symbol": "uETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"decimals": "18", "symbol": "uAAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "uICP", "to": "coingecko#internet-computer"}, "******************************************": {"decimals": "18", "symbol": "uHBAR", "to": "coingecko#hedera"}, "******************************************": {"decimals": "18", "symbol": "uXLM", "to": "coingecko#stellar"}, "******************************************": {"decimals": "18", "symbol": "uTRUMP", "to": "coingecko#official-trump"}, "******************************************": {"decimals": "18", "symbol": "uPEPE", "to": "coingecko#pepe"}, "******************************************": {"decimals": "18", "symbol": "uRNDR", "to": "coingecko#render"}, "******************************************": {"decimals": "18", "symbol": "uMKR", "to": "coingecko#maker"}, "******************************************": {"decimals": "18", "symbol": "uSTX", "to": "coingecko#blockstack"}, "******************************************": {"decimals": "18", "symbol": "uUNI", "to": "coingecko#uniswap"}, "******************************************": {"decimals": "18", "symbol": "uMATIC", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "uLTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "uDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "uBCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"decimals": "18", "symbol": "uALGO", "to": "coingecko#algorand"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 18, "symbol": "USDT0"}, "******************************************": {"to": "asset#arbitrum:******************************************", "decimals": "18", "symbol": "yBTC"}, "******************************************": {"to": "coingecko#resolv-usr", "decimals": "18", "symbol": "USR"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "8", "symbol": "oBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "msETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "fsBLP", "to": "asset#base:******************************************"}, "******************************************": {"decimals": "18", "symbol": "fBLP", "to": "asset#base:******************************************"}, "******************************************": {"decimals": "6", "symbol": "verUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "18", "symbol": "EURA", "to": "coingecko#ageur"}, "******************************************": {"decimals": "18", "symbol": "sUSDS", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "nARS", "to": "coingecko#num-ars"}, "******************************************": {"decimals": "18", "symbol": "MONEY", "to": "coingecko#defi-money"}, "******************************************": {"decimals": "18", "symbol": "cWETHv3", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "PEAS", "to": "coingecko#peapods-finance"}, "******************************************": {"decimals": "9", "symbol": "wUSDR", "to": "coingecko#wrapped-usdr"}, "******************************************": {"decimals": "18", "symbol": "MAI", "to": "coingecko#mimatic"}, "******************************************": {"decimals": "18", "symbol": "rcbETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BTCUSD", "to": "coingecko#bitcoin-usd-btcfi"}, "******************************************": {"decimals": "18", "symbol": "CHEX", "to": "coingecko#chex-token"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#rocket-pool-eth", "decimals": 18, "symbol": "rETH"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#zai-stablecoin", "decimals": 18, "symbol": "xUSDz"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#usds", "decimals": 18, "symbol": "USDS"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"decimals": "8", "symbol": "bfBTC", "to": "coingecko#bitcoin"}}, "jbc": {"******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "JUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "OP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "JFIN", "to": "coingecko#jfin-coin"}, "******************************************": {"decimals": "18", "symbol": "KUB", "to": "coingecko#bitkub-coin"}}, "bitkub": {"******************************************": {"decimals": "18", "symbol": "KUB", "to": "coingecko#bitkub-coin"}, "******************************************": {"to": "coingecko#bitkub-coin", "decimals": 18, "symbol": "KKUB"}}, "sanko": {"******************************************": {"name": "Dream Machine Token", "decimals": "18", "symbol": "DMT", "to": "coingecko#dream-machine-token"}, "******************************************": {"decimals": "18", "symbol": "DMT", "to": "coingecko#wrapped-dmt"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#sanko-bridged-weth-sanko"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#sanko-bridged-usdc-sanko"}, "******************************************": {"decimals": "18", "symbol": "BEAT", "to": "coingecko#beat-the-allegations"}}, "aptos": {"0xe4ccb6d39136469f376242c31b34d10515c8eaaa38092f804db8e08a8f53c5b2::assets_v1::EchoCoin002": {"decimals": "6", "symbol": "GUI", "to": "coingecko#gui-inu"}, "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::WBTC": {"decimals": "6", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "0xfaf4e633ae9eb31366c9ca24214231760926576c7b625313b3688b5e900731f6::staking::ThalaAPT": {"decimals": "8", "symbol": "thAPT", "to": "coingecko#thala-apt"}, "0x63be1898a424616367e19bbd881f456a78470e123e2770b5b5dcdceb61279c54::movegpt_token::MovegptCoin": {"decimals": "8", "symbol": "MGPT", "to": "coingecko#movegpt"}, "0xfaf4e633ae9eb31366c9ca24214231760926576c7b625313b3688b5e900731f6::staking::StakedThalaAPT": {"decimals": "8", "symbol": "sthAPT", "to": "coingecko#staked-thala-apt"}, "0xcfea864b32833f157f042618bd845145256b1bf4c0da34a7013b76e42daa53cc::usdy::USDY": {"decimals": "6", "symbol": "USDY", "to": "ethereum:******************************************"}, "0x5e156f1207d0ebfa19a9eeff00d62a282278fb8719f4fab3a586a0a2c0fffbea::coin::T": {"decimals": "6", "symbol": "USDCET", "to": "coingecko#usd-coin-wormhole-from-ethereum"}, "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::amapt_token::AmnisApt": {"decimals": "8", "symbol": "amAPT", "to": "coingecko#aptos"}, "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::stapt_token::StakedApt": {"decimals": "8", "symbol": "stAPT", "to": "coingecko#aptos"}, "0x159df6b7689437016108a019fd5bef736bac692b6d4a1f10c941f6fbb9a74ca6::oft::CakeOFT": {"decimals": "8", "symbol": "CAKE", "to": "coingecko#pancakeswap-token"}, "0x2ebb2ccac5e027a87fa0e2e5f656a3a4238d6a48d93ec9b610d570fc0aa0df12": {"name": "CELLANA", "decimals": "8", "symbol": "CELL", "to": "coingecko#cellena-finance"}, "0xada35ada7e43e2ee1c39633ffccec38b76ce702b4efc2e60b50f63fbe4f710d8::apetos_token::ApetosCoin": {"decimals": "8", "symbol": "APETOS", "to": "coingecko#apetos"}, "0x6f986d146e4a90b828d8c12c14b6f4e003fdff11a8eecceceb63744363eaac01::mod_coin::MOD": {"decimals": "8", "symbol": "MOD", "to": "coingecko#move-dollar"}, "0xaef6a8c3182e076db72d64324617114cacf9a52f28325edc10b483f7f05da0e7": {"decimals": "8", "symbol": "TruAPT", "to": "coingecko#trufin-staked-apt"}, "0x53a30a6e5936c0a4c5140daed34de39d17ca7fcae08f947c02e979cef98a3719::coin::LSD": {"decimals": "8", "symbol": "LSD", "to": "coingecko#pontem-liquidswap"}, "0xd6a49762f6e4f7401ee79be6f5d4111e70db1408966ba1aa204e6e10c9d437ca::bubbles::BubblesCoin": {"decimals": "8", "symbol": "BUBBLES", "to": "coingecko#bubbles-2"}, "0x4e1854f6d332c9525e258fb6e66f84b6af8aba687bbcb832a24768c4e175feec::abtc::ABTC": {"decimals": "10", "symbol": "ABTC", "to": "coingecko#abtc"}, "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b": {"decimals": "6", "symbol": "USDt", "to": "coingecko#tether"}, "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDT": {"decimals": "6", "symbol": "lzUSDT", "to": "coingecko#layerzero-bridged-usdt-aptos"}, "0x5dee1d4b13fae338a1e1780f9ad2709a010e824388efd169171a26e3ea9029bb::stakestone_bitcoin::StakeStoneBitcoin": {"decimals": "8", "symbol": "lzSBTC", "to": "asset#ethereum:******************************************"}, "0x543c5660aa4d496687e2068c11765f04607c4f4b639a83233a9333604fb8ce59::stakestone_ether::StakeStoneEther": {"decimals": "8", "symbol": "lzSTONE", "to": "coingecko#stakestone-ether"}, "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "0xb30a694a344edee467d9f82330bbe7c3b89f440a1ecd2da1f3bca266560fce69": {"to": "coingecko#ethena-staked-usde", "decimals": 6, "symbol": "sUSDe"}, "0xb36527754eb54d7ff55daf13bcb54b42b88ec484bd6f0e3b2e0d1db169de6451": {"to": "coingecko#ami", "decimals": 8, "symbol": "AMI"}, "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::usdt_coin::USDTether": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "zUSDT"}, "0x821c94e69bc7ca058c913b7b5e6b0a5c9fd1523d58723a966fb8c1f5ea888105": {"to": "coingecko#aptos", "decimals": 8, "symbol": "kAPT"}, "0xbcff91abababee684b194219ff2113c26e63d57c8872e6fdaf25a41a45fb7197": {"to": "coingecko#auro-finance", "decimals": 8, "symbol": "AURO"}}, "velas": {"******************************************": {"decimals": "18", "symbol": "VLX", "to": "coingecko#velas"}}, "cardano": {"lovelace": {"decimals": "6", "symbol": "ADA", "to": "coingecko#cardano"}}, "rpg": {"******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WRPG", "to": "coingecko#rangers-protocol-gas"}, "******************************************": {"decimals": "18", "symbol": "AMG", "to": "coingecko#deherogame-amazing-token"}, "******************************************": {"decimals": "18", "symbol": "MIX", "to": "coingecko#mixmarvel"}}, "winr": {"******************************************": {"decimals": "18", "symbol": "WIMR", "to": "coingecko#winr-protocol"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "ARB", "to": "coingecko#arbitrum"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "SOL", "to": "coingecko#solana"}, "******************************************": {"to": "coingecko#winr-protocol", "decimals": 18, "symbol": "WINR"}}, "cronos": {"******************************************": {"name": "NEAR", "decimals": "24", "symbol": "NEAR", "to": "coingecko#near"}, "******************************************": {"name": "WIF", "decimals": "6", "symbol": "WIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"name": "DOGE", "decimals": "8", "symbol": "DOGE", "to": "coingecko#dogecoin"}, "******************************************": {"name": "PEPE", "decimals": "18", "symbol": "PEPE", "to": "coingecko#pepe"}, "******************************************": {"name": "AAVE", "decimals": "18", "symbol": "AAVE", "to": "coingecko#aave"}, "******************************************": {"name": "HBAR", "decimals": "8", "symbol": "HBAR", "to": "coingecko#hedera-hashgraph"}, "0x769409037336430A1a5890065B7853f0D1D8b58f": {"name": "PENGU", "decimals": "6", "symbol": "PENGU", "to": "coingecko#pudgy-penguins"}, "******************************************": {"name": "SUI", "decimals": "9", "symbol": "SUI", "to": "coingecko#sui"}, "******************************************": {"name": "TRUMP", "decimals": "6", "symbol": "TRUMP", "to": "coingecko#official-trump"}}, "scroll": {"******************************************": {"decimals": "18", "symbol": "COMP", "to": "coingecko#compound-governance-token-governance-token"}, "******************************************": {"decimals": "18", "symbol": "PufETH", "to": "coingecko#pufeth"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "rETH", "to": "coingecko#rocket-pool-eth"}, "******************************************": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"to": "coingecko#ethereum", "decimals": "18", "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": "18", "symbol": "ETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": "6", "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-rseth", "decimals": "18", "symbol": "wrsETH"}, "******************************************": {"to": "coingecko#kelp-dao-restaked-eth", "decimals": "18", "symbol": "rsETH"}, "******************************************": {"to": "coingecko#stakestone-ether", "decimals": "18", "symbol": "STONE"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": "18", "symbol": "weETH"}, "******************************************": {"to": "coingecko#impermax-2", "decimals": "18", "symbol": "IBEX"}, "******************************************": {"to": "coingecko#solana", "decimals": "9", "symbol": "wSOL"}, "******************************************": {"to": "coingecko#stack-2", "decimals": "18", "symbol": "MORE"}, "******************************************": {"to": "coingecko#fbomb", "decimals": "18", "symbol": "fBOMB"}}, "radixdlt": {"resource_rdx1tknxxxxxxxxxradxrdxxxxxxxxx009923554798xxxxxxxxxradxrd": {"decimals": "0", "symbol": "XRD", "to": "coingecko#radix"}, "resource_rdx1th88qcj5syl9ghka2g9l7tw497vy5x6zaatyvgfkwcfe8n9jt2npww": {"decimals": "0", "symbol": "ETH", "to": "coingecko#ethereum"}}, "darwinia": {"******************************************": {"to": "coingecko#darwinia-network-native-token", "decimals": "18", "symbol": "RING"}, "******************************************": {"to": "coingecko#darwinia-network-native-token", "decimals": 18, "symbol": "RING"}}, "pg": {"******************************************": {"to": "coingecko#pego-network-2", "decimals": "18", "symbol": "WPG"}}, "mvc": {"******************************************": {"to": "coingecko#microvisionchain", "decimals": "8", "symbol": "MVC"}}, "nos": {"******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": "18", "symbol": "WBTC"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}}, "renec": {"********************************************": {"to": "coingecko#solana", "decimals": "9", "symbol": "SOL"}, "GwGh3b7iNibT3gpGn6SwZA9xZme7Th4NZmuGVD75jpZL": {"to": "coingecko#ethereum", "decimals": "9", "symbol": "ETH"}, "GwPQTMg3eMVpDTEE3daZDtGsBtNHBK3X47dbBJvXUzF4": {"to": "coingecko#bitcoin", "decimals": "9", "symbol": "BTC"}, "7G8x2UZSgVDZzbPSUKGjg2e2YAkMV8zwiP1525yxEK47": {"to": "coingecko#binancecoin", "decimals": "9", "symbol": "BNB"}, "So11111111111111111111111111111111111111112": {"to": "coingecko#renec", "decimals": "9", "symbol": "RENEC"}, "4Q89182juiadeFgGw3fupnrwnnDmBhf7e7fHWxnUP3S3": {"to": "coingecko#tether", "decimals": "9", "symbol": "USDT"}}, "op_bnb": {"******************************************": {"decimals": "8", "symbol": "oBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "uBTC", "to": "asset#bsquared:******************************************"}, "******************************************": {"to": "coingecko#tether", "decimals": "18", "symbol": "USDT"}, "******************************************": {"to": "coingecko#bitcoin-bep2", "decimals": "18", "symbol": "BTCB"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#first-digital-usd", "decimals": 18, "symbol": "FDUSD"}}, "solana": {"GyWgeqpy5GueU2YbkE8xqUeVEokCMMCEeUrfbtMw6phr": {"decimals": "6", "symbol": "BUIDL", "to": "coingecko#blackrock-usd-institutional-digital-liquidity-fund"}, "GBWAm6UZfWEb3PA3nEouKrQ7vyyzhF2EQCFW3VVZEeYo": {"decimals": "9", "symbol": "uSEI", "to": "coingecko#sei"}, "756wWVqA9tpZpxqNxCiJYSCGWi3gD2NXfwKHh4YsYJg9": {"decimals": "9", "symbol": "uSUI", "to": "coingecko#sui"}, "A4ia88XVtXHYT79z8i1qUQNu2o798Mb3R8u2kNQM5xfR": {"decimals": "9", "symbol": "uRNDR", "to": "coingecko#render"}, "FtXuu45uV4zLEQscW1ryZ7x9MjAQimMhs3fsVgdS2FFv": {"decimals": "9", "symbol": "uMKR", "to": "coingecko#maker"}, "BjVUYyyk98GQNX4nyZp1q17mn5YT3jwXuUvkrB7UQp2Z": {"decimals": "9", "symbol": "uSTX", "to": "coingecko#blockstack"}, "AvA86mVZDD9xmwBHwQcG35YPwUKwb3x6MrxdixqRB558": {"decimals": "9", "symbol": "uAPT", "to": "coingecko#aptos"}, "EgQhLNoedF4c4Von9URgTswzdkM7VjmCUf5SgHDHxSj9": {"decimals": "9", "symbol": "uUNI", "to": "coingecko#uniswap"}, "6rcR5iNAcZ7omdakPCY8NJgFMXMfptqNN8YnVsyWqgN9": {"decimals": "9", "symbol": "uMATIC", "to": "coingecko#polygon-ecosystem-token"}, "At4Nm3ckUyRXuyAG92wkVMRFFyBvpds1DGXnksDFL4fU": {"decimals": "9", "symbol": "uLINK", "to": "coingecko#chainlink"}, "5vNWb1TP6qmsfJbqYZ5PUM9EA3dAwGRyjmKfFYJQDfrs": {"decimals": "9", "symbol": "uSHIB", "to": "coingecko#shiba-inu"}, "GVzErJNwwfbaWrDRDk48xhiFNFWFmSB57tBz21iNMWjR": {"decimals": "9", "symbol": "uAVAX", "to": "coingecko#avalanche-2"}, "AzEdMrLSREpBwXVjcNjqKnrHPCWj2DGRDH3RXr3xKdnf": {"decimals": "9", "symbol": "uETH", "to": "coingecko#ethereum"}, "********************************************": {"decimals": "9", "symbol": "uALGO", "to": "coingecko#algorand"}, "CkXSRuC9RGtEkDdDnLC5M1dYJaBSPxNpa3pMDHQkboFR": {"decimals": "9", "symbol": "uBCH", "to": "coingecko#bitcoin-cash"}, "Ghjyzf4yS3pSgPve4VAbvh85Kt3MiMqaBWZiYcqx2AeJ": {"decimals": "9", "symbol": "uADA", "to": "coingecko#cardano"}, "BWtWsfXTsSBGRwHFAx41YTRTLRHj6rm5vkmMbf2hHoQh": {"decimals": "9", "symbol": "uLTC", "to": "coingecko#litecoin"}, "********************************************": {"decimals": "9", "symbol": "uNEAR", "to": "coingecko#near"}, "Epa3ppMfAvqrrLTMdfCh4UdhAJns5Z1cBJ1J6osQPv5D": {"decimals": "9", "symbol": "uDOT", "to": "coingecko#polkadot"}, "BFARNBVWNfZfh3JQJLhogQJ9bkop4Y8LaDHeSxDDk5nn": {"decimals": "9", "symbol": "uDOGE", "to": "coingecko#dogecoin"}, "2jcHBYd9T2Mc9nhvFEBCDuBN1XjbbQUVow67WGWhv6zT": {"decimals": "9", "symbol": "uXRP", "to": "coingecko#xrp"}, "5JgoWCQjXzyYX2NBdHPQBVmnFLeUCZaz8VApm8PQ5a5P": {"decimals": "9", "symbol": "uSOL", "to": "coingecko#solana"}, "Bd2RHVCihrBBcmzvyLBAGCBCHXbB22NAwbw2iGGL4T6V": {"decimals": "9", "symbol": "uBTC", "to": "coingecko#bitcoin"}, "BenJy1n3WTx9mTjEvy63e8Q1j4RqUc6E4VBMz3ir4Wo6": {"decimals": "6", "symbol": "USD*", "to": "coingecko#usd-coin"}, "4tARAT4ssRYhrENCTxxZrmjL741eE2G23Q1zLPDW2ipf": {"decimals": "9", "symbol": "lrtsSOL", "to": "coingecko#solayer-staked-sol"}, "EcyEx8hGsDBHCNkHupvbfZTTtNRbAiXPkmcceNtWGWdt": {"decimals": "9", "symbol": "sUSDe", "to": "coingecko#ethena-staked-usde"}, "Eh6XEPhSwoLv5wFApukmnaVSHQ6sAnoD9BmgmwQoN2sN": {"decimals": "9", "symbol": "sUSDe", "to": "coingecko#ethena-staked-usde"}, "susdabGDNbhrnCa6ncrYo81u4s9GM8ecK2UwMyZiq4X": {"decimals": "6", "symbol": "sUSD", "to": "coingecko#usd-coin"}, "4MmJVdwYN8LwvbGeCowYjSx7KoEi6BJWg8XXnW4fDDp6": {"decimals": "6", "symbol": "TBILL", "to": "coingecko#openeden-tbill"}, "A9mUU4qviSctJVPJdBJWkb28deg915LYJKrzQ19ji3FM": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr": {"decimals": "8", "symbol": "SPX", "to": "coingecko#spx6900"}, "EfqRM8ZGWhDTKJ7BHmFvNagKVu3AxQRDQs8WMMaoBCu6": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "4XN2eji3jLjaoqef4XDT3xj1q9uHK9qHidtag81JmJQp": {"decimals": "6", "symbol": "WETH", "to": "coingecko#weth"}, "********************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "wzbcJyhGhQDLTV1S99apZiiBdE4jmYfbw99saMMdP59": {"decimals": "9", "symbol": "ZBC", "to": "coingecko#zebec-network"}, "8qJSyQprMC57TWKaYEmetUR3UUiTP2M3hXdcvFhkZdmv": {"decimals": "8", "symbol": "USDT", "to": "coingecko#tether"}, "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs": {"decimals": "8", "symbol": "WETH", "to": "coingecko#weth"}, "G7iK3prSzAA4vzcJWvsLUEsdCqzR7PnMzJV61vSdFSNW": {"decimals": "8", "symbol": "NST", "to": "coingecko#ninja-squad"}, "AwRErBEFGTnohzfLeRSBH9HddQEy2oeRxnWLrbvFFh95": {"decimals": "6", "symbol": "TRUMP", "to": "coingecko#maga"}, "BeGY8KqKxboEwRbJd1q9H2K829jS4Rc5dEyNMYXCbV5p": {"decimals": "8", "symbol": "NPC", "to": "coingecko#non-playable-coin"}, "HaP8r3ksG76PhQLTqR8FYBeNiQpejcFbQmiHbg787Ut1": {"decimals": "8", "symbol": "TRUMP", "to": "coingecko#maga"}, "6LNeTYMqtNm1pBFN8PfhQaoLyegAH8GD32WmHU9erXKN": {"decimals": "8", "symbol": "APTOS", "to": "coingecko#aptos"}, "6FVyLVhQsShWVUsCq2FJRr1MrECGShc3QxBwWtgiVFwK": {"decimals": "8", "symbol": "BWB", "to": "coingecko#bitget-wallet-token"}, "SNSNkV9zfG5ZKWQs6x4hxvBRV6s8SqMfSGCtECDvdMd": {"decimals": "9", "symbol": "SNS", "to": "coingecko#synesis-one"}, "FjK6rqU6QzUeNtmK6QQ78cCuS5AHAhcm4HgJkdsvXaep": {"decimals": "8", "symbol": "YOURAI", "to": "coingecko#your-ai"}, "Dn4noZ5jgGfkntzcQSUZ8czkreiZ1ForXYoV2H8Dm7S1": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "CiKu4eHsVrc1eueVQeHn7qhXTcVu95gSQmBpX4utjL9z": {"decimals": "8", "symbol": "SHIB", "to": "coingecko#shiba-inu"}, "25rXTx9zDZcHyTav5sRqM6YBvTGu9pPH9yv83uAEqbgG": {"decimals": "8", "symbol": "tBTC", "to": "coingecko#tbtc"}, "9dzSzFvPsKDoY2gdWErsuz2H1o4tbzvgBhrNZ9cvkD2j": {"decimals": "8", "symbol": "SDEX", "to": "coingecko#smardex"}, "HAxCJjnmgkdXhwZYeJiUvBgm4NdQvqhGJCS3KxCnCxWs": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "9vMJfxuKxXBoEa7rM12mYLMwTacLMLDJqHozw96WQL8i": {"decimals": "6", "symbol": "USDC", "to": "coingecko#terrausd-wormhole"}, "i7u4r16TcsJTgq1kAG8opmVZyVnAKBwLKu6ZPMwzxNc": {"decimals": "6", "symbol": "OUSG", "to": "coingecko#ousg"}, "ZScHuTtqZukUrtZS43teTKGs2VqkKL8k4QCouR2n6Uo": {"decimals": "8", "symbol": "wSTETH", "to": "coingecko#ethereum:******************************************"}, "A1KLoBrKBde8Ty9qtNQUtq3C2ortoC3u7twggz7sEto6": {"decimals": "6", "symbol": "USDY", "to": "coingecko#ondo-finance-us-dollar-yield"}, "AZsHEMXd36Bj1EMNXhowJajpUXzrKcK57wW4ZGXVa7yR": {"decimals": "5", "symbol": "GUAC", "to": "coingecko#guacamole"}}, "ton": {"EQCvaf0JMrv6BOvPpAgee08uQM_uRpUd__fhA7Nm8twzvbE_": {"decimals": "9", "symbol": "UP", "to": "coingecko#up"}, "EQAvlWFDxGF2lXm67y4yzC17wYKD9A0guwPkMs1gOsM__NOT": {"decimals": "9", "symbol": "NOT", "to": "coingecko#notcoin"}, "EQBlqsm144Dq6SjbPI4jjZvA1hqTIP3CvHovbIfW_t-SCALE": {"decimals": "9", "symbol": "SCALE", "to": "coingecko#scaleton"}, "EQA2kCVNwVsil2EM2mB0SkXytxCqQjS4mttjDpnXmwG9T6bO": {"decimals": "9", "symbol": "STON", "to": "coingecko#ston-2"}, "EQCvxJy4eG8hyHBFsZ7eePxrRsUQSFE_jpptRAYBmcG_DOGS": {"decimals": "9", "symbol": "DOGS", "to": "coingecko#dogs-2"}, "EQBZ_cafPyDr5KUTs0aNxh0ZTDhkpEZONmLJA2SNGlLm4Cko": {"decimals": "9", "symbol": "REDO", "to": "coingecko#resistance-dog"}, "EQAQXlWJvGbbFfE8F3oS8s87lIgdovS455IsWFaRdmJetTon": {"decimals": "9", "symbol": "JETTON", "to": "coingecko#jetton"}, "EQBq4d4GPyBoh-Pjnf3wxUyQSS28WY2Yt-7cPAG8FHpWpNRX": {"to": "coingecko#matic-network", "decimals": "18", "symbol": "MATIC"}, "EQDCIEo0HUUYsAV-lTMviOd-GkSXfVPsNZMGjRaNOA_6--FD": {"to": "coingecko#orbit-chain", "decimals": "18", "symbol": "ORC"}, "EQBf6-YoR9xylol_NwjHrLkrTFAZJCX-bsd-Xx_902OaPaBf": {"to": "coingecko#megaton-finance", "decimals": "9", "symbol": "MEGA"}, "EQCajaUU1XXSAjTD-xOV7pE49fGtg4q8kF3ELCOJtGvQFQ2C": {"to": "coingecko#the-open-network", "decimals": "9", "symbol": "TON"}, "EQCf7Nb341dxOE3N0jimngRxGEV8T3zo-eU2EZVs_nchNhhZ": {"to": "coingecko#wemix-token", "decimals": "18", "symbol": "WEMIX"}, "0:9bd52017d6178af6d2bc6b9097b5849b7475e43cf01758a4f2dd8cf93c7d6582": {"to": "coingecko#ethereum", "decimals": 9, "symbol": "jETH"}, "0:9b9117699eb5997e6d3b74fe67ff08c6c7a9653641cd21d999fa0a4841c0cff8": {"to": "coingecko#bitcoin", "decimals": 9, "symbol": "jBTC"}}, "mantle": {"******************************************": {"to": "coingecko#mantle", "decimals": "18", "symbol": "MNT"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": "18", "symbol": "USDe"}, "******************************************": {"to": "mantle:******************************************", "decimals": "18", "symbol": "fsKLP"}, "******************************************": {"to": "mantle:******************************************", "decimals": "18", "symbol": "fKLP"}, "******************************************": {"to": "coingecko#puff-the-dragon", "decimals": "18", "symbol": "PUFF"}, "******************************************": {"to": "coingecko#yield-yak", "decimals": "18", "symbol": "YAK"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": "8", "symbol": "FBTC"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-Avalon-mnt", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-bedrock-mnt", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 18, "symbol": "uniBTC"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-pump-mnt"}}, "manta": {"******************************************": {"to": "coingecko#mountain-protocol-usdm", "decimals": "18", "symbol": "wUSDM"}, "******************************************": {"to": "coingecko#ethereum", "decimals": "18", "symbol": "ETH"}, "******************************************": {"to": "coingecko#stakestone-ether", "decimals": "18", "symbol": "STONE"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": "6", "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": "6", "symbol": "USDT"}, "******************************************": {"to": "coingecko#celestia", "decimals": "6", "symbol": "TIA"}, "******************************************": {"to": "coingecko#goku-money-gai", "decimals": "18", "symbol": "GAI"}, "******************************************": {"to": "coingecko#manta-network", "decimals": "18", "symbol": "MANTA"}, "******************************************": {"to": "coingecko#anzen-usdz", "decimals": "18", "symbol": "USDz"}}, "beam": {"******************************************": {"to": "coingecko#usd-coin", "decimals": "6", "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": "6", "symbol": "USDT"}, "******************************************": {"to": "coingecko#beam-2", "decimals": "18", "symbol": "WBEAM"}, "******************************************": {"to": "coingecko#avalanche-2", "decimals": "18", "symbol": "AVAX"}, "******************************************": {"to": "coingecko#raini-studios-token", "decimals": "18", "symbol": "RST"}, "******************************************": {"to": "coingecko#goons-of-balatroon", "decimals": "18", "symbol": "GOB"}, "******************************************": {"to": "coingecko#nekoverse-city-of-greed-anima-spirit-gem", "decimals": "18", "symbol": "ASG"}, "******************************************": {"to": "coingecko#beamcat", "decimals": "18", "symbol": "BCAT"}}, "shimmer_evm": {"******************************************": {"to": "coingecko#shimmer", "decimals": "6", "symbol": "WSMR"}, "******************************************": {"to": "coingecko#shimmer", "decimals": "18", "symbol": "WSMR"}, "******************************************": {"to": "coingecko#shimmer", "decimals": "18", "symbol": "WSMR"}, "******************************************": {"to": "coingecko#soonaverse", "decimals": "6", "symbol": "SOONA"}, "******************************************": {"to": "coingecko#tether", "decimals": "18", "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": "18", "symbol": "ETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": "8", "symbol": "WBTC"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "******************************************": {"decimals": "18", "symbol": "AVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "FTM", "to": "coingecko#fantom"}}, "bfc": {"******************************************": {"decimals": "18", "symbol": "WBFC", "to": "coingecko#bifrost"}, "******************************************": {"decimals": "18", "symbol": "BIFI", "to": "coingecko#bifi"}, "******************************************": {"decimals": "18", "symbol": "WITCH", "to": "coingecko#witch-token"}, "******************************************": {"decimals": "18", "symbol": "SAT", "to": "coingecko#super-athletes-token"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BTC.b", "to": "coingecko#bitcoin-avalanche-bridged-btc-b"}, "******************************************": {"to": "coingecko#bitcoin-usd-btcfi", "decimals": 18, "symbol": "BtcUSD"}, "******************************************": {"to": "coingecko#coinbase-wrapped-btc", "decimals": 8, "symbol": "cbBTC"}}, "elysium": {"******************************************": {"decimals": "18", "symbol": "PYR", "to": "coingecko#vulcan-forged"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "WMATIC", "to": "coingecko#wmatic"}, "******************************************": {"decimals": "18", "symbol": "MV", "to": "coingecko#gensokishis-metaverse"}}, "near": {"17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}}, "osmosis": {"osmo1z6r6qdknhgsc0zeracktgpcxf43j6sekq07nw8sxduc9lg0qjjlqfu25e3:alloyed:allBTC": {"to": "coingecko#osmosis-allbtc", "decimals": 6, "symbol": "allBTC"}, "osmo1n3n75av8awcnw4jl62n3l48e6e4sxqmaf97w5ua6ddu4s475q5qq9udvx4:alloyed:allSOL": {"to": "coingecko#osmosis-allsol", "decimals": 6, "symbol": "allSOL"}}, "ibc": {"694A6B26A43A2FBECCFFEAC022DEACB39578E54207FDD32005CD976B57B98004": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "B870E6642B6491779D35F326A895780FC2F7409DFD7F639A98505555AEAF345F": {"to": "coingecko#mantra-dao", "decimals": 6, "symbol": "MANTRA"}, "68B5E8DA9270FA00245484BB1C07AD75399AD67D54A1344F6E998B5FB69B664F": {"to": "coingecko#pax-gold", "decimals": 18, "symbol": "PAXG"}, "147B3FF1D005512CCE4089559AF5D0C951F4211A031F15E782E505B85022DF89": {"to": "coingecko#kava", "decimals": 6, "symbol": "KAVA"}, "8BFE59DCD5A7054F0A97CF91F3E3ABCA8C5BA454E548FA512B729D4004584D47": {"to": "coingecko#neutron-3", "decimals": 6, "symbol": "NTRN"}, "E3459360643C2555C57C7DAB0567FA762B42D5D6D45A76615EA7D99D933AEC04": {"to": "coingecko#secret", "decimals": 6, "symbol": "SCRT"}, "343182F79E6450836403252D1122288D480605885A01426085859B43F5ECD3EF": {"to": "coingecko#babylon", "decimals": 6, "symbol": "BABY"}, "B0BEE773CEF718E8B6D2892A27D57FEFB00BD8B2E698367B5CC96A4F36148726": {"decimals": "6", "symbol": "BABY", "to": "coingecko#babylon"}, "37A3FB4FED4CA04ED6D9E5DA36C6D27248645F0E22F585576A1488B8A89C5A50": {"decimals": "6", "symbol": "INIT", "to": "coingecko#initia"}, "F082B65C88E4B6D5EF1DB243CDA1D331D002759E938A0F5CD3FFDC5D53B3E349": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "6BFB09FE2464A7681645610F56BBEFF555A00B8AE146339FEB4609BF40FB0F4A": {"to": "coingecko#xion-2", "decimals": 6, "symbol": "XION"}, "B88C39AD6C8550716DFD64C3AD28F355633554821249AC9F8BCC21341641CD18": {"to": "coingecko#saga-2", "decimals": 6, "symbol": "SAGA"}, "0E293A7622DC9A6439DB60E6D234B5AF446962E27CA3AB44D0590603DFF6968E": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "25418646C017D377ADF3202FF1E43590D0DAE3346E594E8D78176A139A928F88": {"to": "coingecko#cosmos", "decimals": 6, "symbol": "ATOM"}, "D8A36AE90F20FE4843A8D249B1BCF0CCDDE35C4B605C8DED57BED20C639162D0": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "45D6B52CAD911A15BD9C2F5FFDA80E26AFCB05C7CD520070790ABC86D2B24229": {"to": "coingecko#celestia", "decimals": 6, "symbol": "TIA"}, "65D0BEC6DAD96C7F5043D1E54E54B6BB5D5B3AEC3FF6CEBB75B9E059F3580EA3": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "9117A26BA81E29FA4F78F57DC2BD90CD3D26848101BA880445F119B22A1E254E": {"decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "698350B8A61D575025F3ED13E9AC9C0F45C89DEFE92F76D5838F1D3C1A7FF7C9": {"decimals": "6", "symbol": "stTIA", "to": "coingecko#stride-staked-tia"}, "FA602364BEC305A696CBDF987058E99D8B479F0318E47314C49173E8838C5BAC": {"decimals": "6", "symbol": "qATOM", "to": "coingecko#qatom"}, "56D7C03B8F6A07AD322EEE1BEF3AE996E09D1C1E34C27CF37E0D4A0AC5972516": {"decimals": "12", "symbol": "PICA", "to": "coingecko#picasso"}, "6B2B19D874851F631FF0AF82C38A20D4B82F438C7A22F41EDA33568345397244": {"decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "980E82A9F8E7CA8CD480F4577E73682A6D3855A267D1831485D7EBEF0E7A6C2C": {"decimals": "18", "symbol": "stDYDX", "to": "coingecko#stride-staked-dydx"}, "71B441E27F1BBB44DD0891BCD370C2794D404D60A4FFE5AECCD9B1E28BC89805": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "8CB56C813A5C2387140BBEAABCCE797AFA0960C8D07B171F71A5188726CFED2C": {"decimals": "6", "symbol": "BLD", "to": "coingecko#agoric"}, "CFD58F8A64F93940D00CABE85B05A6D0FBA1FF4DF42D3C1E23C06DF30A2BAE1F": {"decimals": "18", "symbol": "PLQ", "to": "coingecko#planq"}, "C2CFB1C37C146CF95B0784FD518F8030FEFC76C5800105B1742FB65FFE65F873": {"decimals": "6", "symbol": "AKT", "to": "coingecko#akash-network"}, "C0336ECF2DF64E7D2C98B1422EC2B38DE9EF33C34AAADF18C6F2E3FFC7BE3615": {"decimals": "6", "symbol": "IST", "to": "coingecko#inter-stable-token"}, "43897B9739BD63E3A08A88191999C632E052724AB96BD4C74AE31375C991F48D": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "E3409E92F78AE5BF44DBC7C4741901E21EF73B7B8F98C4D48F2BD360AF242C00": {"decimals": "6", "symbol": "DEC", "to": "coingecko#decentr"}, "FCB240D2C3838369155A0FA8970A8BE3EC1042F698269B9D6D9859274F00A0BB": {"decimals": "6", "symbol": "BCNA", "to": "coingecko#bitcanna"}, "3A2DEEBCD51D0B74FE7CE058D40B0BF4C0E556CE9219E8F25F92CF288FF35F56": {"decimals": "8", "symbol": "axlWBTC", "to": "coingecko#wrapped-bitcoin"}, "E070901F36B129933202BEB3EB40A78BE242D8ECBA2D1AF9161DF06F35783900": {"decimals": "6", "symbol": "ALTER", "to": "coingecko#alter"}, "8E6E7AB89246F87DA936F0EEA0A40654E7FB6B0C3E834F447EB444AAD95A106F": {"decimals": "6", "symbol": "FLIX", "to": "coingecko#omniflix-network"}, "BF8BDCAA292B56035E669D80711D9881CC96796AC6BCB0376836FAD045355E37": {"decimals": "6", "symbol": "CMDX", "to": "coingecko#comdex"}, "31D711D31CD5D83D98E76B1486EEDA1A38CD1F7D6FCBD03521FE51323115AECA": {"decimals": "6", "symbol": "GRAV", "to": "coingecko#graviton"}, "6F681B32B47E765AE278844A8A97C8B7AD5A3FB591E37CFDFA0E208EFEBEF97D": {"decimals": "6", "symbol": "USDC.grav", "to": "coingecko#usd-coin"}, "7F6928F9DB35D5A420330B9470EA4419B5F305EEB668D3274FC1ACDAC325ED17": {"decimals": "6", "symbol": "USDT.grav", "to": "coingecko#tether"}, "381C09E1B743A8D893DF43B0861FE77AB2C437ED4BE10CB827A0A38285C11977": {"decimals": "8", "symbol": "SHD", "to": "coingecko#shade-protocol"}, "55D94A32095A766971637425D998AAABF8357A1ABCB1CAC8614887BE51BF1FB1": {"decimals": "6", "symbol": "ANDR", "to": "coingecko#andromeda-2"}, "6569E05DEE32B339D9286A52BE33DFCEFC97267F23EF9CFDE0C055140967A9A5": {"decimals": "6", "symbol": "STTIA", "to": "coingecko#stride-staked-tia"}, "6A9571DE6A3F60D7703C3290E2944E806C15A47C1EA6D4AFCD3AE4DC8AF080B1": {"decimals": "6", "symbol": "LVN", "to": "coingecko#levana-protocol"}, "27394FB092D2ECCD56123C74F36E4C1F926001CEADA9CA97EA622B25F41E5EB2": {"decimals": "6", "symbol": "ATOM", "to": "coingecko#cosmos"}, "4FC395BDCDCD4A3A681C0D222EE64B610AEDB215DB8D18F46B575357F27CD6A7": {"decimals": "6", "symbol": "PASG", "to": "coingecko#passage"}, "0471F1C4E7AFD3F07702BEF6DC365268D64570F7C1FDC98EA6098DD6DE59817B": {"decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "926432AE1C5FA4F857B36D970BE7774C7472079506820B857B75C5DE041DD7A3": {"decimals": "6", "symbol": "JKL", "to": "coingecko#jackal-protocol"}, "E6208E49B5FA2353C9C47D2EB9AABA03FED788E4970C7C623F6B511263E7CB11": {"decimals": "6", "symbol": "BTSG", "to": "coingecko#bitsong"}, "B559A80D62249C8AA07A380E2A2BEA6E5CA9A6F079C912C3A9E9B494105E4F81": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "DF8722298D192AAB85D86D0462E8166234A6A9A572DD4A2EA7996029DF4DB363": {"decimals": "8", "symbol": "axlWBTC", "to": "coingecko#wrapped-bitcoin"}, "FBDF85EF021068F5618F8E1017E1DF4CB235C20ECE324D3FE40FAAEA3B2AD878": {"decimals": "6", "symbol": "stkOSMO", "to": "coingecko#pstake-staked-osmo"}, "EEEF1FE5F4C01CAF351AAC0F6AFFC3C777578DE4D5B1E257091FE185F5991F15": {"decimals": "6", "symbol": "DVPN", "to": "coingecko#sentinel"}, "FCFF8B19C61677F3B78E2A5AE3B4A34A8D23858D16905F253B8438B3AFD07FF8": {"decimals": "6", "symbol": "stATOM", "to": "coingecko#stride-staked-atom"}, "E45CFCB959F4F6D1065B7033EE49A88E606E6AD82E75725219B3D68B0FA89987": {"decimals": "8", "symbol": "allBTC", "to": "coingecko#osmosis-allbtc"}, "762E1E45658845A12E214A91C3C05FDFC5951D60404FAADA225A369A96DCD9A9": {"decimals": "9", "symbol": "allSOL", "to": "coingecko#osmosis-allsol"}, "ADC63C00000CA75F909D2BE3ACB5A9980BED3A73B92746E0FCE6C67414055459": {"decimals": "6", "symbol": "AKT", "to": "coingecko#akash-network"}, "factory:osmo1z6r6qdknhgsc0zeracktgpcxf43j6sekq07nw8sxduc9lg0qjjlqfu25e3:alloyed:allBTC": {"decimals": "8", "symbol": "allBTC", "to": "coingecko#osmosis-allbtc"}, "factory:osmo1n3n75av8awcnw4jl62n3l48e6e4sxqmaf97w5ua6ddu4s475q5qq9udvx4:alloyed:allSOL": {"decimals": "9", "symbol": "allSOL", "to": "coingecko#osmosis-allsol"}, "factory:neutron1k6hr0f83e7un2wjf29cspk7j69jrnskk65k3ek2nj9dztrlzpj6q00rtsa:udatom": {"decimals": "6", "symbol": "dATOM", "to": "coingecko#drop-staked-atom"}, "factory:osmo1f5vfcph2dvfeqcqkhetwv75fda69z7e5c2dldm3kvgj23crkv6wqcn47a0:umilkTIA": {"decimals": "6", "symbol": "milkTIA", "to": "coingecko#milkyway-staked-tia"}, "987C17B11ABC2B20019178ACE62929FE9840202CE79498E29FE8E5CB02B7C0A4": {"decimals": "6", "symbol": "STARS", "to": "coingecko#stargaze"}, "DE63D8AC34B752FB7D4CAA7594145EDE1C9FC256AC6D4043D0F12310EB8FC255": {"decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol-protocol"}, "13B2C536BB057AC79D5616B8EA1B9540EC1F2170718CAFF6F0083C966FFFED0B": {"decimals": "6", "symbol": "OSMO", "to": "coingecko#osmosis"}, "BF28D9C17E0306B194D50F51C3B2590BEAD15E04E03ADD34C3A26E62D85C9676": {"decimals": "6", "symbol": "TIA", "to": "coingecko#celestia"}, "B8AF5D92165F35AB31F3FC7C7B444B9D240760FA5D406C49D24862BD0284E395": {"decimals": "6", "symbol": "LUNA", "to": "coingecko#terra-luna-2"}, "BFAAB7870A9AAABF64A7366DAAA0B8E5065EAA1FCE762F45677DC24BE796EF65": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "B9E4FD154C92D3A23BEA029906C4C5FF2FE74CB7E3A058290B77197A263CF88B": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "120DC39B61CC121E91525C1D51624E41BBE74C537D7B0BE50BBFF9A00E37B6EE": {"decimals": "18", "symbol": "stDYDX", "to": "coingecko#stride-staked-dydx"}, "FA78980867B7E87F382CDA00275C55DDC248CABC7DEE27AC6868CCF97DD5E02F": {"decimals": "6", "symbol": "stTIA", "to": "coingecko#stride-staked-tia"}, "F8CA5236869F819BC006EEF088E67889A26E4140339757878F0F4E229CDDA858": {"decimals": "18", "symbol": "DYDX", "to": "coingecko#dydx-chain"}, "EA6E1E8BA2EB9F681C4BD12C8C81A46530A62934F2BD561B120A00F46946CE87": {"decimals": "6", "symbol": "dATOM", "to": "coingecko#drop-staked-atom"}, "3FC3D99F9E5003057353AD968A6E3AC12AE50741B41441666BAB3890485C9D00": {"decimals": "18", "symbol": "mBTC", "to": "coingecko#midas-btc-yield-token"}, "925137DE4F77A2F26A5FA5051200437F43AFBBC39A4F443968C8E93B756AFB3B": {"decimals": "18", "symbol": "USDY", "to": "coingecko#ondo-finance-us-dollar-yield"}, "C9D6B804F68F218448D7D2AF5D26C0E6E93CD16F99D1695E77B5BE5CBD7D5ADB": {"decimals": "18", "symbol": "sUSDS", "to": "coingecko#susds"}, "88386AC48152D48B34B082648DF836F975506F0B57DBBFC10A54213B1BF484CB": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}}, "zeta": {"******************************************": {"decimals": "8", "symbol": "CBBTC.ETH", "to": "coingecko#coinbase-wrapped-btc"}, "******************************************": {"decimals": "6", "symbol": "USDT.ETH", "to": "coingecko#tether"}, "******************************************": {"decimals": "9", "symbol": "SOL.SOL", "to": "coingecko#solana"}, "******************************************": {"decimals": "8", "symbol": "CBBTC.SOL", "to": "coingecko#coinbase-wrapped-btc"}, "******************************************": {"decimals": "6", "symbol": "USDC.SOL", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT.SOL", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "BASE.USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDC.ARB", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "BASE.ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "pufETH", "to": "coingecko#pufeth"}, "******************************************": {"decimals": "18", "symbol": "UTIL.BSC", "to": "coingecko#ultiverse"}, "******************************************": {"decimals": "18", "symbol": "ULTI.ETH", "to": "coingecko#ultiverse"}, "******************************************": {"decimals": "18", "symbol": "wzeta", "to": "coingecko#zetachain"}}, "eos_evm": {"******************************************": {"decimals": "18", "symbol": "WEOS", "to": "coingecko#eos"}}, "eon": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "ZEN", "to": "coingecko#zencash"}, "******************************************": {"decimals": "18", "symbol": "WAVAX", "to": "coingecko#wrapped-avax"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "18", "symbol": "LINK", "to": "coingecko#chainlink"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}}, "zilliqa": {"******************************************": {"decimals": "18", "symbol": "ZIL", "to": "coingecko#zilliqa"}, "******************************************": {"decimals": "18", "symbol": "WZIL", "to": "coingecko#zilliqa"}, "******************************************": {"decimals": "6", "symbol": "zUSDT", "to": "coingecko#tether"}, "******************************************": {"to": "coingecko#stzil", "decimals": 12, "symbol": "stZIL"}, "******************************************": {"to": "coingecko#governance-zil", "decimals": 15, "symbol": "gZIL"}, "******************************************": {"to": "coingecko#xcad-network", "decimals": 18, "symbol": "XCAD"}, "******************************************": {"to": "coingecko#kalijo", "decimals": 18, "symbol": "SEED"}, "******************************************": {"to": "coingecko#lunr-token", "decimals": 4, "symbol": "Lunr"}, "******************************************": {"to": "coingecko#zilpepe", "decimals": 18, "symbol": "ZILPEPE"}, "******************************************": {"to": "coingecko#zilstream", "decimals": 8, "symbol": "STREAM"}, "******************************************": {"to": "coingecko#web3war", "decimals": 12, "symbol": "FPS"}}, "ethf": {"******************************************": {"decimals": "18", "symbol": "ETHF", "to": "coingecko#ethereumfair"}, "******************************************": {"decimals": "18", "symbol": "WETHF", "to": "coingecko#ethereumfair"}}, "chz": {"******************************************": {"decimals": "18", "symbol": "CHZ", "to": "coingecko#chiliz"}, "******************************************": {"decimals": "18", "symbol": "WCHZ", "to": "coingecko#chiliz"}, "******************************************": {"decimals": "18", "symbol": "WCHZ", "to": "coingecko#wrapped-chiliz"}, "******************************************": {"to": "coingecko#pepper", "decimals": 18, "symbol": "PEPPER"}}, "edg": {"******************************************": {"decimals": "18", "symbol": "EDG", "to": "coingecko#edgeware"}, "******************************************": {"decimals": "18", "symbol": "WEDG", "to": "coingecko#edgeware"}}, "elsm": {"******************************************": {"decimals": "18", "symbol": "LAVA", "to": "coingecko#lava"}, "******************************************": {"decimals": "18", "symbol": "WLAVA", "to": "coingecko#lava"}, "******************************************": {"decimals": "18", "symbol": "PYR", "to": "coingecko#vulcan-forged"}}, "meer": {"******************************************": {"decimals": "18", "symbol": "MEER", "to": "coingecko#qitmeer-network"}, "0x470cBFB236860eb5257bBF78715FB5bd77119C2F": {"decimals": "18", "symbol": "MEER", "to": "coingecko#qitmeer-network"}, "******************************************": {"decimals": "18", "symbol": "MEER", "to": "coingecko#qitmeer-network"}}, "neon_evm": {"0x202c35e517fa803b537565c40f0a6965d7204609": {"decimals": "18", "symbol": "WNEON", "to": "coingecko#neon"}, "******************************************": {"decimals": "18", "symbol": "NEON", "to": "coingecko#neon"}, "******************************************": {"decimals": "18", "symbol": "MORA", "to": "coingecko#mora"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "orai": {"orai": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decimals": "6", "symbol": "ORAI", "to": "coingecko#oraichain-token"}, "orai12hzjxfh77wl572gdzct2fxv2arxcwh6gykc7qh": {"name": "USDT", "decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "orai1gzvndtzceqwfymu2kqhta2jn6gmzxvzqwdgvjw": {"name": "milky-token", "decimals": "6", "symbol": "milky-token", "to": "coingecko#milky-token"}, "orai1nd4r053e3kgedgld2ymen8l9yrw8xpjyaal7j5": {"name": "kawaii-islands", "decimals": "6", "symbol": "kawaii-islands", "to": "coingecko#kawaii-islands"}, "orai10ldgzued6zjp0mkqwsv2mux3ml50l97c74x8sg": {"name": "airight", "decimals": "6", "symbol": "airight", "to": "coingecko#airight"}, "orai1lus0f0rhx8s03gdllx2n6vhkmf0536dv57wfge": {"name": "oraidex", "decimals": "6", "symbol": "oraidex", "to": "coingecko#oraidex"}, "orai1065qe48g7aemju045aeyprflytemx7kecxkf5m7u5h5mphd0qlcs47pclp": {"name": "scorai", "decimals": "6", "symbol": "scorai", "to": "coingecko#scorai"}, "orai10g6frpysmdgw5tdqke47als6f97aqmr8s3cljsvjce4n5enjftcqtamzsd": {"name": "bitcoin", "decimals": "6", "symbol": "BTC", "to": "coingecko#bitcoin"}, "orai15un8msx3n5zf9ahlxmfeqd2kwa5wm0nrpxer304m9nd5q6qq0g6sku5pdd": {"name": "USD Coin", "decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "orai19rtmkk6sn4tppvjmp5d5zj6gfsdykrl5rw2euu5gwur3luheuuusesqn49": {"name": "injective-protocol", "decimals": "6", "symbol": "INJ", "to": "coingecko#injective-protocol-protocol"}, "orai1c7tpjenafvgjtgm9aqwm7afnke6c56hpdms8jc6md40xs3ugd0es5encn0": {"name": "wTRX", "decimals": "6", "symbol": "TRX", "to": "coingecko#tron"}, "orai1dqa52a7hxxuv8ghe7q5v0s36ra0cthea960q2cukznleqhk0wpnshfegez": {"name": "WETH", "decimals": "6", "symbol": "WETH", "to": "coingecko#weth"}, "orai1hn8w33cqvysun2aujk5sv33tku4pgcxhhnsxmvnkfvdxagcx0p8qa4l98q": {"name": "OCH", "decimals": "6", "symbol": "OCH", "to": "coingecko#och"}, "orai19q4qak2g3cj2xc2y3060t0quzn3gfhzx08rjlrdd3vqxhjtat0cq668phq": {"name": "SCATOM", "decimals": "6", "symbol": "SCATOM", "to": "coingecko#orchai-protocol-staked-compound-atom"}, "factory:orai1wuvhex9xqs3r539mvc6mtm7n20fcj3qr2m0y9khx6n5vtlngfzes3k0rq9:ton": {"name": "TON", "decimals": "9", "symbol": "TON", "to": "coingecko#the-open-network"}}, "xdai": {"******************************************": {"decimals": "18", "symbol": "USDA", "to": "coingecko#usda-2"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "18", "symbol": "wxHOPR", "to": "xdai:******************************************"}, "******************************************": {"to": "coingecko#polkamarkets", "decimals": 18, "symbol": "POLK"}, "******************************************": {"to": "coingecko#savings-dai", "decimals": 18, "symbol": "sDAI"}}, "mode": {"******************************************": {"decimals": "18", "symbol": "uBTC", "to": "asset#bsquared:******************************************"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "PEAS", "to": "coingecko#peapods-finance"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ezETH", "to": "coingecko#renzo-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "weETH", "to": "coingecko#wrapped-eeth"}, "******************************************": {"decimals": "18", "symbol": "STONE", "to": "coingecko#tranquil-staked-one"}, "******************************************": {"decimals": "18", "symbol": "wrsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "mBTC", "to": "coingecko#merlin-s-seal-btc"}, "******************************************": {"decimals": "18", "symbol": "cSTONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "weETH.m", "to": "coingecko#wrapped-eeth"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "8", "symbol": "oBTC", "to": "coingecko#bitcoin"}}, "fsc": {"******************************************": {"decimals": "18", "symbol": "FSC", "to": "coingecko#fonsmartchain"}, "******************************************": {"decimals": "18", "symbol": "WFSC", "to": "coingecko#fonsmartchain"}}, "lightlink_phoenix": {"******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "BUSD.b", "to": "coingecko#binance-usd"}, "******************************************": {"decimals": "18", "symbol": "WBNB.b", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "CSIX.b", "to": "coingecko#carbon-browser"}, "******************************************": {"decimals": "18", "symbol": "LL", "to": "coingecko#lightlink"}}, "linea": {"******************************************": {"decimals": "18", "symbol": "frxUSD", "to": "coingecko#frax-usd"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "18", "symbol": "DUSD", "to": "coingecko#davos-protocol"}, "******************************************": {"decimals": "18", "symbol": "LYNX", "to": "coingecko#lynex"}, "******************************************": {"decimals": "18", "symbol": "agEUR", "to": "coingecko#ageur"}, "******************************************": {"decimals": "18", "symbol": "ezETH", "to": "coingecko#renzo-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "GRAI", "to": "coingecko#grai"}, "******************************************": {"decimals": "18", "symbol": "SDAI", "to": "coingecko#savings-dai"}, "******************************************": {"decimals": "18", "symbol": "M-BTC", "to": "coingecko#merlin-s-seal-btc"}, "******************************************": {"decimals": "18", "symbol": "LUSD", "to": "coingecko#liquity-usd"}}, "wan": {"******************************************": {"decimals": "18", "symbol": "WAN", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "18", "symbol": "WWAN", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "6", "symbol": "ADA", "to": "coingecko#cardano"}, "******************************************": {"decimals": "18", "symbol": "wanAVAX", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "wanBNB", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "8", "symbol": "wanBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "wan<PERSON>i", "to": "coingecko#cyberfi"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "8", "symbol": "wanDOGE", "to": "coingecko#dogecoin"}, "******************************************": {"decimals": "10", "symbol": "wanDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "wanETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "2", "symbol": "EURS", "to": "coingecko#stasis-eurs"}, "******************************************": {"decimals": "18", "symbol": "wanFTM", "to": "coingecko#fantom"}, "******************************************": {"decimals": "18", "symbol": "FX", "to": "coingecko#fx-coin"}, "******************************************": {"decimals": "18", "symbol": "GERO", "to": "coingecko#gerowallet"}, "******************************************": {"decimals": "18", "symbol": "GLMR", "to": "coingecko#moonbeam"}, "******************************************": {"decimals": "18", "symbol": "wanLINK", "to": "coingecko#chainlink"}, "******************************************": {"decimals": "8", "symbol": "wanLTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "wanMOVR", "to": "coingecko#moonriver"}, "******************************************": {"decimals": "6", "symbol": "wanUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "wanUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "XDC", "to": "coingecko#xdce-crowd-sale"}, "******************************************": {"decimals": "6", "symbol": "wanXRP", "to": "coingecko#ripple"}}, "okexchain": {"******************************************": {"decimals": "18", "symbol": "AAVEw", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "AVAXw", "to": "coingecko#avalanche-2"}, "******************************************": {"decimals": "18", "symbol": "BALw", "to": "coingecko#balancer"}, "******************************************": {"decimals": "18", "symbol": "BNBw", "to": "coingecko#binancecoin"}, "******************************************": {"decimals": "18", "symbol": "CRVw", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "DAIw", "to": "coingecko#dai"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "MATICw", "to": "coingecko#matic-network"}, "******************************************": {"decimals": "6", "symbol": "USDCw", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WAN", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "18", "symbol": "XDCw", "to": "coingecko#xdce-crowd-sale"}, "******************************************": {"decimals": "6", "symbol": "XRPw", "to": "coingecko#ripple"}}, "xdc": {"******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "8", "symbol": "xBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "xUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "xUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WAN", "to": "coingecko#wanchain"}, "******************************************": {"decimals": "6", "symbol": "xXRP", "to": "coingecko#ripple"}, "******************************************": {"decimals": "18", "symbol": "FXD", "to": "coingecko#fathom-dollar"}, "******************************************": {"decimals": "2", "symbol": "EURS", "to": "coingecko#stasis-eurs"}, "******************************************": {"decimals": "18", "symbol": "FTHM", "to": "coingecko#fathom-protocol"}, "******************************************": {"to": "coingecko#comtech-gold", "decimals": 18, "symbol": "CGO"}}, "telos": {"******************************************": {"decimals": "8", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "moonriver": {"******************************************": {"decimals": "8", "symbol": "DOGE", "to": "coingecko#dogecoin"}, "******************************************": {"decimals": "18", "symbol": "GLMR", "to": "coingecko#moonbeam"}, "******************************************": {"decimals": "18", "symbol": "MOVR", "to": "coingecko#moonriver"}, "******************************************": {"decimals": "12", "symbol": "BNC", "to": "coingecko#bifrost-native-coin"}, "******************************************": {"decimals": "12", "symbol": "KSM", "to": "coingecko#kusama"}, "******************************************": {"to": "coingecko#polkamarkets", "decimals": 18, "symbol": "POLK"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}}, "moonbeam": {"******************************************": {"decimals": "18", "symbol": "GLMR", "to": "coingecko#moonbeam"}, "******************************************": {"decimals": "10", "symbol": "xcvDOT", "to": "coingecko#voucher-dot"}, "******************************************": {"decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "MOVR", "to": "coingecko#"}, "******************************************": {"decimals": "9", "symbol": "SOL", "to": "coingecko#solana"}, "******************************************": {"decimals": "12", "symbol": "xcBNC", "to": "coingecko#bifrost-native-coin"}, "******************************************": {"decimals": "10", "symbol": "xcLDOT", "to": "coingecko#liquid-staking-dot"}, "******************************************": {"decimals": "10", "symbol": "xcZTG", "to": "coingecko#zeitgeist"}, "******************************************": {"decimals": "18", "symbol": "xcCFG", "to": "coingecko#centrifuge"}, "0xffffffff7d3875460d4509eb8d0362c611b4e841": {"decimals": "18", "symbol": "xcMANTA", "to": "coingecko#manta-network"}, "0xffffffff99dabe1a8de0ea22baa6fd48fde96f6c": {"decimals": "18", "symbol": "VGLMR", "to": "coingecko#voucher-glmr"}, "******************************************": {"decimals": "18", "symbol": "FIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "6", "symbol": "xcUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"to": "coingecko#polkamarkets", "decimals": 18, "symbol": "POLK"}}, "metis": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "m.USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "m.USDT", "to": "coingecko#tether"}}, "starknet": {"0x498edfaf50ca5855666a700c25dd629d577eb9afccdf3b5977aec79aee55ada": {"decimals": "18", "symbol": "CASH", "to": "coingecko#usd-coin"}, "0x0057912720381af14b0e5c87aa4718ed5e527eab60b3801ebf702ab09139e38b": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "0x4718f5a0fc34cc1af16a1cdee98ffb20c31f5cd61d6ab07201858f4287c938d": {"decimals": "18", "symbol": "STRK", "to": "coingecko#starknet"}, "0x049210ffc442172463f3177147c1aeaa36c51d152c1b0630f2364c300d4f48ee": {"decimals": "18", "symbol": "UNI", "to": "coingecko#uniswap"}, "0x070a76fd48ca0ef910631754d77dd822147fe98a569b826ec85e3c33fde586ac": {"decimals": "18", "symbol": "LUSD", "to": "coingecko#liquity-usd"}, "0x0319111a5037cbec2b3e638cc34a3474e2d2608299f3e62866e9cc683208c610": {"decimals": "18", "symbol": "RETH", "to": "coingecko#rocket-pool-eth"}, "0x009c6b4fb13dfaa025c1383ed6190af8ed8cbb09d9588a3bb020feb152442406": {"decimals": "18", "symbol": "FRAX", "to": "coingecko#frax"}, "0x0058efd0e73c33a848ffaa88738d128ebf0af98ea78cf3c14dc757bb02d39ffb": {"decimals": "18", "symbol": "FXS", "to": "coingecko#frax-share"}, "0x0124aeb495b947201f5fac96fd1138e326ad86195b98df6dec9009158a533b49": {"decimals": "18", "symbol": "LORDS", "to": "coingecko#lords"}, "0x719b5092403233201aa822ce928bd4b551d0cdb071a724edd7dc5e5f57b7f34": {"decimals": "18", "symbol": "UNO", "to": "coingecko#nostra-uno"}, "0x0719b5092403233201aa822ce928bd4b551d0cdb071a724edd7dc5e5f57b7f34": {"decimals": "18", "symbol": "UNO", "to": "coingecko#nostra-uno"}, "0x0585c32b625999e6e5e78645ff8df7a9001cf5cf3eb6b80ccdd16cb64bd3a34": {"decimals": "18", "symbol": "ZEND", "to": "coingecko#zklend-2"}, "0x00585c32b625999e6e5e78645ff8df7a9001cf5cf3eb6b80ccdd16cb64bd3a34": {"decimals": "18", "symbol": "ZEND", "to": "coingecko#zklend-2"}, "0x00c530f2c0aa4c16a0806365b0898499fba372e5df7a7172dc6fe9ba777e8007": {"decimals": "18", "symbol": "NSTR", "to": "coingecko#nostra"}, "0x75afe6402ad5a5c20dd25e10ec3b3986acaa647b77e4ae24b0cbc9a54a27a87": {"decimals": "18", "symbol": "EKUBO", "to": "coingecko#ekubo-protocol"}, "0xc530f2c0aa4c16a0806365b0898499fba372e5df7a7172dc6fe9ba777e8007": {"decimals": "18", "symbol": "NSTR", "to": "coingecko#nostra"}, "0x03b405a98c9e795d427fe82cdeeeed803f221b52471e3a757574a2b4180793ee": {"to": "coingecko#starknet-brother", "decimals": 18, "symbol": "0x03b405a98c9e795d427fe82cdeeeed803f221b52471e3a757574a2b4180793ee"}, "0x20ff2f6021ada9edbceaf31b96f9f67b746662a6e6b2bc9d30c0d3e290a71f6": {"to": "coingecko#spiko-us-t-bills-money-market-fund", "decimals": 5, "symbol": "USTBL"}, "0x4f5e0de717daa6aa8de63b1bf2e8d7823ec5b21a88461b1519d9dbc956fb7f2": {"to": "coingecko#eutbl", "decimals": 5, "symbol": "EUTBL"}}, "milkomeda": {"0x6085c822b7a4c688d114468b1380a0ed1873a0b3": {"decimals": "18", "symbol": "VOID", "to": "shimmer_evm:0xe5f3dcc241dd008e3c308e57cf4f7880ea9210f8"}}, "new": {"0xf4905b9bc02ce21c98eac1803693a9357d5253bf": {"decimals": "18", "symbol": "NEW", "to": "coingecko#newton-project"}}, "astar": {"0xffffffff00000000000000010000000000000008": {"decimals": "10", "symbol": "vDOT", "to": "coingecko#voucher-dot"}, "0xffffffff00000000000000010000000000000010": {"decimals": "18", "symbol": "ASTR", "to": "coingecko#astar"}, "0x1ec4bbd1ff6642d29c1453ce1207b039a434a438": {"to": "coingecko#astar", "decimals": 18, "symbol": "DASTR"}}, "acala": {"0x0000000000000000000100000000000000000002": {"decimals": "10", "symbol": "DOT", "to": "coingecko#polkadot"}, "0x0000000000000000000100000000000000000003": {"decimals": "10", "symbol": "LDOT", "to": "coingecko#liquid-staking-dot"}, "0x07DF96D1341A7d16Ba1AD431E2c847d978BC2bCe": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "ACA": {"to": "coingecko#acala", "decimals": 12, "symbol": "ACA"}, "LDOT": {"to": "coingecko#liquid-staking-dot", "decimals": 10, "symbol": "LDOT"}, "DOT": {"to": "coingecko#polkadot", "decimals": 10, "symbol": "DOT"}}, "conflux": {"******************************************": {"decimals": "18", "symbol": "CFLX", "to": "coingecko#conflux-token"}}, "chainx": {"******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#wrapped-bitcoin"}}, "airdao": {"******************************************": {"decimals": "18", "symbol": "SAMB", "to": "coingecko#amber"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "******************************************": {"to": "coingecko#amber", "decimals": 18, "symbol": "AMB"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "USDC"}}, "zkfair": {"******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "ZEEP", "to": "coingecko#zeepr"}}, "icon": {"******************************************": {"decimals": "18", "symbol": "ICX", "to": "coingecko#icon"}, "cxb49d82c46be6b61cab62aaf9824b597c6cf8a25d": {"decimals": "18", "symbol": "BUSD", "to": "coingecko#binance-usd"}, "cx5b5a03cb525a1845d0af3a872d525b18a810acb0": {"decimals": "18", "symbol": "BTCB", "to": "coingecko#bitcoin-bep2"}, "cx288d13e1b63563459a2ac6179f237711f6851cb5": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "kroma": {"******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "WEMIX", "to": "coingecko#wemix-token"}}, "archway": {"aarch": {"decimals": "18", "symbol": "ARCH", "to": "coingecko#archway"}, "archway1cutfh7m87cyq5qgqqw49f289qha7vhsg6wtr6rl5fvm28ulnl9ssg0vk0n": {"decimals": "18", "symbol": "xARCH", "to": "coingecko#astrovault-xarch"}, "archway13wj2dvr4h4cuwqftlxcdykhpw0pm7a2g542w3t608lwlcy4ctmgs4eutcn": {"decimals": "6", "symbol": "xOSMO", "to": "coingecko#astrovault-xosmo_astrovault"}, "archway1t2llqsvwwunf98v692nqd5juudcmmlu3zk55utx7xtfvznel030saclvq6": {"decimals": "18", "symbol": "sARCH", "to": "coingecko#liquid-finance-arch"}, "archway14fzmzu2apcdzwx9dspyk0yjmxwwnpmxh7zlezh5ywe4l0pky39ysgzd9gg": {"decimals": "6", "symbol": "xCMDX", "to": "coingecko#comdex"}, "archway1asgu5g79cdjcdd40lgefplszehykpwzcunx30ca4456a4tddmwcsrmtvx8": {"decimals": "6", "symbol": "xFLIX", "to": "coingecko#omniflix-network"}, "archway1tl8l2gt9dncdu6huds39dsg366ctllvtnm078qkkad2mnv28erss98tl2n": {"decimals": "6", "symbol": "xAKT", "to": "coingecko#xakt_astrovault"}, "archway1h7vfp6hjjluw8n6m2v4tkfdw3getkwqldu59xghltdskt3rh6shqczumjc": {"decimals": "18", "symbol": "xPLQ", "to": "coingecko#xplq_astrovault"}, "archway1qfkn8ezkf5jqvchfs2jp3lukul7l79a4jg56upgkvu464805g78q6flwrx": {"decimals": "18", "symbol": "xCUDOS", "to": "coingecko#xcudos_astrovault"}, "archway1gxewwgtk5r7ygnlvuu42vza06700gzac4xl59dz4xhv3m7trzmvsf5nrtq": {"decimals": "6", "symbol": "xPASG", "to": "coingecko#xpasg_astrovault"}, "archway1m273xq2fjmn993jm4kft5c49w2c70yfv5zypt3d92cqp4n5faefqqkuf0l": {"decimals": "6", "symbol": "xATOM", "to": "coingecko#astrovault-xatom"}, "archway1l3m84nf7xagkdrcced2y0g367xphnea5uqc3mww3f83eh6h38nqqxnsxz7": {"decimals": "18", "symbol": "bnUSD", "to": "coingecko#balanced-dollars"}, "archway1mast3w89sn8gtmvdhr6gfemu4jdx60563fajs5ucsc7s7e9jv00q7qw0hu": {"decimals": "6", "symbol": "xBCNA", "to": "coingecko#bitcanna"}, "archway1veyq07az0d7mlp49sa9f9ef56w0dd240vjsy76yv0m4pl5a2x2uq698cs7": {"decimals": "6", "symbol": "xDEC", "to": "coingecko#decentr"}, "archway1yv8uhe795xs4fwz6mjm278yr35ps0yagjchfp39q5x49dty9jgssm5tnkv": {"decimals": "6", "symbol": "xBLD", "to": "coingecko#agoric"}, "archway1zfnzv39cp4dv3jjy0aptn5msc02tjmy602l46u90dt729q80939qjgqcdj": {"decimals": "6", "symbol": "xGRAV", "to": "coingecko#graviton"}, "archway1ecjefhcf8r60wtfnhwefrxhj9caeqa90fj58cqsaafqveawn6cjs5znd2n": {"decimals": "6", "symbol": "AXV", "to": "coingecko#astrovault"}, "archway1yjdgfut7jkq5xwzyp6p5hs7hdkmszn34zkhun6mglu3falq3yh8sdkaj7j": {"decimals": "6", "symbol": "xJKL", "to": "coingecko#astrovault-xjkl"}, "archway170gg4z9rpgu3uq6cz4584um70xknltxfttz8r9vea0hp7ksvuqhqckztk7": {"decimals": "6", "symbol": "xBTSG", "to": "coingecko#xbtsg_astrovault"}, "archway1ckpwu65werp2srtatvdj4akclra4j5rxwxrzk335et7xhjs5vx5sxsh96s": {"decimals": "6", "symbol": "xDVPN", "to": "coingecko#xdvpn_astrovault"}}, "klaytn": {"******************************************": {"decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "******************************************": {"decimals": "18", "symbol": "WKLAY", "to": "coingecko#klay-token"}, "******************************************": {"decimals": "18", "symbol": "BTCB", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "8", "symbol": "WFBTC", "to": "coingecko#ignition-fbtc"}}, "blast": {"******************************************": {"decimals": "18", "symbol": "jcvWETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "rsETH", "to": "coingecko#kelp-dao-restaked-eth"}, "******************************************": {"decimals": "18", "symbol": "wrsETH", "to": "coingecko#wrapped-rseth"}, "******************************************": {"decimals": "18", "symbol": "weETH", "to": "coingecko#wrapped-eeth"}, "******************************************": {"decimals": "18", "symbol": "DETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "DUSD", "to": "coingecko#usdb"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "USDB", "to": "coingecko#usdb"}, "******************************************": {"decimals": "18", "symbol": "ezETH", "to": "ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "YIELD", "to": "coingecko#crypto-valleys-yield-token"}, "******************************************": {"decimals": "18", "symbol": "YES", "to": "coingecko#yes-money"}, "******************************************": {"decimals": "18", "symbol": "MIM", "to": "coingecko#magic-internet-money"}, "******************************************": {"decimals": "18", "symbol": "iUSD", "to": "coingecko#izumi-bond-usd"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "weth", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "DEUS", "to": "coingecko#deus-finance-2"}, "******************************************": {"decimals": "18", "symbol": "USDE", "to": "coingecko#ethena-usde"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"to": "blast:******************************************", "decimals": 18, "symbol": "LockedBLASTUP"}}, "bitcoin": {"BSSB": {"decimals": "0", "symbol": "BSSB", "to": "coingecko#bitstable-finance"}, "MUBI": {"decimals": "0", "symbol": "MUBI", "to": "coingecko#multibit"}, "rats": {"decimals": "0", "symbol": "rats", "to": "coingecko#rats"}, "BTCs": {"decimals": "0", "symbol": "BTCs", "to": "coingecko#btcs"}, "MMSS": {"decimals": "0", "symbol": "MMSS", "to": "coingecko#mmss"}, "AINN": {"decimals": "0", "symbol": "AINN", "to": "coingecko#artificial-neural-network-ordinals"}}, "bitrock": {"******************************************": {"decimals": "18", "symbol": "WBR", "to": "coingecko#wrapped-bitrock"}, "******************************************": {"decimals": "9", "symbol": "BTR", "to": "coingecko#bitrock"}}, "tomochain": {"******************************************": {"decimals": "18", "symbol": "ARKEN", "to": "coingecko#arken-finance"}, "******************************************": {"decimals": "18", "symbol": "VIC", "to": "coingecko#viction"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "SAROS", "to": "coingecko#saros-finance"}, "******************************************": {"decimals": "18", "symbol": "SVIC", "to": "coingecko#defusion-staked-vic"}}, "btn": {"******************************************": {"decimals": "18", "symbol": "BTN", "to": "coingecko#bitnet"}}, "btnx": {"******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}}, "area": {"******************************************": {"decimals": "18", "symbol": "AREA", "to": "coingecko#areon-network"}, "******************************************": {"decimals": "18", "symbol": "AREA", "to": "coingecko#areon-network"}}, "defiverse": {"******************************************": {"decimals": "18", "symbol": "OASY", "to": "coingecko#oasys"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "CCP", "to": "coingecko#chainccolosseumphoenix"}, "******************************************": {"decimals": "18", "symbol": "bCCP", "to": "coingecko#beta-chain-ccolosseum-phoenix"}}, "xai": {"******************************************": {"decimals": "18", "symbol": "XAI", "to": "coingecko#xai-blockchain"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"to": "coingecko#xai-blockchain", "decimals": 18, "symbol": "WXAI"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "merlin": {"******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#merlin-chain-bridged-wrapped-btc-merlin"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "VOYA", "to": "coingecko#merlin-chain-bridged-voya-merlin"}, "******************************************": {"decimals": "18", "symbol": "M-BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "SATS", "to": "coingecko#sats-ordinals"}, "******************************************": {"decimals": "18", "symbol": "RATS", "to": "coingecko#rats"}, "******************************************": {"decimals": "18", "symbol": "ORDI", "to": "coingecko#ordinals"}, "******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "M-STONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "WOO", "to": "coingecko#woo-network"}, "******************************************": {"decimals": "18", "symbol": "aMerSOLVBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "xSolvBTC", "to": "coingecko#solv-btc"}}, "naka": {"******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ORDI", "to": "coingecko#ordinals"}}, "astrzk": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ASTR", "to": "coingecko#astar"}, "******************************************": {"decimals": "18", "symbol": "VASTR", "to": "coingecko#bifrost-voucher-astr"}, "******************************************": {"decimals": "18", "symbol": "DOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "ARSW", "to": "coingecko#arthswap"}}, "inevm": {"******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol-protocol"}, "******************************************": {"decimals": "18", "symbol": "INJ", "to": "coingecko#injective-protocol-protocol"}}, "sapphire": {"******************************************": {"decimals": "18", "symbol": "ROSE", "to": "coingecko#oasis-network-network"}, "******************************************": {"decimals": "18", "symbol": "stROSE", "to": "coingecko#oasis-network-network"}, "******************************************": {"decimals": "18", "symbol": "ROSE", "to": "coingecko#oasis-network-network"}, "******************************************": {"decimals": "18", "symbol": "ROSE", "to": "coingecko#oasis-network-network"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ROSY", "to": "coingecko#rosy"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}}, "q": {"******************************************": {"decimals": "18", "symbol": "Q", "to": "coingecko#q-protocol"}, "******************************************": {"decimals": "18", "symbol": "WQ", "to": "coingecko#q-protocol"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WDAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "6", "symbol": "WUSDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ELK", "to": "coingecko#elk-finance"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "ftn": {"******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "FTN", "to": "coingecko#fasttoken"}, "******************************************": {"decimals": "18", "symbol": "FTN", "to": "coingecko#fasttoken"}, "******************************************": {"decimals": "18", "symbol": "FTN", "to": "coingecko#fasttoken"}, "******************************************": {"to": "coingecko#lolik-staked-ftn", "decimals": 18, "symbol": "stFTN"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "mUSDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "mUSDT"}}, "zklink": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.m", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "BTCT", "to": "coingecko#bitcoin-trc20"}, "******************************************": {"decimals": "18", "symbol": "M-BTC", "to": "coingecko#merlin-s-seal-btc"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.b", "to": "coingecko#solv-btc"}}, "zora": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDzC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "imx": {"******************************************": {"decimals": "18", "symbol": "IMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "IMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "axlUSDC", "to": "coingecko#axlusdc"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "sei": {"******************************************": {"decimals": "18", "symbol": "M-BTC", "to": "coingecko#merlin-s-seal-btc"}, "******************************************": {"decimals": "8", "symbol": "brBTC", "to": "coingecko#bedrock-btc"}, "******************************************": {"decimals": "6", "symbol": "USD₮0", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "FRAX", "to": "coingecko#frax"}, "******************************************": {"decimals": "18", "symbol": "frxETH", "to": "coingecko#frax-ether"}, "******************************************": {"decimals": "18", "symbol": "sfrxETH", "to": "coingecko#staked-frax-ether"}, "******************************************": {"decimals": "18", "symbol": "sFRAX", "to": "coingecko#staked-frax"}, "******************************************": {"decimals": "18", "symbol": "JLY", "to": "coingecko#jellyverse"}, "******************************************": {"decimals": "18", "symbol": "fastUSD", "to": "coingecko#sei-fastusd"}, "******************************************": {"decimals": "18", "symbol": "ssETH", "to": "coingecko#super-seyian-eth"}, "******************************************": {"decimals": "18", "symbol": "seiyanETH", "to": "coingecko#seiyaneth"}, "******************************************": {"decimals": "18", "symbol": "dinero", "to": "coingecko#dinero-2"}, "******************************************": {"decimals": "18", "symbol": "syUSD", "to": "coingecko#syusd"}, "******************************************": {"decimals": "6", "symbol": "seiyan", "to": "coingecko#seiyan"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#syusd", "decimals": 18, "symbol": "syUSD"}, "******************************************": {"to": "coingecko#frax-share", "decimals": 18, "symbol": "FXS"}, "******************************************": {"to": "coingecko#zenrock", "decimals": 6, "symbol": "ROCK"}, "******************************************": {"to": "coingecko#milli-coin", "decimals": 6, "symbol": "MILLI"}, "******************************************": {"to": "coingecko#kryptonite-staked-sei", "decimals": 6, "symbol": "STSEI"}, "******************************************": {"to": "coingecko#seiyaneth", "decimals": 18, "symbol": "seiyanETH"}, "******************************************": {"to": "coingecko#super-seyian-eth", "decimals": 18, "symbol": "seiyanETH"}, "******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": 18, "symbol": "sUSDa"}, "******************************************": {"to": "coingecko#wrapped-steth", "decimals": 18, "symbol": "wstETH"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 18, "symbol": "uBTC"}, "******************************************": {"decimals": "8", "symbol": "WFBTC", "to": "coingecko#ignition-fbtc"}}, "rss3_vsl": {"******************************************": {"decimals": "18", "symbol": "RSS3", "to": "coingecko#rss3"}}, "bevm": {"******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "wstBTC", "to": "coingecko#wrapped-stbtc"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "degen": {"******************************************": {"decimals": "18", "symbol": "DEGEN", "to": "coingecko#degen-base"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "ancient8": {"******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "karak": {"******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "RSWETH", "to": "coingecko#restaked-swell-eth"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "bsquared": {"******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "BSTONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "FDUSD", "to": "coingecko#first-digital-usd"}, "******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "18", "symbol": "MATIC", "to": "coingecko#matic-network"}, "******************************************": {"decimals": "18", "symbol": "uBTC", "to": "coingecko#bitcoin"}, "******************************************": {"to": "coingecko#lorenzo-stbtc", "decimals": 18, "symbol": "stBTC"}}, "planq": {"******************************************": {"decimals": "18", "symbol": "PLQ", "to": "coingecko#planq"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "9", "symbol": "SRCX", "to": "coingecko#source-protocol"}, "******************************************": {"decimals": "6", "symbol": "SOURCE", "to": "coingecko#source"}, "******************************************": {"decimals": "6", "symbol": "KUJI", "to": "coingecko#kujira"}, "******************************************": {"decimals": "18", "symbol": "ARCH", "to": "coingecko#archway"}, "******************************************": {"decimals": "18", "symbol": "CNTO", "to": "coingecko#ciento-exchange"}}, "lac": {"******************************************": {"decimals": "18", "symbol": "LAC", "to": "coingecko#la-coin"}, "******************************************": {"to": "coingecko#la-coin", "decimals": 18, "symbol": "LAC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "genesys": {"******************************************": {"decimals": "18", "symbol": "GSYS", "to": "coingecko#genesys"}}, "bob": {"******************************************": {"decimals": "8", "symbol": "LBTC", "to": "coingecko#lombard-staked-btc"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "TBTC", "to": "coingecko#tbtc"}, "******************************************": {"decimals": "18", "symbol": "WSTETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "18", "symbol": "STONE", "to": "coingecko#stakestone-ether"}, "******************************************": {"decimals": "18", "symbol": "RETH", "to": "coingecko#rocket-pool-eth"}, "******************************************": {"decimals": "18", "symbol": "BLUES", "to": "coingecko#blueshift"}, "******************************************": {"decimals": "18", "symbol": "SOV", "to": "coingecko#sovryn"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-bedrock-bob", "to": "coingecko#ignition-fbtc"}, "******************************************": {"decimals": "8", "symbol": "lfbtc-pump-bob", "to": "coingecko#ignition-fbtc"}, "******************************************": {"to": "coingecko#solv-btc", "decimals": 18, "symbol": "SolvBTC"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 8, "symbol": "uniBTC"}, "******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}, "******************************************": {"to": "coingecko#satoshi-stablecoin", "decimals": 18, "symbol": "satUSD"}, "******************************************": {"to": "coingecko#babylon", "decimals": 6, "symbol": "BABY"}}, "xlayer": {"******************************************": {"decimals": "18", "symbol": "OKB", "to": "coingecko#okb"}, "******************************************": {"decimals": "8", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"to": "coingecko#okb", "decimals": 18, "symbol": "OKB"}}, "btr": {"******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "18", "symbol": "ordi", "to": "coingecko#ordi"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.m", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "BitUSD", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "WBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "stBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ZBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc", "decimals": 18, "symbol": "aBitSOLVBTC"}, "******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}}, "iotaevm": {"******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}}, "bouncebit": {"******************************************": {"decimals": "18", "symbol": "AUCTION", "to": "coingecko#auction"}, "******************************************": {"decimals": "18", "symbol": "BB", "to": "coingecko#bouncebit"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "BBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BB", "to": "coingecko#bouncebit"}, "******************************************": {"to": "coingecko#bouncebit-btc", "decimals": 18, "symbol": "wstBBTC"}}, "taiko": {"******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "18", "symbol": "TAIKO", "to": "coingecko#taiko"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "6", "symbol": "USDC.e", "to": "coingecko#usd-coin"}, "******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "6", "symbol": "USDT.e", "to": "coingecko#tether"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}, "******************************************": {"to": "coingecko#solv-btc", "decimals": 18, "symbol": "SolvBTC"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#merlin-s-seal-btc", "decimals": 18, "symbol": "M-BTC"}, "******************************************": {"to": "coingecko#loopring", "decimals": 18, "symbol": "LRC"}, "******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": 18, "symbol": "sUSDa"}}, "real": {"******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "DAI", "to": "coingecko#dai"}, "******************************************": {"decimals": "18", "symbol": "USDM", "to": "coingecko#mountain-protocol-usdm"}, "******************************************": {"decimals": "18", "symbol": "arcUSD", "to": "coingecko#arcana-2"}, "******************************************": {"decimals": "18", "symbol": "arcUSD", "to": "coingecko#arcana-2"}, "******************************************": {"decimals": "18", "symbol": "MORE", "to": "coingecko#stack-2"}, "******************************************": {"to": "coingecko#re-al", "decimals": 18, "symbol": "RWA"}, "******************************************": {"to": "coingecko#pearl", "decimals": 18, "symbol": "PEARL"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#real-usd", "decimals": 9, "symbol": "USDR"}}, "cyeth": {"******************************************": {"decimals": "18", "symbol": "CYBER", "to": "coingecko#cyberconnect"}, "******************************************": {"decimals": "18", "symbol": "ETH", "to": "coingecko#ethereum"}}, "lukso": {"******************************************": {"decimals": "18", "symbol": "WLYX", "to": "coingecko#wrapped-lyx-universalswaps"}, "******************************************": {"to": "coingecko#lukso-token-2", "decimals": 18, "symbol": "LYX"}, "******************************************": {"to": "coingecko#stakingverse-staked-lyx", "decimals": 18, "symbol": "sLYX"}, "******************************************": {"decimals": "18", "symbol": "CHILL", "to": "coingecko#chillwhales"}, "******************************************": {"to": "coingecko#fabs", "decimals": 18, "symbol": "FABS"}}, "joltify": {"ujolt": {"decimals": "6", "symbol": "JOLT", "to": "coingecko#joltify"}}, "defichain_evm": {"******************************************": {"to": "coingecko#defichain", "decimals": 18, "symbol": "DFI"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#decentralized-usd", "decimals": 18, "symbol": "DUSD"}, "******************************************": {"to": "coingecko#javsphere", "decimals": 18, "symbol": "JAV"}}, "map": {"******************************************": {"to": "coingecko#marcopolo", "decimals": 18, "symbol": "stMAPO"}, "****************************************** ": {"to": "coingecko#marcopolo", "decimals": 18, "symbol": "MAPO "}}, "karura": {"KSM": {"to": "coingecko#kusama", "decimals": 12, "symbol": "KSM"}, "LKSM": {"to": "coingecko#liquid-ksm", "decimals": 12, "symbol": "LKSM"}, "KAR": {"to": "coingecko#karura", "decimals": 12, "symbol": "KAR"}, "BNC": {"to": "coingecko#bifrost-native-coin", "decimals": 12, "symbol": "BNC"}, "PHA": {"to": "coingecko#pha", "decimals": 12, "symbol": "PHA"}, "KINT": {"to": "coingecko#kintsugi", "decimals": 12, "symbol": "KINT"}, "KBTC": {"to": "coingecko#kintsugi-btc", "decimals": 8, "symbol": "KBTC"}}, "etlk": {"******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "LBTC"}, "******************************************": {"to": "coingecko#stacy-staked-xtz", "decimals": 6, "symbol": "stXTZ"}, "******************************************": {"to": "coingecko#tezos", "decimals": 18, "symbol": "OGV1WXTZ"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#spiko-us-t-bills-money-market-fund", "decimals": 5, "symbol": "USTBL"}, "******************************************": {"to": "coingecko#EUTBL", "decimals": 5, "symbol": "EUTBL"}}, "mint": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "nETH"}}, "rari": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "ailayer": {"******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 18, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#bouncebit-btc", "decimals": 18, "symbol": "BBTC"}, "******************************************": {"to": "coingecko#artificial-neural-network-ordinals", "decimals": 18, "symbol": "ORDI"}, "******************************************": {"to": "coingecko#izumi-bond-usd", "decimals": 18, "symbol": "iUSD"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "BFBTC"}}, "fraxtal": {"******************************************": {"decimals": "18", "symbol": "CRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "crvUSD", "to": "coingecko#crvusd"}, "******************************************": {"decimals": "18", "symbol": "scrvUSD", "to": "coingecko#savings-crvusd"}, "******************************************": {"to": "coingecko#axlusdc", "decimals": 18, "symbol": "axlUSDC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#staked-frax", "decimals": 18, "symbol": "sFRAX"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": 18, "symbol": "USDe"}}, "flare": {"******************************************": {"to": "coingecko#hex-trust-usdx", "decimals": 6, "symbol": "cUSDX"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#hex-trust-usdx", "decimals": 6, "symbol": "USDX"}}, "assetchain": {"******************************************": {"to": "coingecko#xend-finance", "decimals": 18, "symbol": "xRWA"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "BTC"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wicrypt", "decimals": 18, "symbol": "WNT"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 18, "symbol": "WBTC"}}, "aeternity": {"******************************************": {"to": "coingecko#aeternity", "decimals": 18, "symbol": "AE"}, "ct_J3zBY8xxjsRr3QojETNw48Eb38fjvEuJKkQ6KzECvubvEcvCa": {"to": "coingecko#aeternity", "decimals": 18, "symbol": "WAE"}, "ct_WVqAvLQpvZCgBg4faZLXA1YBj43Fxj91D33Z8K7pFsY8YCofv": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "ct_U1i8dzJTVWdnU2cv59TZQfLFpLfjqf7MQQC5ygSMKphn8Yew2": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "USDC"}, "ct_2AiMceYFXnUdA6A9Lu2ZQ2tr2TpfbGVfkxLfBnceoWgHTKZYvc": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}, "ct_26Q5MYFKE4z4GaYLmhZiZ9AHsSVqVNZiiyzySSHTorWyr4od4K": {"to": "coingecko#wrapped-bitcoin", "decimals": 18, "symbol": "WBTC"}}, "ethereumclassic": {"******************************************": {"to": "coingecko#etcpow", "decimals": 18, "symbol": "ETCPOW"}}, "pulse": {"******************************************": {"to": "coingecko#savings-dai", "decimals": 18, "symbol": "sDAI"}}, "saakuru": {"******************************************": {"to": "coingecko#oasys", "decimals": 18, "symbol": "WOAS"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "ripple": {"XRP": {"to": "coingecko#ripple", "decimals": 6, "symbol": "XRP"}, "4F55534700000000000000000000000000000000.rHuiXXjHLpMP8ZE9sSQU5aADQVWDwv6h5p": {"to": "coingecko#ousg", "decimals": 6, "symbol": "OUSG"}, "524C555344000000000000000000000000000000.rMxCKbEDwqr76QuheSUMdEGf4B9xJ8m5De": {"to": "coingecko#ripple-usd", "decimals": 6, "symbol": "RLUSD"}}, "reya": {"******************************************": {"to": "asset#ethereum:******************************************", "decimals": 30, "symbol": "srUSD"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#elixir-deusd", "decimals": 18, "symbol": "sdeUSD"}, "******************************************": {"to": "coingecko#elixir-deusd", "decimals": 18, "symbol": "deUSD"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": 18, "symbol": "USDE"}}, "chainflip": {"Dot": {"to": "coingecko#polkadot", "decimals": 10, "symbol": "Dot"}, "Usdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "Usdc"}, "Flip": {"to": "coingecko#chainflip", "decimals": 18, "symbol": "Flip"}, "Btc": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "Btc"}, "Eth": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "Eth"}, "Usdt": {"to": "coingecko#tether", "decimals": 6, "symbol": "Usdt"}, "ArbEth": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ArbEth"}, "ArbUsdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "ArbUsdc"}}, "thundercore": {"******************************************": {"to": "coingecko#thunder-token", "decimals": 18, "symbol": "veTT"}}, "oasis": {"******************************************": {"to": "coingecko#oasis-network-network", "decimals": 18, "symbol": "OAS"}}, "heco": {"******************************************": {"to": "coingecko#huobi-token", "decimals": 18, "symbol": "WHT"}}, "zircuit": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": 18, "symbol": "sUSDa"}, "******************************************": {"to": "coingecko#eigenpie-msteth", "decimals": 18, "symbol": "mstETH"}}, "islm": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "cronos_zkevm": {"******************************************": {"to": "coingecko#crypto-com-chain", "decimals": 18, "symbol": "wzkCRO"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}}, "noble": {"uusdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "uusdc"}, "ausdy": {"to": "coingecko#ondo-finance-us-dollar-yield", "decimals": 18, "symbol": "ausdy"}, "uusdn": {"to": "coingecko#m-2", "decimals": 6, "symbol": "uusdn"}}, "neox": {"******************************************": {"to": "coingecko#gas", "decimals": 18, "symbol": "GAS"}, "******************************************": {"to": "coingecko#gas", "decimals": 18, "symbol": "WGAS"}, "******************************************": {"to": "coingecko#gas", "decimals": 18, "symbol": "WGAS"}}, "aura": {"******************************************": {"to": "coingecko#aura-network", "decimals": 18, "symbol": "AURA"}, "******************************************": {"to": "coingecko#aura-network", "decimals": 18, "symbol": "WAURA"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "gravity": {"******************************************": {"to": "coingecko#g-token", "decimals": 18, "symbol": "G"}, "******************************************": {"to": "coingecko#g-token", "decimals": 18, "symbol": "wG"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "idex": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "etn": {"******************************************": {"to": "coingecko#electroneum", "decimals": 18, "symbol": "ETN"}, "******************************************": {"to": "coingecko#electroneum", "decimals": 18, "symbol": "ETN"}}, "polynomial": {"******************************************": {"to": "coingecko#usd0-liquid-bond", "decimals": 18, "symbol": "USD0++.e"}, "******************************************": {"to": "coingecko#solv-btc", "decimals": 18, "symbol": "SolvBTC"}, "******************************************": {"to": "coingecko#wrapped-steth", "decimals": 18, "symbol": "wstETH"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#savings-dai", "decimals": 18, "symbol": "SDAI"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#f-x-protocol-morpho-usdc", "decimals": 18, "symbol": "fxUSDC"}}, "songbird": {"******************************************": {"name": "Wrapped Songbird", "decimals": "18", "symbol": "WSGB", "to": "coingecko#songbird"}, "******************************************": {"name": "Experimental Finance Token", "decimals": "18", "symbol": "EXFI", "to": "coingecko#flare-networks-finance"}, "******************************************": {"name": "Canary Dollar", "decimals": "18", "symbol": "CAND", "to": "coingecko#canary-dollar"}, "******************************************": {"decimals": "6", "symbol": "exUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "exETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "exXDC", "to": "coingecko#xdce-crowd-sale"}}, "core": {"******************************************": {"name": "suBTC", "decimals": "18", "symbol": "suBTC", "to": "coingecko#bitcoin"}, "******************************************": {"to": "coingecko#free-bridged-solvbtcb-core", "decimals": 18, "symbol": "SolvBTC.CORE"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "oBTC"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "uBTC"}, "******************************************": {"name": "nBTC", "decimals": "8", "symbol": "nBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "BTCUSD", "to": "coingecko#bitcoin-usd-btcfi"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.m", "to": "coingecko#solv-btc"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC.b", "to": "coingecko#solv-btc"}}, "stellar": {"CAS3J7GYLGXMF6TDJBBYYSE3HQ6BBSMLNUQ34T6TZMYMW2EVH34XOWMA": {"to": "coingecko#stellar", "decimals": 7, "symbol": "XLM"}, "CCW67TSZV3SSS2HXMBQ5JFGCKJNXKZM7UQUWUZPUTHXSTZLEO7SJMI75": {"to": "coingecko#usd-coin", "decimals": 7, "symbol": "USDC"}, "CDTKPWPLOURQA2SGTKTUQOWRCBZEORB4BWBOMJ3D3ZTQQSGE5F6JBQLV": {"to": "coingecko#euro-coin", "decimals": 7, "symbol": "EURC"}, "CAUIKL3IYGMERDRUN6YSCLWVAKIFG5Q4YJHUKM4S4NJZQIA3BAS6OJPK": {"to": "coingecko#aquarius", "decimals": 7, "symbol": "AQUA"}, "CD25MNVTZDL4Y3XBCPCJXGXATV5WUHHOWMYFF4YBEGU5FCPGMYTVG5JY": {"to": "coingecko#blend", "decimals": 7, "symbol": "BLND"}, "XLM": {"to": "coingecko#stellar", "decimals": 7, "symbol": "XLM"}, "USDC-GA5ZSEJYB37JRC5AVCIA5MOP4RHTM335X2KGX3IHOJAPP5RE34K4KZVN-1": {"to": "coingecko#usd-coin", "decimals": 7, "symbol": "USDC"}}, "shape": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}}, "hela": {"******************************************": {"to": "coingecko#hela-usd", "decimals": 18, "symbol": "HLUSD"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "hUSDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "hUSDT"}, "******************************************": {"to": "coingecko#hela-usd", "decimals": 18, "symbol": "WHLUSD"}}, "lisk": {"******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#lisk", "decimals": 18, "symbol": "LSK"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"decimals": 18, "symbol": "M-BTC", "to": "coingecko#merlin-s-seal-btc"}}, "flow": {"******************************************": {"to": "coingecko#flow", "decimals": 18, "symbol": "WFLOW"}, "******************************************": {"to": "coingecko#ankr-staked-flow", "decimals": 18, "symbol": "ankrFLOWEVM"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "stgUSDC"}, "******************************************": {"to": "coingecko#flow-bridged-pyusd-flow", "decimals": 6, "symbol": "USDF"}}, "matchain": {"******************************************": {"to": "coingecko#matchain", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#matchain", "decimals": 18, "symbol": "MAT"}, "******************************************": {"to": "coingecko#binancecoin", "decimals": 18, "symbol": "BNB"}, "******************************************": {"to": "coingecko#binancecoin", "decimals": 18, "symbol": "WBNB"}}, "rollux": {"******************************************": {"to": "coingecko#uno-re", "decimals": 18, "symbol": "UNO"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "BTC"}}, "fuel": {"0x286c479da40dc953bddc3bb4c453b608bba2e0ac483b077bd475174115395e6b": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "0xf8f8b6283d7fa5b672b530cbb84fcccb4ff8dc40f8176ef4544ddb1f1952ad07": {"to": "coingecko#ethereum", "decimals": 9, "symbol": "ETH"}, "0xa0265fb5c32f6e8db3197af3c7eb05c48ae373605b8165b6f4a51c5b0ba4812e": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "0x9e46f919fbf978f3cad7cd34cca982d5613af63ff8aab6c379e4faa179552958": {"to": "coingecko#savings-dai", "decimals": 9, "symbol": "sDAI"}, "0xaf3111a248ff7a3238cdeea845bb2d43cf3835f1f6b8c9d28360728b55b9ce5b": {"to": "coingecko#manta-mbtc", "decimals": 9, "symbol": "MBTC"}, "0xafd219f513317b1750783c6581f55530d6cf189a5863fd18bd1b3ffcec1714b4": {"to": "coingecko#manta-meth", "decimals": 9, "symbol": "METH"}, "0x91b3559edb2619cde8ffb2aa7b3c3be97efd794ea46700db7092abeee62281b0": {"to": "coingecko#renzo-restaked-eth", "decimals": 9, "symbol": "rzETH"}}, "wc": {"******************************************": {"decimals": "18", "symbol": "uRON", "to": "coingecko#ronin"}, "******************************************": {"decimals": "18", "symbol": "uCOMP", "to": "coingecko#compound-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uZRO", "to": "coingecko#layerzero"}, "******************************************": {"decimals": "18", "symbol": "uPENGU", "to": "coingecko#pudgy-penguins"}, "******************************************": {"decimals": "18", "symbol": "uETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "18", "symbol": "uALEO", "to": "coingecko#aleo"}, "******************************************": {"decimals": "18", "symbol": "uREZ", "to": "coingecko#renzo"}, "******************************************": {"decimals": "18", "symbol": "uTAO", "to": "coingecko#bittensor"}, "******************************************": {"decimals": "18", "symbol": "uPOL", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uIP", "to": "coingecko#story"}, "******************************************": {"decimals": "18", "symbol": "u1INCH", "to": "coingecko#1inch"}, "******************************************": {"decimals": "18", "symbol": "uBONK", "to": "coingecko#bonk"}, "******************************************": {"decimals": "18", "symbol": "uTIA", "to": "coingecko#celestia"}, "******************************************": {"decimals": "18", "symbol": "uCRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"decimals": "18", "symbol": "uCRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "uWIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"decimals": "18", "symbol": "uENS", "to": "coingecko#ethereum-name-service"}, "******************************************": {"decimals": "18", "symbol": "uFIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "18", "symbol": "uFLR", "to": "coingecko#flare-networks"}, "******************************************": {"decimals": "18", "symbol": "uFLOKI", "to": "coingecko#floki"}, "******************************************": {"decimals": "18", "symbol": "uFLOW", "to": "coingecko#flow"}, "******************************************": {"decimals": "18", "symbol": "uJTO", "to": "coingecko#jito-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uMOVE", "to": "coingecko#movement"}, "******************************************": {"decimals": "18", "symbol": "uONDO", "to": "coingecko#ondo-finance"}, "******************************************": {"decimals": "18", "symbol": "uOP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "uPNUT", "to": "coingecko#peanut-the-squirrel"}, "0xd045be6AB98D17A161cfCfc118a8b428D70543Ff": {"decimals": "18", "symbol": "uSTRK", "to": "coingecko#starknet"}, "0x6ca225AE2C92c8A7E9c3162cFcAaA55aD0B09701": {"decimals": "18", "symbol": "uGMT", "to": "coingecko#stepn"}, "0x444Fa322DA64A49A32D29ccd3a1f4DF3De25cF52": {"decimals": "18", "symbol": "uSNX", "to": "coingecko#havven"}, "0x1B0DcC586323C0e10f8Be72EcC104048f25FD625": {"decimals": "18", "symbol": "uVET", "to": "coingecko#vechain"}, "0x3a6B4b4F2250B8CCe56cED4ca286a2ebe6F479A2": {"decimals": "18", "symbol": "uZK", "to": "coingecko#zksync"}, "0x44951C66dFe920baED34457A2cFA65a0c7ff2025": {"decimals": "18", "symbol": "uBLUR", "to": "coingecko#blur"}, "0x2f2041c267795a85B0De04443E7B947A6234fEe8": {"decimals": "18", "symbol": "uKSM", "to": "coingecko#kusama"}, "0xe3AE3EE16a89973D67b678aaD2c3bE865Dcc6880": {"decimals": "18", "symbol": "uLPT", "to": "coingecko#livepeer"}, "0x3ECb91ac996E8c55fe1835969A4967F95a07Ca71": {"decimals": "18", "symbol": "uROSE", "to": "coingecko#oasis-network"}, "0xD6A746236F15E18053Dd3ae8c27341B44CB08E59": {"decimals": "18", "symbol": "uMINA", "to": "coingecko#mina-protocol"}, "0xc5cDEb649ED1A7895b935ACC8EB5Aa0D7a8492BE": {"decimals": "18", "symbol": "uCHZ", "to": "coingecko#chiliz"}, "0x83f31af747189c2FA9E5DeB253200c505eff6ed2": {"decimals": "18", "symbol": "uZEC", "to": "coingecko#zcash"}, "0x9AF46F95a0a8be5C2E0a0274A8b153C72d617E85": {"decimals": "18", "symbol": "uAPE", "to": "coingecko#apecoin"}, "0x16275fD42439A6671b188bDc3949a5eC61932C48": {"decimals": "18", "symbol": "uEGLD", "to": "coingecko#elrond-erd-2"}, "0x508e751fdCf144910074Cc817a16757F608DB52A": {"decimals": "18", "symbol": "uMANA", "to": "coingecko#decentraland"}, "0x5A03841C2e2f5811f9E548cF98E88e878e55d99E": {"decimals": "18", "symbol": "uAXS", "to": "coingecko#axie-infinity"}, "0x05f191a4Aac4b358AB99DB3A83A8F96216ecb274": {"decimals": "18", "symbol": "uHNT", "to": "coingecko#helium"}, "0x31d664ebd97A50d5a2Cd49B16f7714AB2516Ed25": {"decimals": "18", "symbol": "uEOS", "to": "coingecko#eos"}, "0xD7D5c59457d66FE800dBA22b35e9c6C379D64499": {"decimals": "18", "symbol": "uXTZ", "to": "coingecko#tezos"}, "0x1B94330EEc66BA458a51b0b14f411910D5f678d0": {"decimals": "18", "symbol": "uSAND", "to": "coingecko#the-sandbox"}, "0x893ADcbdC7FcfA0eBb6d3803f01Df1eC199Bf7C5": {"decimals": "18", "symbol": "uQNT", "to": "coingecko#quant"}, "0x135Ff404bA56E167F58bc664156beAa0A0Fd95ac": {"decimals": "18", "symbol": "uLDO", "to": "coingecko#lido-dao"}, "0x8989377fd349ADFA99E6CE3Cb6c0D148DfC7F19e": {"decimals": "18", "symbol": "uJASMY", "to": "coingecko#jasmycoin"}, "0x3d00283AF5AB11eE7f6Ec51573ab62b6Fb6Dfd8f": {"decimals": "18", "symbol": "uGRT", "to": "coingecko#the-graph"}, "0x0935b271CA903ADA3FFe1Ac1353fC4A49E7EE87b": {"decimals": "18", "symbol": "uIMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "uINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"decimals": "18", "symbol": "uATOM", "to": "coingecko#cosmos-hub"}, "******************************************": {"decimals": "18", "symbol": "uFET", "to": "coingecko#fetch-ai"}, "******************************************": {"decimals": "18", "symbol": "uETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"decimals": "18", "symbol": "uAAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "uICP", "to": "coingecko#internet-computer"}, "******************************************": {"decimals": "18", "symbol": "uHBAR", "to": "coingecko#hedera"}, "******************************************": {"decimals": "18", "symbol": "uXLM", "to": "coingecko#stellar"}, "******************************************": {"decimals": "18", "symbol": "uTRUMP", "to": "coingecko#official-trump"}, "******************************************": {"decimals": "18", "symbol": "uPEPE", "to": "coingecko#pepe"}, "******************************************": {"decimals": "18", "symbol": "uRNDR", "to": "coingecko#render"}, "******************************************": {"decimals": "18", "symbol": "uMKR", "to": "coingecko#maker"}, "******************************************": {"decimals": "18", "symbol": "uSTX", "to": "coingecko#blockstack"}, "******************************************": {"decimals": "18", "symbol": "uUNI", "to": "coingecko#uniswap"}, "******************************************": {"decimals": "18", "symbol": "uMATIC", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "uLTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "uDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "uBCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"decimals": "18", "symbol": "uALGO", "to": "coingecko#algorand"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 18, "symbol": "USDT0"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#worldcoin-wld", "decimals": 18, "symbol": "WLD"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}}, "apechain": {"******************************************": {"to": "coingecko#apecoin", "decimals": 18, "symbol": "APE"}, "******************************************": {"to": "coingecko#savings-dai", "decimals": 18, "symbol": "sDAI"}, "******************************************": {"to": "coingecko#staked-ether", "decimals": 18, "symbol": "ApeETH"}, "******************************************": {"to": "coingecko#apecoin", "decimals": 18, "symbol": "APE"}}, "hedera": {"******************************************": {"to": "coingecko#xsauce", "decimals": 6, "symbol": "XSAUCE"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#karate-combat", "decimals": 8, "symbol": "KARATE"}, "******************************************": {"to": "coingecko#hbarx", "decimals": 8, "symbol": "HBARX"}, "******************************************": {"to": "coingecko#saucerswap", "decimals": 6, "symbol": "SAUCE"}, "******************************************": {"to": "coingecko#hedera-hashgraph", "decimals": 18, "symbol": "HBAR"}, "******************************************": {"to": "coingecko#hedera-hashgraph", "decimals": 8, "symbol": "WHBAR"}, "******************************************": {"to": "coingecko#dovu-2", "decimals": 8, "symbol": "DOVU"}, "0x00000000000000000000000000000000005c9f70": {"to": "coingecko#hedera-liquity", "decimals": 8, "symbol": "HLQT"}, "0x00000000000000000000000000000000000ec585": {"to": "coingecko#headstarter", "decimals": 8, "symbol": "HST"}, "******************************************": {"to": "coingecko#hashpack", "decimals": 6, "symbol": "PACK"}, "******************************************": {"to": "coingecko#steam", "decimals": 2, "symbol": "STEAM"}, "******************************************": {"to": "coingecko#hsuite", "decimals": 4, "symbol": "HSUITE"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC[hts]"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT[hts]"}, "******************************************": {"to": "coingecko#dai", "decimals": 8, "symbol": "DAI[hts]"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC[hts]"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 8, "symbol": "WETH[hts]"}, "******************************************": {"to": "coingecko#bonzo-finance", "decimals": 8, "symbol": "BONZO"}}, "morph": {"******************************************": {"decimals": "8", "symbol": "oBTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "8", "symbol": "enzoBTC", "to": "coingecko#bitcoin"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": 18, "symbol": "USDe"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"decimals": "18", "symbol": "sUSDa", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"decimals": "10", "symbol": "mphBTC", "to": "asset#bsquared:******************************************"}}, "iotex": {"******************************************": {"to": "coingecko#iotex", "decimals": 18, "symbol": "uniIOTX"}}, "eclipse": {"F72PqK74jc28zjC7kWDk6ykJ2ZAbjNzn2jaAY9v9M6om": {"to": "coingecko#etherfi-weeths", "decimals": 9, "symbol": "weETHs"}, "So11111111111111111111111111111111111111112": {"to": "coingecko#ethereum", "decimals": 9, "symbol": "ETH"}, "9pan9bMn5HatX4EJdBwg9VgCa7Uz5HL8N1m5D3NdXejP": {"to": "coingecko#ethereum", "decimals": 9, "symbol": "ETH"}, "BeRUj3h7BqkbdfFU7FBNYbodgf8GCHodzKvF9aVjNNfL": {"to": "coingecko#solana", "decimals": 9, "symbol": "SOL"}, "AKEWE7Bgh87GPp171b4cJPSSZfmZwQ3KaqYqXoKLNAEE": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "841P4tebEgNux2jaWSjCoi9LhrVr9eHGjLc758Va3RPH": {"to": "coingecko#dogwifcoin", "decimals": 6, "symbol": "WIF"}, "HP5ksEQBkX5UZXxLThvF24TEh5ta9AUB8TLA1YSXKzDs": {"to": "coingecko#neptune-protocol", "decimals": 9, "symbol": "NPT"}, "V5m1Cc9VK61mKL8xVYrjR7bjD2BC5VpADLa6ws3G8KM": {"to": "coingecko#stride-staked-tia", "decimals": 6, "symbol": "stTIA"}, "9RryNMhAVJpAwAGjCAMKbbTFwgjapqPkzpGMfTQhEjf8": {"to": "coingecko#celestia", "decimals": 6, "symbol": "TIA"}, "Fu5P5ikrnQ8BKZECJ1XeeDAaTgWJUrcjw8JmFrNA8TJk": {"to": "coingecko#renzo-restaked-sol", "decimals": 9, "symbol": "ezSOL"}, "CEBP3CqAbW4zdZA57H2wfaSG1QNdzQ72GiQEbQXyW9Tm": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "unit0": {"******************************************": {"to": "coingecko#unit0", "decimals": 18, "symbol": "UNIT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}}, "rbn": {"******************************************": {"to": "coingecko#redbelly-network-token", "decimals": 18, "symbol": "RBNT"}, "******************************************": {"to": "coingecko#redbelly-network-token", "decimals": 18, "symbol": "RBNT"}, "******************************************": {"to": "coingecko#liquid-crypto", "decimals": 18, "symbol": "LQDX"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}}, "shido": {"******************************************": {"to": "coingecko#shido", "decimals": 18, "symbol": "WSHIDO"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "zksync": {"******************************************": {"to": "coingecko#usda", "decimals": 18, "symbol": "USDa"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": 18, "symbol": "sUSDa"}}, "ace": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "tara": {"******************************************": {"to": "coingecko#taraxa", "decimals": 18, "symbol": "TARA"}, "******************************************": {"to": "coingecko#taraxa", "decimals": 18, "symbol": "WTARA"}, "******************************************": {"to": "coingecko#mountain-protocol-usdm", "decimals": 18, "symbol": "USDM"}, "******************************************": {"to": "coingecko#lara", "decimals": 18, "symbol": "LARA"}}, "corn": {"******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "LBTC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "wETH"}, "******************************************": {"to": "coingecko#usdc", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "USDT0"}, "******************************************": {"to": "coingecko#corn-3", "decimals": 18, "symbol": "CORN"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "BTCN"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "BTCN"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}}, "sonic": {"******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"decimals": "18", "symbol": "PEAS", "to": "coingecko#peapods-finance"}, "******************************************": {"to": "asset#sonic:******************************************", "decimals": 18, "symbol": "veSTTX"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "DUSX"}, "******************************************": {"to": "coingecko#sonic-3", "decimals": 18, "symbol": "anS"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "scUSD"}, "******************************************": {"to": "sonic:******************************************", "decimals": 18, "symbol": "bpt-rsb-gauge"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "stkscUSD"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "scETH"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "stkscETH"}, "******************************************": {"to": "coingecko#euro-coin", "decimals": 6, "symbol": "EURC.e"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#sonic-3", "decimals": 18, "symbol": "S"}, "******************************************": {"to": "coingecko#fantom", "decimals": 18, "symbol": "wS"}, "******************************************": {"to": "coingecko#fantom", "decimals": 18, "symbol": "WS"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"decimals": "18", "symbol": "SolvBTC", "to": "coingecko#solv-btc"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "lfbtc-avalon-sonic"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "LBTC"}, "******************************************": {"decimals": "18", "symbol": "USDa", "to": "coingecko#usda"}, "******************************************": {"to": "asset#ethereum:******************************************", "decimals": "18", "symbol": "sUSDa"}, "******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": "8", "symbol": "scBTC"}, "******************************************": {"decimals": "18", "symbol": "AMPED", "to": "coingecko#amped-finance"}}, "vinu": {"******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#vita-inu", "decimals": 18, "symbol": "VINU"}, "******************************************": {"to": "coingecko#vinuchain", "decimals": 18, "symbol": "WVC"}}, "ink": {"******************************************": {"to": "coingecko#global-dollar", "decimals": 6, "symbol": "USDG"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "USDT0"}}, "sophon": {"******************************************": {"to": "coingecko#aethir", "decimals": 18, "symbol": "stAethir"}, "******************************************": {"to": "coingecko#azuro-protocol", "decimals": 18, "symbol": "stAZUR"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-steth", "decimals": 18, "symbol": "WSTETH"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}, "******************************************": {"to": "coingecko#savings-dai", "decimals": 18, "symbol": "sDAI"}, "******************************************": {"to": "coingecko#noon-usn", "decimals": 18, "symbol": "USN"}, "******************************************": {"to": "coingecko#sophon", "decimals": 18, "symbol": "SOPH"}}, "duckchain": {"******************************************": {"to": "coingecko#the-open-network", "decimals": 18, "symbol": "TON"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "MBTC"}}, "qom": {"******************************************": {"to": "coingecko#wrapped-qom", "decimals": 18, "symbol": "WQOM"}}, "vana": {"******************************************": {"to": "coingecko#vana", "decimals": 18, "symbol": "VANA"}, "******************************************": {"to": "coingecko#vana", "decimals": 18, "symbol": "WVANA"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "water": {"******************************************": {"to": "coingecko#water-3", "decimals": 18, "symbol": "wWATER"}}, "bittorrent": {"******************************************": {"to": "coingecko#bittorrent", "decimals": 18, "symbol": "BTTR"}}, "dymension": {"******************************************": {"to": "coingecko#dymension", "decimals": 18, "symbol": "DYM"}}, "energyweb": {"******************************************": {"to": "coingecko#energy-web-token", "decimals": 18, "symbol": "EWT"}}, "kopi": {"uasusdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "uasusdc"}, "ucusdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "ucusdc"}, "uasusdtinj": {"to": "coingecko#tether", "decimals": 6, "symbol": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ucusdtinj": {"to": "coingecko#tether", "decimals": 6, "symbol": "ucusdtinj"}}, "swellchain": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin-universal", "decimals": 18, "symbol": "uBTC"}, "******************************************": {"to": "coingecko#lorenzo-stbtc", "decimals": 18, "symbol": "stBTC"}, "******************************************": {"to": "coingecko#swell-network", "decimals": 18, "symbol": "SWELL"}, "******************************************": {"to": "coingecko#swell-restaked-btc", "decimals": 8, "symbol": "swBTC"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 18, "symbol": "uBTC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#restaked-swell-eth", "decimals": 18, "symbol": "rswETH"}, "******************************************": {"to": "coingecko#sweth", "decimals": 18, "symbol": "swETH"}, "******************************************": {"to": "coingecko#renzo-restaked-lst", "decimals": 18, "symbol": "pzETH"}, "******************************************": {"to": "coingecko#renzo-restaked-eth", "decimals": 18, "symbol": "ezETH"}, "******************************************": {"to": "coingecko#kelp-dao-restaked-eth", "decimals": 18, "symbol": "rsETH"}, "******************************************": {"to": "coingecko#wrapped-steth", "decimals": 18, "symbol": "wstETH"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": 18, "symbol": "USDe"}, "******************************************": {"to": "coingecko#ethena-staked-usde", "decimals": 18, "symbol": "sUSDe"}, "******************************************": {"to": "coingecko#ethena", "decimals": 18, "symbol": "ENA"}}, "elys": {"uelys": {"to": "coingecko#elys-network", "decimals": 6, "symbol": "u<PERSON>s"}}, "soneium": {"******************************************": {"to": "coingecko#resolv-wstusr", "decimals": 18, "symbol": "wstUSR"}, "******************************************": {"to": "coingecko#resolv-rlp", "decimals": 18, "symbol": "RLP"}, "******************************************": {"to": "coingecko#resolv-usr", "decimals": 18, "symbol": "USR"}, "******************************************": {"to": "coingecko#openusdt", "decimals": 6, "symbol": "OUSDT"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "USDT0"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#astar", "decimals": 18, "symbol": "ASTAR"}, "******************************************": {"to": "coingecko#bifrost-voucher-astr", "decimals": 18, "symbol": "vASTR"}, "******************************************": {"to": "coingecko#solv-btc", "decimals": 18, "symbol": "SolvBTC"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-bbn", "decimals": 18, "symbol": "xSolvBTC"}, "******************************************": {"to": "coingecko#neemo-staked-astar", "decimals": 18, "symbol": "NSASTR"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "nrETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 18, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#wrapped-stastr", "decimals": 18, "symbol": "wstASTR"}, "******************************************": {"to": "coingecko#solv-protocol-solvbtc-jupiter", "decimals": 18, "symbol": "SOLVBTC.JUP"}}, "odyssey": {"******************************************": {"to": "coingecko#dione", "decimals": 18, "symbol": "DIONE"}, "******************************************": {"to": "coingecko#dione", "decimals": 18, "symbol": "WDIONE"}}, "crossfi": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#crossfi-2", "decimals": 18, "symbol": "XFI"}, "******************************************": {"to": "coingecko#crossfi-2", "decimals": 18, "symbol": "WXFI"}}, "mantra": {"uom": {"to": "coingecko#mantra-dao", "decimals": 6, "symbol": "uom"}}, "verus": {"i5w5MuNik5NtLcYmNzcvaoixooEebB6MGV": {"to": "coingecko#verus-coin", "decimals": 0, "symbol": "VERUS"}, "iGBs4DWztRNvNEJBt4mqHszLxfKTNHTkhM": {"to": "coingecko#dai", "decimals": 0, "symbol": "DAI"}, "iCkKJuJScy4Z6NSDK7Mt42ZAB2NEnAE1o4": {"to": "coingecko#maker", "decimals": 0, "symbol": "MKR"}, "i9nwxtKuVYX4MSbeULLiK2ttVi6rUEhh4X": {"to": "coingecko#ethereum", "decimals": 0, "symbol": "ETH"}, "iS8TfRPfVpKo5FVfSUzfHBQxo9KuzpnqLU": {"to": "coingecko#tbtc", "decimals": 0, "symbol": "TBTC"}, "i9oCSqKALwJtcv49xUKS2U2i79h1kX6NEY": {"to": "coingecko#tether", "decimals": 0, "symbol": "USDT"}}, "spn": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#arbitrum", "decimals": 18, "symbol": "ARB"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#fluidity-money", "decimals": 18, "symbol": "FLY"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}}, "occ": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "ESD"}, "******************************************": {"to": "coingecko#edu-coin", "decimals": 18, "symbol": "WEDU"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "artela": {"******************************************": {"to": "coingecko#artela-network", "decimals": 18, "symbol": "ART"}, "******************************************": {"to": "coingecko#artela-network", "decimals": 18, "symbol": "WART"}, "******************************************": {"to": "coingecko#artela-network", "decimals": 18, "symbol": "WART"}}, "fluence": {"******************************************": {"to": "coingecko#fluence-2", "decimals": 18, "symbol": "FLT"}, "******************************************": {"to": "coingecko#fluence-2", "decimals": 18, "symbol": "WFLT"}}, "swan": {"******************************************": {"to": "coingecko#swan-chain", "decimals": 18, "symbol": "SWAN"}, "******************************************": {"to": "coingecko#swan-chain", "decimals": 18, "symbol": "SWAN"}}, "abstract": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "zero_network": {"******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "redstone": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "parex": {"******************************************": {"to": "coingecko#parex", "decimals": 18, "symbol": "WPRX"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}}, "plume": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "pETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "pUSD"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"decimals": "8", "symbol": "WFBTC", "to": "coingecko#ignition-fbtc"}}, "sxr": {"******************************************": {"to": "coingecko#sx-network-2", "decimals": 18, "symbol": "SX"}, "******************************************": {"to": "coingecko#sx-network-2", "decimals": 18, "symbol": "WSX"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "stratis": {"******************************************": {"to": "coingecko#stratis", "decimals": 18, "symbol": "WSTRAX"}}, "silicon_zk": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}}, "unichain": {"******************************************": {"decimals": "18", "symbol": "sUSDS", "to": "coingecko#susds"}, "******************************************": {"to": "coingecko#renzo-restaked-eth", "decimals": 18, "symbol": "ezETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#kelp-dao-restaked-eth", "decimals": 18, "symbol": "rsETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}, "******************************************": {"to": "coingecko#uniswap", "decimals": 18, "symbol": "UNI"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}}, "sty": {"******************************************": {"to": "coingecko#story-2", "decimals": 18, "symbol": "IP"}, "******************************************": {"to": "coingecko#story-2", "decimals": 18, "symbol": "WIP"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}}, "formnetwork": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}}, "hyperliquid": {"******************************************": {"to": "coingecko#hyperliquid", "decimals": 18, "symbol": "liquidHYPE"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 18, "symbol": "USDp"}, "******************************************": {"to": "coingecko#fartcoin", "decimals": 6, "symbol": "UFART"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDHL"}, "******************************************": {"to": "coingecko#mantle-restaked-eth", "decimals": 18, "symbol": "cmETH"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USD₮0"}, "******************************************": {"to": "coingecko#ethena-usde", "decimals": 18, "symbol": "USDe"}, "******************************************": {"to": "coingecko#liquidlaunch", "decimals": 18, "symbol": "LIQD"}, "******************************************": {"to": "coingecko#hyperliquid", "decimals": 18, "symbol": "HYPE"}, "******************************************": {"to": "coingecko#hyperliquid", "decimals": 18, "symbol": "WHYPE"}, "******************************************": {"to": "coingecko#staked-hype", "decimals": 18, "symbol": "STHYPE"}, "******************************************": {"to": "coingecko#hyperliquid", "decimals": 18, "symbol": "wstHYPE"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "UETH"}}, "hemi": {"******************************************": {"to": "coingecko#tbtc", "decimals": 18, "symbol": "tBTC"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether-gold", "decimals": 6, "symbol": "XAUt"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "bfBTC"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 18, "symbol": "iBTC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#bitcoin", "decimals": 8, "symbol": "hemiBTC"}, "******************************************": {"to": "coingecko#babypie-wrapped-btc", "decimals": 8, "symbol": "mBTC"}, "******************************************": {"decimals": "8", "symbol": "enzoBTC", "to": "coingecko#lorenzo-stbtc"}, "******************************************": {"decimals": "18", "symbol": "M-BTC", "to": "coingecko#merlin-s-seal-btc"}, "******************************************": {"decimals": "18", "symbol": "stBTC", "to": "coingecko#lorenzo-stbtc"}, "******************************************": {"decimals": "18", "symbol": "uBTC", "to": "coingecko#wrapped-bitcoin"}, "******************************************": {"decimals": "8", "symbol": "brBTC", "to": "coingecko#bedrock-btc"}, "******************************************": {"decimals": "18", "symbol": "VUSD", "to": "coingecko#vesper-vdollar"}, "******************************************": {"decimals": "18", "symbol": "egETH", "to": "coingecko#weth"}, "******************************************": {"decimals": "8", "symbol": "uniBTC", "to": "coingecko#universal-btc"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#kelp-dao-restaked-eth", "decimals": 18, "symbol": "rsETH"}}, "oas": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}}, "hsk": {"******************************************": {"to": "coingecko#hashkey-ecopoints", "decimals": 18, "symbol": "WHSK"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#cellula", "decimals": 18, "symbol": "CELA"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "saga": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#uniswap", "decimals": 18, "symbol": "UNI"}, "******************************************": {"to": "coingecko#saga-2", "decimals": 6, "symbol": "SAGA"}}, "move": {"0x51ffc9885233adf3dd411078cad57535ed1982013dc82d9d6c433a55f2e0035d": {"to": "coingecko#kelp-dao-restaked-eth", "decimals": 8, "symbol": "rsETH"}, "0x2f6af255328fe11b88d840d1e367e946ccd16bd7ebddd6ee7e2ef9f7ae0c53ef": {"to": "coingecko#renzo-restaked-eth", "decimals": 8, "symbol": "ezETH"}, "0x527c43638a6c389a9ad702e7085f31c48223624d5102a5207dfab861f482c46d": {"to": "coingecko#solv-btc", "decimals": 8, "symbol": "SolvBTC"}, "0x95c0fd13373299ada1b9f09ff62473ab8b3908e6a30011730210c141dffdc990": {"to": "coingecko#lorenzo-stbtc", "decimals": 8, "symbol": "stBTC"}, "0xe4354602aa4311f36240dd57f3f3435ffccdbd0cd2963f1a69da39a2dbcd59b5": {"to": "coingecko#layerzero-bridged-frxusd", "decimals": 6, "symbol": "frxUSD"}, "0xbf2efbffbbd7083aaf006379d96b866b73bb4eb9684a7504c62feafe670962c2": {"to": "coingecko#staked-frax", "decimals": 6, "symbol": "sfrxUSD"}, "0x658f4ef6f76c8eeffdc06a30946f3f06723a7f9532e2413312b2a612183759c": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "LBTC"}, "0xe956f5062c3b9cba00e82dc775d29acf739ffa1e612e619062423b58afdbf035": {"to": "coingecko#wrapped-eeth", "decimals": 8, "symbol": "weETH"}, "0x1::aptos_coin::AptosCoin": {"decimals": "8", "symbol": "MOVE", "to": "coingecko#movement"}, "0xa": {"to": "coingecko#movement", "decimals": 8, "symbol": "MOVE"}, "0x447721a30109c662dde9c73a0c2c9c9c459fb5e5a9c92f03c50fa69737f5d08d": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT.e"}, "0x83121c9f9b0527d1f056e21a950d6bf3b9e9e2e8353d0e95ccea726713cbea39": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "0xb06f29f24dde9c6daeec1f930f14a441a8d6c0fbea590725e88b340af3e1939c": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "0x908828f4fb0213d4034c3ded1630bbd904e8a3a6bf3c63270887f0b06653a376": {"to": "coingecko#ethereum", "decimals": 8, "symbol": "ETH"}, "0x9d146a4c9472a7e7b0dbc72da0eafb02b54173a956ef22a9fba29756f8661c6c": {"to": "coingecko#ethena-usde", "decimals": 6, "symbol": "USDe"}, "0x74f0c7504507f7357f8a218cc70ce3fc0f4b4e9eb8474e53ca778cb1e0c6dcc5": {"to": "coingecko#ethena-staked-usde", "decimals": 6, "symbol": "sUSDe"}, "0x48b904a97eafd065ced05168ec44638a63e1e3bcaec49699f6b8dabbd1424650": {"to": "coingecko#usda", "decimals": 8, "symbol": "USDa"}, "0xe699e6c1733462632821b6e5b954c0ed3aa9ad1efb70b6ce92616952ade89258": {"to": "asset#ethereum:******************************************", "decimals": 8, "symbol": "sUSDa"}, "0x16e733c5c943d78dbbaf1fc5beebbcc8db4ed647d2bdfcab74c2a527184a16aa": {"to": "coingecko#ignition-fbtc", "decimals": 8, "symbol": "FBTC"}}, "mtt_network": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}}, "waves": {"GjwAHMjqWzYR4LgoNy91CxUKAGJN79h2hseZoae4nU8t": {"to": "coingecko#unit0", "decimals": 8, "symbol": "UNIT0"}}, "perennial": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}}, "prom": {"******************************************": {"to": "coingecko#prometeus", "decimals": 18, "symbol": "PROM"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 18, "symbol": "USDT"}, "******************************************": {"to": "coingecko#prometeus", "decimals": 18, "symbol": "WPROM"}}, "plume_mainnet": {"******************************************": {"decimals": "18", "symbol": "pUSDT", "to": "coingecko#tether"}, "******************************************": {"decimals": "18", "symbol": "BTC", "to": "coingecko#bitcoin"}, "******************************************": {"decimals": "18", "symbol": "yUSD", "to": "coingecko#yieldfi-ytoken"}, "******************************************": {"decimals": "18", "symbol": "vyUSD", "to": "asset#ethereum:******************************************"}, "******************************************": {"to": "coingecko#nest-elixir-vault-lp", "decimals": 6, "symbol": "inELIXIR"}, "******************************************": {"to": "coingecko#nest-alpha-vault", "decimals": 6, "symbol": "nALPHA"}, "******************************************": {"to": "coingecko#nest-basis-vault", "decimals": 6, "symbol": "nBASIS"}, "******************************************": {"to": "coingecko#nest-btc-vault", "decimals": 8, "symbol": "nBTC"}, "******************************************": {"to": "coingecko#nest-credit-vault", "decimals": 6, "symbol": "nCREDIT"}, "******************************************": {"to": "coingecko#nest-elixir-vault", "decimals": 6, "symbol": "nELIXIR"}, "******************************************": {"to": "coingecko#nest-etf-vault", "decimals": 6, "symbol": "nETF"}, "******************************************": {"to": "coingecko#nest-institutional-vault", "decimals": 6, "symbol": "nINSTO"}, "******************************************": {"to": "coingecko#nest-payfi-vault", "decimals": 6, "symbol": "nPAYFI"}, "******************************************": {"to": "coingecko#nest-treasury-vault", "decimals": 6, "symbol": "nTBILL"}, "******************************************": {"to": "coingecko#paypal-usd", "decimals": 6, "symbol": "PayUSD"}, "******************************************": {"to": "coingecko#dinero-staked-eth", "decimals": 18, "symbol": "pETH"}, "******************************************": {"to": "coingecko#plume", "decimals": 18, "symbol": "PLUME"}, "******************************************": {"to": "coingecko#plume-usd", "decimals": 6, "symbol": "pUSD"}, "******************************************": {"to": "coingecko#superstate-uscc", "decimals": 6, "symbol": "USCC"}, "******************************************": {"to": "coingecko#usdc", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#superstate-short-duration-us-government-securities-fund-ustb", "decimals": 6, "symbol": "USTB"}, "******************************************": {"to": "coingecko#weth", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#plume", "decimals": 18, "symbol": "WPLUME"}}, "mxczkevm": {"******************************************": {"to": "coingecko#mxc", "decimals": 18, "symbol": "WMXC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usdt0", "decimals": 6, "symbol": "USDT0"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#dai", "decimals": 18, "symbol": "DAI"}}, "xp": {"******************************************": {"to": "coingecko#xphere", "decimals": 18, "symbol": "wXP"}}, "aleph_zero": {"******************************************": {"to": "coingecko#aleph-zero", "decimals": 18, "symbol": "AZERO"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}}, "sseed": {"******************************************": {"to": "coingecko#superseed", "decimals": 18, "symbol": "SUPR"}, "******************************************": {"to": "coingecko#superseed-bridged-weth-superseed", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#superseed-bridged-usdc-superseed", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#openusdt", "decimals": 6, "symbol": "oUSDT"}}, "lens": {"******************************************": {"to": "coingecko#gho", "decimals": 18, "symbol": "WGHO"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "******************************************": {"to": "coingecko#bonsai-token", "decimals": 18, "symbol": "BONSAI"}}, "echelon_initia": {"0x274be80e66d76fe5d65c12207ac47625d6756bb4fae8182d044463d21e80e078": {"to": "coingecko#initia", "decimals": 6, "symbol": "INIT"}, "0x29b45e201f7958d307b0b1239c3c3859f67ea5d4ddd15f00ca26af5d2d73eb7": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "0xbdeb90bb2e0f5bab4583fa46c28ec2bffa57502a31d1d5f24725d5c2242b888": {"to": "coingecko#celestia", "decimals": 6, "symbol": "TIA"}, "0x98a9ca6d7d34d9fdd8637947fda22bad26716d48515db179f1dcfa51d3d51705": {"to": "coingecko#ethereum", "decimals": 6, "symbol": "ETH"}, "0x61d7c7ec64adfda0ac8f30c2f3f828c1c5d9a53bbb756d14a0a9f86bf05deb9a": {"to": "coingecko#wrapped-eeth", "decimals": 6, "symbol": "weETH"}, "0xc67813aa57a9fea540542aa1f00f6d8c62eb10108d09a2599cdd6447ad45eb25": {"decimals": "6", "symbol": "milkTIA", "to": "coingecko#milkyway-staked-tia"}}, "namada": {"tnam1pkl64du8p2d240my5umxm24qhrjsvh42ruc98f97": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "tnam1p4px8sw3am4qvetj7eu77gftm4fz4hcw2ulpldc7": {"to": "coingecko#stride-staked-osmo", "decimals": 6, "symbol": "stOSMO"}, "tnam1p5z5538v3kdk3wdx7r2hpqm4uq9926dz3ughcp7n": {"to": "coingecko#stride-staked-atom", "decimals": 6, "symbol": "stATOM"}, "tnam1ph6xhf0defk65hm7l5ursscwqdj8ehrcdv300u4g": {"to": "coingecko#stride-staked-tia", "decimals": 6, "symbol": "stTIA"}, "tnam1p5z8ruwyu7ha8urhq2l0dhpk2f5dv3ts7uyf2n75": {"to": "coingecko#osmosis", "decimals": 6, "symbol": "OSMO"}, "tnam1pkg30gnt4q0zn7j00r6hms4ajrxn6f5ysyyl7w9m": {"to": "coingecko#cosmos", "decimals": 6, "symbol": "ATOM"}, "tnam1pklj3kwp0cpsdvv56584rsajty974527qsp8n0nm": {"to": "coingecko#celestia", "decimals": 6, "symbol": "TIA"}, "tnam1pk288t54tg99umhamwx998nh0q2dhc7slch45sqy": {"to": "coingecko#penumbra", "decimals": 6, "symbol": "UM"}, "tnam1phv4vcuw2ftsjahhvg65w4ux8as09tlysuhvzqje": {"to": "coingecko#nym", "decimals": 6, "symbol": "NYM"}, "tnam1pk6pgu4cpqeu4hqjkt6s724eufu64svpqgu52m3g": {"to": "coingecko#neutron-3", "decimals": 6, "symbol": "NTRN"}}, "hydradx": {"******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#polkadot", "decimals": 10, "symbol": "DOT"}, "******************************************": {"to": "coingecko#voucher-dot", "decimals": 10, "symbol": "vDOT"}, "******************************************": {"to": "coingecko#tbtc", "decimals": 18, "symbol": "tBTC"}, "******************************************": {"to": "coingecko#gigadot", "decimals": 18, "symbol": "2-Pool-GDOT"}, "******************************************": {"to": "coingecko#gigaeth", "decimals": 18, "symbol": "2-Pool-GETH"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}}, "coti": {"******************************************": {"to": "coingecko#coti-governance-token", "decimals": 18, "symbol": "gCOTI"}, "******************************************": {"to": "coingecko#coti", "decimals": 18, "symbol": "COTI"}, "******************************************": {"to": "coingecko#coti", "decimals": 18, "symbol": "COTI"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC.e"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}}, "boba": {"******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}}, "katana": {"******************************************": {"to": "asset#ethereum:******************************************", "decimals": 18, "symbol": "vyUSD"}, "******************************************": {"decimals": "18", "symbol": "uRON", "to": "coingecko#ronin"}, "******************************************": {"decimals": "18", "symbol": "uCOMP", "to": "coingecko#compound-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uZRO", "to": "coingecko#layerzero"}, "******************************************": {"decimals": "18", "symbol": "uPENGU", "to": "coingecko#pudgy-penguins"}, "******************************************": {"decimals": "18", "symbol": "uETHFI", "to": "coingecko#ether-fi"}, "******************************************": {"decimals": "18", "symbol": "uALEO", "to": "coingecko#aleo"}, "******************************************": {"decimals": "18", "symbol": "uREZ", "to": "coingecko#renzo"}, "******************************************": {"decimals": "18", "symbol": "uTAO", "to": "coingecko#bittensor"}, "******************************************": {"decimals": "18", "symbol": "uPOL", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uIP", "to": "coingecko#story"}, "******************************************": {"decimals": "18", "symbol": "u1INCH", "to": "coingecko#1inch"}, "******************************************": {"decimals": "18", "symbol": "uBONK", "to": "coingecko#bonk"}, "******************************************": {"decimals": "18", "symbol": "uTIA", "to": "coingecko#celestia"}, "******************************************": {"decimals": "18", "symbol": "uCRO", "to": "coingecko#crypto-com-chain"}, "******************************************": {"decimals": "18", "symbol": "uCRV", "to": "coingecko#curve-dao-token"}, "******************************************": {"decimals": "18", "symbol": "uWIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"decimals": "18", "symbol": "uENS", "to": "coingecko#ethereum-name-service"}, "******************************************": {"decimals": "18", "symbol": "uFIL", "to": "coingecko#filecoin"}, "******************************************": {"decimals": "18", "symbol": "uFLR", "to": "coingecko#flare-networks"}, "******************************************": {"decimals": "18", "symbol": "uFLOKI", "to": "coingecko#floki"}, "******************************************": {"decimals": "18", "symbol": "uFLOW", "to": "coingecko#flow"}, "******************************************": {"decimals": "18", "symbol": "uJTO", "to": "coingecko#jito-governance-token"}, "******************************************": {"decimals": "18", "symbol": "uMOVE", "to": "coingecko#movement"}, "******************************************": {"decimals": "18", "symbol": "uONDO", "to": "coingecko#ondo-finance"}, "******************************************": {"decimals": "18", "symbol": "uOP", "to": "coingecko#optimism"}, "******************************************": {"decimals": "18", "symbol": "uPNUT", "to": "coingecko#peanut-the-squirrel"}, "0xd045be6AB98D17A161cfCfc118a8b428D70543Ff": {"decimals": "18", "symbol": "uSTRK", "to": "coingecko#starknet"}, "0x6ca225AE2C92c8A7E9c3162cFcAaA55aD0B09701": {"decimals": "18", "symbol": "uGMT", "to": "coingecko#stepn"}, "0x444Fa322DA64A49A32D29ccd3a1f4DF3De25cF52": {"decimals": "18", "symbol": "uSNX", "to": "coingecko#havven"}, "0x1B0DcC586323C0e10f8Be72EcC104048f25FD625": {"decimals": "18", "symbol": "uVET", "to": "coingecko#vechain"}, "0x3a6B4b4F2250B8CCe56cED4ca286a2ebe6F479A2": {"decimals": "18", "symbol": "uZK", "to": "coingecko#zksync"}, "0x44951C66dFe920baED34457A2cFA65a0c7ff2025": {"decimals": "18", "symbol": "uBLUR", "to": "coingecko#blur"}, "0x2f2041c267795a85B0De04443E7B947A6234fEe8": {"decimals": "18", "symbol": "uKSM", "to": "coingecko#kusama"}, "0xe3AE3EE16a89973D67b678aaD2c3bE865Dcc6880": {"decimals": "18", "symbol": "uLPT", "to": "coingecko#livepeer"}, "0x3ECb91ac996E8c55fe1835969A4967F95a07Ca71": {"decimals": "18", "symbol": "uROSE", "to": "coingecko#oasis-network"}, "0xD6A746236F15E18053Dd3ae8c27341B44CB08E59": {"decimals": "18", "symbol": "uMINA", "to": "coingecko#mina-protocol"}, "0xc5cDEb649ED1A7895b935ACC8EB5Aa0D7a8492BE": {"decimals": "18", "symbol": "uCHZ", "to": "coingecko#chiliz"}, "0x83f31af747189c2FA9E5DeB253200c505eff6ed2": {"decimals": "18", "symbol": "uZEC", "to": "coingecko#zcash"}, "0x9AF46F95a0a8be5C2E0a0274A8b153C72d617E85": {"decimals": "18", "symbol": "uAPE", "to": "coingecko#apecoin"}, "0x16275fD42439A6671b188bDc3949a5eC61932C48": {"decimals": "18", "symbol": "uEGLD", "to": "coingecko#elrond-erd-2"}, "0x508e751fdCf144910074Cc817a16757F608DB52A": {"decimals": "18", "symbol": "uMANA", "to": "coingecko#decentraland"}, "0x5A03841C2e2f5811f9E548cF98E88e878e55d99E": {"decimals": "18", "symbol": "uAXS", "to": "coingecko#axie-infinity"}, "0x05f191a4Aac4b358AB99DB3A83A8F96216ecb274": {"decimals": "18", "symbol": "uHNT", "to": "coingecko#helium"}, "0x31d664ebd97A50d5a2Cd49B16f7714AB2516Ed25": {"decimals": "18", "symbol": "uEOS", "to": "coingecko#eos"}, "0xD7D5c59457d66FE800dBA22b35e9c6C379D64499": {"decimals": "18", "symbol": "uXTZ", "to": "coingecko#tezos"}, "0x1B94330EEc66BA458a51b0b14f411910D5f678d0": {"decimals": "18", "symbol": "uSAND", "to": "coingecko#the-sandbox"}, "0x893ADcbdC7FcfA0eBb6d3803f01Df1eC199Bf7C5": {"decimals": "18", "symbol": "uQNT", "to": "coingecko#quant"}, "0x135Ff404bA56E167F58bc664156beAa0A0Fd95ac": {"decimals": "18", "symbol": "uLDO", "to": "coingecko#lido-dao"}, "0x8989377fd349ADFA99E6CE3Cb6c0D148DfC7F19e": {"decimals": "18", "symbol": "uJASMY", "to": "coingecko#jasmycoin"}, "0x3d00283AF5AB11eE7f6Ec51573ab62b6Fb6Dfd8f": {"decimals": "18", "symbol": "uGRT", "to": "coingecko#the-graph"}, "0x0935b271CA903ADA3FFe1Ac1353fC4A49E7EE87b": {"decimals": "18", "symbol": "uIMX", "to": "coingecko#immutable-x"}, "******************************************": {"decimals": "18", "symbol": "uINJ", "to": "coingecko#injective-protocol"}, "******************************************": {"decimals": "18", "symbol": "uATOM", "to": "coingecko#cosmos-hub"}, "******************************************": {"decimals": "18", "symbol": "uFET", "to": "coingecko#fetch-ai"}, "******************************************": {"decimals": "18", "symbol": "uETC", "to": "coingecko#ethereum-classic"}, "******************************************": {"decimals": "18", "symbol": "uAAVE", "to": "coingecko#aave"}, "******************************************": {"decimals": "18", "symbol": "uICP", "to": "coingecko#internet-computer"}, "******************************************": {"decimals": "18", "symbol": "uHBAR", "to": "coingecko#hedera"}, "******************************************": {"decimals": "18", "symbol": "uXLM", "to": "coingecko#stellar"}, "******************************************": {"decimals": "18", "symbol": "uTRUMP", "to": "coingecko#official-trump"}, "******************************************": {"decimals": "18", "symbol": "uPEPE", "to": "coingecko#pepe"}, "******************************************": {"decimals": "18", "symbol": "uRNDR", "to": "coingecko#render"}, "******************************************": {"decimals": "18", "symbol": "uMKR", "to": "coingecko#maker"}, "******************************************": {"decimals": "18", "symbol": "uSTX", "to": "coingecko#blockstack"}, "******************************************": {"decimals": "18", "symbol": "uUNI", "to": "coingecko#uniswap"}, "******************************************": {"decimals": "18", "symbol": "uMATIC", "to": "coingecko#polygon-ecosystem-token"}, "******************************************": {"decimals": "18", "symbol": "uETH", "to": "coingecko#ethereum"}, "******************************************": {"decimals": "18", "symbol": "uLTC", "to": "coingecko#litecoin"}, "******************************************": {"decimals": "18", "symbol": "uDOT", "to": "coingecko#polkadot"}, "******************************************": {"decimals": "18", "symbol": "uBCH", "to": "coingecko#bitcoin-cash"}, "******************************************": {"decimals": "18", "symbol": "uALGO", "to": "coingecko#algorand"}, "******************************************": {"to": "coingecko#agora-dollar", "decimals": 6, "symbol": "AUSD"}, "******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "LBTC"}, "******************************************": {"to": "coingecko#wrapped-steth", "decimals": 18, "symbol": "wstETH"}, "******************************************": {"to": "coingecko#wrapped-eeth", "decimals": 18, "symbol": "weETH"}, "******************************************": {"to": "coingecko#morpho", "decimals": 18, "symbol": "MORPHO"}, "******************************************": {"to": "coingecko#polygon-ecosystem-token", "decimals": 18, "symbol": "POL"}, "******************************************": {"to": "coingecko#sushi", "decimals": 18, "symbol": "SUSHI"}, "******************************************": {"to": "coingecko#yearn-finance", "decimals": 18, "symbol": "YFI"}, "******************************************": {"to": "coingecko#jito-staked-sol", "decimals": 18, "symbol": "JitoSOL"}, "******************************************": {"to": "coingecko#wrapped-solana-universal", "decimals": 18, "symbol": "uSOL"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "vbETH"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "vbUSDC"}, "******************************************": {"to": "coingecko#tether", "decimals": 6, "symbol": "vbUSDT"}, "******************************************": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "vbWBTC"}, "******************************************": {"to": "coingecko#usds", "decimals": 18, "symbol": "vbUSDS"}, "******************************************": {"to": "coingecko#lombard-staked-btc", "decimals": 8, "symbol": "BTCK"}}, "xrplevm": {"******************************************": {"to": "coingecko#ripple", "decimals": 18, "symbol": "XRP"}, "******************************************": {"to": "coingecko#ripple", "decimals": 18, "symbol": "XRP"}, "******************************************": {"to": "coingecko#ripple", "decimals": 18, "symbol": "WXRP"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "WETH"}}, "titan": {"atkx": {"to": "coingecko#tokenize-xchange", "decimals": 18, "symbol": "TKX"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:usdc": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:usdt": {"to": "coingecko#tether", "decimals": 6, "symbol": "USDT"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:eth": {"to": "coingecko#ethereum", "decimals": 18, "symbol": "ETH"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:sol": {"to": "coingecko#solana", "decimals": 9, "symbol": "SOL"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:meow": {"to": "coingecko#meow", "decimals": 8, "symbol": "MEOW"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:oracler": {"to": "coingecko#oracler-ai", "decimals": 6, "symbol": "ORACLER"}, "factory:titan1pvrwmjuusn9wh34j7y520g8gumuy9xtl3gvprlljfdpwju3x7ucsgehpjy:monkeys": {"to": "coingecko#monkeys-2", "decimals": 6, "symbol": "MONKEYS"}, "factory:titan1eyfccmjm6732k7wp4p6gdjwhxjwsvje44j0hfx8nkgrm8fs7vqfsalaj2e:wbtc": {"to": "coingecko#wrapped-bitcoin", "decimals": 8, "symbol": "WBTC"}}, "vechain": {"******************************************": {"to": "coingecko#vechain", "decimals": 18, "symbol": "VET"}, "******************************************": {"to": "coingecko#vechain", "decimals": 18, "symbol": "WVET"}, "******************************************": {"to": "coingecko#vechain", "decimals": 18, "symbol": "VVET"}, "******************************************": {"to": "coingecko#vethor-token", "decimals": 18, "symbol": "VTHO"}, "******************************************": {"to": "coingecko#vebetterdao", "decimals": 18, "symbol": "B3TR"}, "******************************************": {"to": "coingecko#vechain", "decimals": 18, "symbol": "BVET"}}, "dchainmainnet": {"******************************************": {"decimals": "18", "symbol": "WETH", "to": "coingecko#ethereum"}}, "tac": {"******************************************": {"decimals": "18", "symbol": "TAC", "to": "coingecko#tac"}, "******************************************": {"decimals": "18", "symbol": "wstETH", "to": "coingecko#wrapped-steth"}, "******************************************": {"decimals": "8", "symbol": "cbETH", "to": "coingecko#coinbase-wrapped-btc"}, "******************************************": {"decimals": "9", "symbol": "bmTON", "to": "coingecko#bemo"}, "******************************************": {"decimals": "18", "symbol": "WIF", "to": "coingecko#dogwifcoin"}, "******************************************": {"decimals": "8", "symbol": "LBTC", "to": "coingecko#lombard-staked-btc"}, "******************************************": {"decimals": "18", "symbol": "USDN", "to": "coingecko#noon-usn"}, "******************************************": {"decimals": "18", "symbol": "sUSDN", "to": "coingecko#staked-usn"}, "******************************************": {"decimals": "18", "symbol": "USD0", "to": "coingecko#usual-usd"}, "******************************************": {"decimals": "18", "symbol": "USD0++", "to": "coingecko#usd0-liquid-bond"}, "******************************************": {"decimals": "18", "symbol": "USR", "to": "coingecko#resolv-usr"}, "******************************************": {"decimals": "18", "symbol": "RLP", "to": "coingecko#resolv-rlp"}, "******************************************": {"decimals": "18", "symbol": "wstUSR", "to": "coingecko#resolv-wstusr"}, "******************************************": {"to": "coingecko#universal-btc", "decimals": 8, "symbol": "uniBTC"}}, "initia": {"0x8e4733bdabcf7d4afc3d14f0dd46c9bf52fb0fce9e4b996c939e195b8bc891d9": {"decimals": 0, "symbol": "INIT", "to": "coingecko#initia"}}, "eventum": {"******************************************": {"decimals": "6", "symbol": "USDT", "to": "coingecko#tether"}}, "eni": {"******************************************": {"decimals": "18", "symbol": "EGAS", "to": "asset#eni:******************************************"}, "******************************************": {"decimals": "18", "symbol": "USDT", "to": "coingecko#tether"}}, "babylon": {"bbn17y5zvse30629t7r37xsdj73xsqp7qsdr7gpnh966wf5aslpn66rq5ekwsz": {"decimals": 6, "symbol": "cBABY", "to": "coingecko#cube-staked-baby"}}, "peaq": {"******************************************": {"decimals": 6, "symbol": "USDT", "to": "coingecko#tether"}, "******************************************": {"to": "coingecko#usd-coin", "decimals": 6, "symbol": "USDC"}, "******************************************": {"to": "coingecko#peaq-2", "decimals": 18, "symbol": "PEAQ"}, "******************************************": {"to": "coingecko#peaq-2", "decimals": 18, "symbol": "PEAQ"}, "******************************************": {"to": "coingecko#peaq-2", "decimals": 18, "symbol": "PEAQ"}, "******************************************": {"to": "coingecko#auki-labs", "decimals": 18, "symbol": "AUKI"}, "******************************************": {"to": "coingecko#silencio", "decimals": 18, "symbol": "SLC"}, "******************************************": {"to": "coingecko#ovr", "decimals": 18, "symbol": "OVR"}, "******************************************": {"to": "coingecko#airtor-protocol", "decimals": 18, "symbol": "ANYONE"}, "******************************************": {"to": "coingecko#peaq-2", "decimals": 18, "symbol": "PEAQ"}}}