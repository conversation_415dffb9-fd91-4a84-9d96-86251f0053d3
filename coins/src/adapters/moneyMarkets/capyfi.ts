import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import { addToDBWritesList } from "../utils/database";
import { getTokenInfo } from "../utils/erc20";

const CHAIN = "ethereum";
const LAC = "******************************************"; // LAC ERC-20 on Ethereum
const ORACLE = "******************************************"; // AggregatorV3Interface proxy for LAC / USD on Ethereum

// World Chain configuration
const WORLD_CHAIN = "wc";
const worldChainFeeds: { symbol: string; token: string; oracle: string }[] = [
  { symbol: "WARS", token: "******************************************", oracle: "******************************************" },
  { symbol: "LAC", token: "******************************************", oracle: "******************************************" }
];

const CHAINLINK_LATEST_ROUND_ABI =
  "function latestRoundData() view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)";

export async function capyfi(timestamp: number) {
  const writes: Write[] = [];

  // Ethereum LAC via Chainlink
  {
    const api = await getApi(CHAIN, timestamp);
    const result = await api.multiCall({
      calls: [
        {
          target: ORACLE,
        },
      ],
      abi: CHAINLINK_LATEST_ROUND_ABI,
    });
    const price = Number((result as any)[0].answer) / 1e8; // Chainlink answers are 8 decimals

    addToDBWritesList(
      writes,
      CHAIN,
      LAC,
      price,
      18,
      "LAC",
      timestamp,
      "capyfi-oracle",
      0.9,
    );
  }

  // World Chain tokens via provided oracles
  if (worldChainFeeds.length) {
    const wcApi = await getApi(WORLD_CHAIN, timestamp);

    const pricesRes = await wcApi.multiCall({
      calls: worldChainFeeds.map((f) => ({ target: f.oracle })),
      abi: CHAINLINK_LATEST_ROUND_ABI,
      permitFailure: true,
    });

    const tokens = worldChainFeeds.map((f) => f.token);
    const tokenInfo = await getTokenInfo(WORLD_CHAIN, tokens, undefined);

    pricesRes.forEach((res: any, i: number) => {
      if (!res || res.answer === undefined || res.answer === null) return;
      const price = Number(res.answer) / 1e8;
      const decimals = tokenInfo.decimals?.[i]?.output ?? 18;
      const symbol = tokenInfo.symbols?.[i]?.output ?? worldChainFeeds[i].symbol;

      addToDBWritesList(
        writes,
        WORLD_CHAIN,
        worldChainFeeds[i].token,
        price,
        Number(decimals),
        symbol,
        timestamp,
        "capyfi-oracle",
        0.9,
      );
    });
  }

  return writes;
}