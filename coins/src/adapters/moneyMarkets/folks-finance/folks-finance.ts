import { Write } from "../../utils/dbInterfaces";
import { getApi } from "../../utils/sdk";
import { formatUnits } from "ethers";
import { addToDBWritesList } from "../../utils/database";

const oracleManager: string = "******************************************";

const pools: { [id: string]: string } = {
  "1": "******************************************", // fUSDC
  "2": "******************************************", // fAVAX
  "3": "******************************************", // fsAVAX
  "4": "******************************************", // fETH_eth
  "5": "******************************************", // fETH_base
  "6": "******************************************", // fwETH_ava
  "7": "******************************************", // fwBTC_eth
  "8": "******************************************", // fBTCb_ava
  "9": "******************************************", // fcbBTC_base
  "10": "******************************************", // fBNB
  "11": "******************************************", // fETHB_bsc
  "12": "******************************************", // fBTCB_bsc
  "13": "******************************************", // fETH_arbitrum
  "14": "******************************************", // fARB
  "15": "******************************************", // fSolvBTC
  "16": "******************************************", // fJOE
  "17": "******************************************", // fggAVAX
  "19": "******************************************", // fPOL
  "20": "******************************************", // fwBTC_pol
  "21": "******************************************", // fwETH_pol
};

export default async function getTokenPrice(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp);
  const writes: Write[] = [];

  const [prices, interestIndexes, decimals, symbols] = await Promise.all([
    api.multiCall({
      calls: Object.keys(pools).map((poolId) => ({
        target: oracleManager,
        params: poolId,
      })),
      abi: "function processPriceFeed(uint8 poolId) view returns (uint256 price, uint8 decimals)",
    }),
    api.multiCall({
      calls: Object.values(pools).map((fTokenAddress) => ({
        target: fTokenAddress,
      })),
      abi: "function getUpdatedDepositInterestIndex() view returns (uint256 interestIndex)",
    }),
    api.multiCall({
      calls: Object.values(pools).map((fTokenAddress) => ({
        target: fTokenAddress,
      })),
      abi: "erc20:decimals",
    }),
    api.multiCall({
      calls: Object.values(pools).map((fTokenAddress) => ({
        target: fTokenAddress,
      })),
      abi: "erc20:symbol",
    }),
  ]);

  Object.values(pools).map((token, i) =>
    addToDBWritesList(
      writes,
      chain,
      token,
      parseFloat(
        formatUnits(BigInt(prices[i].price) * BigInt(interestIndexes[i]), 36),
      ),
      decimals[i],
      symbols[i],
      timestamp,
      "folks",
      0.9,
    ),
  );

  return writes;
}
