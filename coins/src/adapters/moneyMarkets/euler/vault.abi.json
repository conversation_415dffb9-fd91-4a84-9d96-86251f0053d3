[{"inputs": [{"components": [{"internalType": "address", "name": "evc", "type": "address"}, {"internalType": "address", "name": "protocolConfig", "type": "address"}, {"internalType": "address", "name": "sequenceRegistry", "type": "address"}, {"internalType": "address", "name": "balanceTracker", "type": "address"}, {"internalType": "address", "name": "permit2", "type": "address"}], "internalType": "struct Base.Integrations", "name": "integrations", "type": "tuple"}, {"components": [{"internalType": "address", "name": "initialize", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "vault", "type": "address"}, {"internalType": "address", "name": "borrowing", "type": "address"}, {"internalType": "address", "name": "liquidation", "type": "address"}, {"internalType": "address", "name": "riskManager", "type": "address"}, {"internalType": "address", "name": "balanceForwarder", "type": "address"}, {"internalType": "address", "name": "governance", "type": "address"}], "internalType": "struct Dispatch.DeployedModules", "name": "modules", "type": "tuple"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "E_AccountLiquidity", "type": "error"}, {"inputs": [], "name": "E_AmountTooLargeToEncode", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "E_BadAssetReceiver", "type": "error"}, {"inputs": [], "name": "E_BadBorrowCap", "type": "error"}, {"inputs": [], "name": "E_BadCollateral", "type": "error"}, {"inputs": [], "name": "<PERSON>_Bad<PERSON>ee", "type": "error"}, {"inputs": [], "name": "E_BadMaxLiquidationDiscount", "type": "error"}, {"inputs": [], "name": "E_BadSharesOwner", "type": "error"}, {"inputs": [], "name": "E_BadSharesReceiver", "type": "error"}, {"inputs": [], "name": "E_BadSupplyCap", "type": "error"}, {"inputs": [], "name": "E_BorrowCapExceeded", "type": "error"}, {"inputs": [], "name": "E_CheckUnauthorized", "type": "error"}, {"inputs": [], "name": "E_CollateralDisabled", "type": "error"}, {"inputs": [], "name": "E_ConfigAmountTooLargeToEncode", "type": "error"}, {"inputs": [], "name": "E_ControllerDisabled", "type": "error"}, {"inputs": [], "name": "E_DebtAmountTooLargeToEncode", "type": "error"}, {"inputs": [], "name": "E_EmptyError", "type": "error"}, {"inputs": [], "name": "E_ExcessiveRepayAmount", "type": "error"}, {"inputs": [], "name": "E_FlashLoanNotRepaid", "type": "error"}, {"inputs": [], "name": "E_Initialized", "type": "error"}, {"inputs": [], "name": "E_InsufficientAllowance", "type": "error"}, {"inputs": [], "name": "E_InsufficientAssets", "type": "error"}, {"inputs": [], "name": "E_InsufficientBalance", "type": "error"}, {"inputs": [], "name": "E_InsufficientCash", "type": "error"}, {"inputs": [], "name": "E_InsufficientDebt", "type": "error"}, {"inputs": [], "name": "E_InvalidLTVAsset", "type": "error"}, {"inputs": [], "name": "E_LTVBorrow", "type": "error"}, {"inputs": [], "name": "E_LTVLiquidation", "type": "error"}, {"inputs": [], "name": "E_LiquidationCoolOff", "type": "error"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "E_NoLiability", "type": "error"}, {"inputs": [], "name": "E_NoPriceOracle", "type": "error"}, {"inputs": [], "name": "E_NotController", "type": "error"}, {"inputs": [], "name": "E_NotHookTarget", "type": "error"}, {"inputs": [], "name": "E_NotSupported", "type": "error"}, {"inputs": [], "name": "E_OperationDisabled", "type": "error"}, {"inputs": [], "name": "E_OutstandingDebt", "type": "error"}, {"inputs": [], "name": "E_ProxyMetadata", "type": "error"}, {"inputs": [], "name": "E_Reentrancy", "type": "error"}, {"inputs": [], "name": "E_RepayTooMuch", "type": "error"}, {"inputs": [], "name": "E_SelfLiquidation", "type": "error"}, {"inputs": [], "name": "E_SelfTransfer", "type": "error"}, {"inputs": [], "name": "E_SupplyCapExceeded", "type": "error"}, {"inputs": [], "name": "E_TransientState", "type": "error"}, {"inputs": [], "name": "E_Unauthorized", "type": "error"}, {"inputs": [], "name": "E_ViolatorLiquidityDeferred", "type": "error"}, {"inputs": [], "name": "E_ZeroAssets", "type": "error"}, {"inputs": [], "name": "E_ZeroShares", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "status", "type": "bool"}], "name": "BalanceForwarderStatus", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "Borrow", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "protocolReceiver", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "protocolShares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "governorShares", "type": "uint256"}], "name": "ConvertFees", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "DebtSocialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "asset", "type": "address"}, {"indexed": false, "internalType": "address", "name": "dToken", "type": "address"}], "name": "<PERSON><PERSON><PERSON>Created", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "newSupplyCap", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newBorrowCap", "type": "uint16"}], "name": "GovSetCaps", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "newConfigFlags", "type": "uint32"}], "name": "GovSetConfigFlags", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newFeeReceiver", "type": "address"}], "name": "GovSetFeeReceiver", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newGovernorAdmin", "type": "address"}], "name": "GovSetGovernorAdmin", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newHookTarget", "type": "address"}, {"indexed": false, "internalType": "uint32", "name": "newHookedOps", "type": "uint32"}], "name": "GovSetHookConfig", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "new<PERSON>ee", "type": "uint16"}], "name": "GovSetInterestFee", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newInterestRateModel", "type": "address"}], "name": "GovSetInterestRateModel", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "collateral", "type": "address"}, {"indexed": false, "internalType": "uint16", "name": "borrowLTV", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "liquidationLTV", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "initialLiquidationLTV", "type": "uint16"}, {"indexed": false, "internalType": "uint48", "name": "targetTimestamp", "type": "uint48"}, {"indexed": false, "internalType": "uint32", "name": "rampDuration", "type": "uint32"}], "name": "GovSetLTV", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "newCoolOffTime", "type": "uint16"}], "name": "GovSetLiquidationCoolOffTime", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint16", "name": "newDiscount", "type": "uint16"}], "name": "GovSetMaxLiquidationDiscount", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "InterestAccrued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "liquidator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "violator", "type": "address"}, {"indexed": false, "internalType": "address", "name": "collateral", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "repayAssets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "yieldBalance", "type": "uint256"}], "name": "Liquidate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "<PERSON>ay", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "totalShares", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalBorrows", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "accumulatedFees", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "cash", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "interestAccumulator", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "interestRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "VaultStatus", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "sender", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "assets", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "Withdraw", "type": "event"}, {"inputs": [], "name": "EVC", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collateral", "type": "address"}], "name": "LTVBorrow", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collateral", "type": "address"}], "name": "LTVFull", "outputs": [{"internalType": "uint16", "name": "borrowLTV", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationLTV", "type": "uint16"}, {"internalType": "uint16", "name": "initialLiquidationLTV", "type": "uint16"}, {"internalType": "uint48", "name": "targetTimestamp", "type": "uint48"}, {"internalType": "uint32", "name": "rampDuration", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collateral", "type": "address"}], "name": "LTVLiquidation", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LTVList", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_BALANCE_FORWARDER", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_BORROWING", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_GOVERNANCE", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_INITIALIZE", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_LIQUIDATION", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_RISKMANAGER", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_TOKEN", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MODULE_VAULT", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "liquidation", "type": "bool"}], "name": "accountLiquidity", "outputs": [{"internalType": "uint256", "name": "collateralValue", "type": "uint256"}, {"internalType": "uint256", "name": "liabilityValue", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bool", "name": "liquidation", "type": "bool"}], "name": "accountLiquidityFull", "outputs": [{"internalType": "address[]", "name": "collaterals", "type": "address[]"}, {"internalType": "uint256[]", "name": "collateralValues", "type": "uint256[]"}, {"internalType": "uint256", "name": "liabilityValue", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "accumulatedFees", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "accumulatedFeesAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "holder", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "asset", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceForwarderEnabled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "balanceTrackerAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "borrow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "caps", "outputs": [{"internalType": "uint16", "name": "supplyCap", "type": "uint16"}, {"internalType": "uint16", "name": "borrowCap", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cash", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address[]", "name": "collaterals", "type": "address[]"}], "name": "checkAccountStatus", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "liquidator", "type": "address"}, {"internalType": "address", "name": "violator", "type": "address"}, {"internalType": "address", "name": "collateral", "type": "address"}], "name": "checkLiquidation", "outputs": [{"internalType": "uint256", "name": "maxRepay", "type": "uint256"}, {"internalType": "uint256", "name": "max<PERSON>ield", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkVaultStatus", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "config<PERSON><PERSON>s", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "convertFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "convertToAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "convertToShares", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "creator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "dToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "debtOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "debtOfExact", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "disableController", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "enableBalanceForwarder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "flashLoan", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "hookConfig", "outputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "proxyCreator", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "interestAccumulator", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "interestFee", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "interestRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "interestRateModel", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "violator", "type": "address"}, {"internalType": "address", "name": "collateral", "type": "address"}, {"internalType": "uint256", "name": "repayAssets", "type": "uint256"}, {"internalType": "uint256", "name": "minYieldBalance", "type": "uint256"}], "name": "liquidate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "liquidationCoolOffTime", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "maxDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxLiquidationDiscount", "outputs": [{"internalType": "uint16", "name": "", "type": "uint16"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "maxMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "max<PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "max<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "oracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "permit2Address", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "previewDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "previewMint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}], "name": "previewRedeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "assets", "type": "uint256"}], "name": "previewWithdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolConfigAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolFeeReceiver", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "protocolFeeShare", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "from", "type": "address"}], "name": "pullDebt", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "repay", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "repayWithShares", "outputs": [{"internalType": "uint256", "name": "shares", "type": "uint256"}, {"internalType": "uint256", "name": "debt", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "supplyCap", "type": "uint16"}, {"internalType": "uint16", "name": "borrowCap", "type": "uint16"}], "name": "setCaps", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "newConfigFlags", "type": "uint32"}], "name": "setConfigFlags", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newFeeReceiver", "type": "address"}], "name": "setFeeReceiver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newGovernorAdmin", "type": "address"}], "name": "setGovernorAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newHookTarget", "type": "address"}, {"internalType": "uint32", "name": "newHookedOps", "type": "uint32"}], "name": "setHookConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "new<PERSON>ee", "type": "uint16"}], "name": "setInterestFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newModel", "type": "address"}], "name": "setInterestRateModel", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collateral", "type": "address"}, {"internalType": "uint16", "name": "borrowLTV", "type": "uint16"}, {"internalType": "uint16", "name": "liquidationLTV", "type": "uint16"}, {"internalType": "uint32", "name": "rampDuration", "type": "uint32"}], "name": "setLTV", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "newCoolOffTime", "type": "uint16"}], "name": "setLiquidationCoolOffTime", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "newDiscount", "type": "uint16"}], "name": "setMaxLiquidationDiscount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}], "name": "skim", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalBorrows", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalBorrowsExact", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "touch", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "transferFromMax", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unitOfAccount", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "viewDelegate", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "withdraw", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}]