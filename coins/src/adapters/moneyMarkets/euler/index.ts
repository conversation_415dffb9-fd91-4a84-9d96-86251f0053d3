import getTokenPrices from "./euler";
import getEulerV2TokenPrices from "./eulerV2";

function euler(timestamp: number = 0) {
  return getTokenPrices("ethereum", timestamp);
}

const v2ChainConfigs = {
  ethereum: {
    factory: "******************************************",
    vaultLens: "******************************************",
    fromBlock: 20529225,
    vaultCount: 432,
  },
  bob: {
    factory: "******************************************",
    vaultLens: "******************************************",
    fromBlock: 12266832,
    vaultCount: 24,
  },
  sonic: {
    factory: "******************************************",
    vaultLens: "******************************************",
    fromBlock: 5324454,
    vaultCount: 77,
  },
  avax: {
    factory: "******************************************",
    vaultLens: "******************************************",
    fromBlock: 56805794,
    vaultCount: 68,
  },
  berachain: {
    factory: "******************************************",
    vaultLens: "******************************************",
    fromBlock: 786314,
    vaultCount: 0,
  },
  bsc: {
    factory: "******************************************",
    vaultLens: "0xBfD019C90e8Ca8286f9919DF31c25BF989C6bD46",
    fromBlock: 46370655,
    vaultCount: 58,
  },
  base: {
    factory: "0x7F321498A801A191a93C840750ed637149dDf8D0",
    vaultLens: "0xCCC8D18e40c439F5234042FbEA0f4f1528f52f00",
    fromBlock: 22282408,
    vaultCount: 126,
  },
  // swellchain: {
  //   factory: "0x238bF86bb451ec3CA69BB855f91BDA001aB118b9",
  //   vaultLens: "0x1f1997528FbD68496d8007E65599637fBBe85582",
  //   fromBlock: 2350701,
  //   vaultCount: 0,
  // },
  unichain: {
    factory: "0xbAd8b5BDFB2bcbcd78Cc9f1573D3Aad6E865e752",
    vaultLens: "0x03833b4A873eA1F657340C72971a2d0EbB2B4D82",
    fromBlock: 8541544,
    vaultCount: 37,
  },
};

function eulerV2(timestamp: number = 0) {
  return Promise.all(
    Object.entries(v2ChainConfigs).map(([chain, config]) =>
      getEulerV2TokenPrices(
        chain,
        timestamp,
        config.factory,
        config.fromBlock,
      ),
    ),
  );
}

export const adapters = {
  // euler,
  eulerV2,
};
