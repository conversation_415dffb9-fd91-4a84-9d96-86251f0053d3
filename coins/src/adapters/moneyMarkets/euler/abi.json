{"convertBalanceToUnderlying": {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "name": "convertBalanceToUnderlying", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "underlyingAsset": {"inputs": [], "name": "underlyingAsset", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}}