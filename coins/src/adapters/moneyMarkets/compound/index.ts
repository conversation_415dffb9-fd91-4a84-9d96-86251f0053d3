import { compoundPrices } from "../../utils/compound-fork";
import getTokenPrices from "./compound";
import v3 from "./v3";

export function compound(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "ethereum",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function venus(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "bsc",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "era",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function ironbank(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "ethereum",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function benqi(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "avax",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function rari(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "arbitrum",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function cream(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "arbitrum",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "polygon",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "bsc",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function ironBank(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "optimism",
      "0xE0B57FEEd45e7D908f2d0DaCd26F113Cf26715BF",
      timestamp,
    ),
    getTokenPrices(
      "fantom",
      "0x4250a6d3bd57455d7c6821eecb6206f507576cd2",
      timestamp,
    ),
    getTokenPrices(
      "avax",
      "0x2eE80614Ccbc5e28654324a66A396458Fa5cD7Cc",
      timestamp,
    ),
  ]);
}
export function Ovix(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "polygon",
      "0x8849f1a0cB6b5D6076aB150546EddEe193754F1C",
      timestamp,
    ),
    getTokenPrices(
      "polygon_zkevm",
      "0x6EA32f626e3A5c41547235ebBdf861526e11f482",
      timestamp,
    ),
  ]);
}
export function scream(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "fantom",
      "0x260e596dabe3afc463e75b6cc05d8c46acacfb09",
      timestamp,
    ),
  ]);
}
export function aurigami(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "aurora",
      "0x817af6cfAF35BdC1A634d6cC94eE9e4c68369Aeb",
      timestamp,
    ),
  ]);
}
export function traderjoe(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "avax",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function mare(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "kava",
      "******************************************",
      timestamp,
    ),
  ]);
}
export function tonpound(timestamp: number = 0) {
  return getTokenPrices(
    "ethereum",
    "******************************************",
    timestamp,
  );
}

export function lodestar(timestamp: number = 0) {
  return getTokenPrices(
    "arbitrum",
    "******************************************",
    timestamp,
  );
}
export function marev2(timestamp: number = 0) {
  return getTokenPrices(
    "kava",
    "******************************************",
    timestamp,
  );
}
export function tenderfi(timestamp: number = 0) {
  return getTokenPrices(
    "arbitrum",
    "******************************************",
    timestamp,
  );
}

export function cantoLending(timestamp: number = 0) {
  return compoundPrices({
    chain: "canto",
    timestamp,
    cether: "******************************************",
    comptroller: "******************************************",
    projectName: "canto-lending",
  });
}
export function sumerian(timestamp: number = 0) {
  return getTokenPrices(
    "meter",
    "******************************************",
    timestamp,
  );
}
export function hover(timestamp: number = 0) {
  return getTokenPrices(
    "kava",
    "******************************************",
    timestamp,
  );
}

export function moonwell(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices(
      "base",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "optimism",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "moonbeam",
      "******************************************",
      timestamp,
    ),
    getTokenPrices(
      "moonriver",
      "******************************************",
      timestamp,
    ),
  ]);
}

export function orbitv2(timestamp: number = 0) {
  return getTokenPrices(
    "blast",
    "0x1E18C3cb491D908241D0db14b081B51be7B6e652",
    timestamp,
  );
}

export function segmentFinance(timestamp: number = 0) {
  return getTokenPrices(
    "bob",
    "0xcD7C4F508652f33295F0aEd075936Cd95A4D2911",
    timestamp,
  );
}

export function compoundV3(timestamp: number = 0) {
  return v3(timestamp);
}

export const adapters = {
  cantoLending,
  moonwell,
  hover,
  sumerian,
  compound,
  venus,
  ironbank,
  benqi,
  traderjoe,
  rari,
  aurigami,
  cream,
  scream,
  tonpound,
  lodestar,
  marev2,
  tenderfi,
  Ovix,
  mare,
  orbitv2,
  ironBank,
  segmentFinance,
  compoundV3,
};
