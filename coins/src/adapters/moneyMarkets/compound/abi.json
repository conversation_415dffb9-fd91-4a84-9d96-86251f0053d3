{"exchangeRateStored": {"constant": true, "inputs": [], "name": "exchangeRateStored", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, "getAllMarkets": {"constant": true, "inputs": [], "name": "getAllMarkets", "outputs": [{"internalType": "contract CToken[]", "name": "", "type": "address[]"}], "payable": false, "stateMutability": "view", "type": "function"}, "underlying": {"constant": true, "inputs": [], "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}}