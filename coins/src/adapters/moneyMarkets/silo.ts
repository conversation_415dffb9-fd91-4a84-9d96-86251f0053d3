import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";
import * as sdk from '@defillama/sdk'
import { getLogs } from "../../utils/cache/getLogs";

const config: any = {
  ethereum: {
    factories: [
      {
        START_BLOCK: 15307294,
        SILO_FACTORY: '******************************************', // Silo Ethereum (Original)
      },
      {
        START_BLOCK: 17391885,
        SILO_FACTORY: '******************************************' // Silo Ethereum (Convex Factory)
      },
      {
        START_BLOCK: 17782576,
        SILO_FACTORY: '******************************************' // Silo Ethereum (LLAMA Edition)
      }
    ]
  },
  arbitrum: {
    factories: [
      {
        START_BLOCK: 51894508,
        SILO_FACTORY: '******************************************', // Silo Arbitrum (Original)
      }
    ]
  },
}

export function silo(timestamp: number = 0) {
  return Promise.all(
    Object.keys(config).map((chain) => getTokenPrice(chain, timestamp)),
  );
}
const fallbackBlacklist = ["******************************************",];

async function getSilos(api: sdk.ChainApi) {
  const chain = api.chain
  let logs: any[] = [];
  for (let factory of config[chain].factories) {
    const { SILO_FACTORY, START_BLOCK, } = factory;
    let logChunk = await getLogs({
      api,
      target: SILO_FACTORY,
      fromBlock: START_BLOCK,
      topic: 'NewSiloCreated(address,address,uint128)',
    })
    logs = [...logs, ...logChunk];
  }

  return logs.map((log: any) => `0x${log.topics[1].substring(26)}`).filter((address: any) => fallbackBlacklist.indexOf(address.toLowerCase()) === -1);
}

async function getTokenPrice(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp)
  const pricesObject: any = {}
  const writes: Write[] = [];
  const silos = await getSilos(api)
  const assetsAndStates = await api.multiCall({ abi: abi.getAssetsWithState, calls: silos })
  const collTokens = assetsAndStates.map((v0: any) => v0.assetsStorage.map((v: any, i: number) => [
    { token: v.collateralToken, balance: v.totalDeposits, uToken: v0.assets[i] },
    { token: v.collateralOnlyToken, balance: v.collateralOnlyDeposits, uToken: v0.assets[i] },
  ]).flat()).flat().filter((v: any) => +v.balance > 0)
  const tokenSupplies = await api.multiCall({ abi: 'erc20:totalSupply', calls: collTokens.map((v: any) => v.token) })
  collTokens.forEach((v: any, i: number) => {
    pricesObject[v.token] = { underlying: v.uToken, price: v.balance / tokenSupplies[i] }
  })
  // add debt tokens
  assetsAndStates.forEach((v0: any) => {
    v0.assetsStorage.forEach((v: any, i: number) => {
      pricesObject[v.debtToken] = { underlying: v0.assets[i], price: -1 }
    })
  })
  return getWrites({ chain, timestamp, writes, pricesObject, projectName: 'silo' })
}

const abi = {
  "getAssetsWithState": "function getAssetsWithState() view returns (address[] assets, tuple(address collateralToken, address collateralOnlyToken, address debtToken, uint256 totalDeposits, uint256 collateralOnlyDeposits, uint256 totalBorrowAmount)[] assetsStorage)",
}