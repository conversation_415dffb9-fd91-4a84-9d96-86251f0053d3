import getTokenPrices from "./aave";

export function aave(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices("base", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("optimism", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("arbitrum", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("ethereum", "******************************************", null, "v2", timestamp,),
    getTokenPrices("ethereum", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("ethereum",  "******************************************", null, "v3", timestamp), 
    // AMM market has no registry
    //getTokenPrices("ethereum", "******************************************");
    getTokenPrices("polygon", "******************************************", null, "v2", timestamp,),
    //polygon V3
    getTokenPrices("polygon", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("avax", "******************************************", null, "v2", timestamp,),
    //avax V3
    getTokenPrices("avax", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("scroll", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("metis", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("bsc", "******************************************", "******************************************", "v3", timestamp,),
    getTokenPrices("xdai", "0x1236010CECea55998384e795B59815D871f5f94d", "0x02e9b27599C4Bf8f789d34b6E65C51092c3d9FA6", "v3", timestamp,),
    getTokenPrices("era", "0x0753E3637ddC6efc40759D9c347251046644F25F", "0x1Bc8dbF1f5aF8094Aa166098131116CaAd6B22F9", "v3", timestamp,),
    getTokenPrices("sonic", "0x5E5D87858592d211fdb4503F09FB2B5cf805cB51", null, "v3", timestamp,),
  ]);
}
export function geist(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices("fantom", "0x4CF8E50A5ac16731FA2D8D9591E195A285eCaA82", null, "v2", timestamp,),
  ]);
}
export function radiant(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices("arbitrum", "0x7BB843f889e3a0B307299c3B65e089bFfe9c0bE0", null, "v2", timestamp,),
    getTokenPrices("arbitrum", "******************************************", null, "v2", timestamp,),
  ]);
}
export function klap(timestamp: number = 0) {
  return Promise.all([
    getTokenPrices("klaytn", "******************************************", null, "v2", timestamp,),
  ]);
}
export function valas(timestamp: number = 0) {
  return getTokenPrices("bsc", "******************************************", null, "v2", timestamp,);
}
export function uwulend(timestamp: number = 0) {
  return getTokenPrices("ethereum", "******************************************", null, "v2", timestamp,);
}

export function bonzo(timestamp: number = 0) {
  return getTokenPrices("hedera", "******************************************", null, "v2", timestamp, {
    lendingPool: '******************************************'
  });
}

export function hyperlend(timestamp: number = 0) {
  return getTokenPrices("hyperliquid", "******************************************", null, "v3", timestamp);
}

export function hypurrfi(timestamp: number = 0) {
  return getTokenPrices("hyperliquid", "******************************************", null, "v3", timestamp, {
    lendingPool: "******************************************",
  });
}

export function sparkLend(timestamp: number = 0) {
  return Promise.all([
     getTokenPrices("ethereum", "******************************************", null, "v3", timestamp),
     getTokenPrices("xdai", "******************************************", null, "v3", timestamp)
  ])
}

export const adapters = {
  aave,
  geist,
  radiant,
  uwulend,
  bonzo,
  //klap,
  //valas,
  hyperlend, 
  hypurrfi, 
  sparkLend
};
