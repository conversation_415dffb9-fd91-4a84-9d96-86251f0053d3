import { Write } from "../../utils/dbInterfaces";
import getWrites from "../../utils/getWrites";
import { getApi } from "../../utils/sdk";

export const config = {
  ethereum: {
    pools: [
      { version: "v2", pool: "******************************************" },
      { version: "v3", pool: "******************************************" },
    ],
  },
  polygon: {
    pools: [
      { version: "v2", pool: "******************************************" },
      { version: "v3", pool: "******************************************" },
    ],
  },
  avax: {
    pools: [
      { version: "v2", pool: "******************************************" },
      { version: "v3", pool: "******************************************" },
    ],
  },
  base: {
    pools: [
      { version: "v3", pool: "******************************************" },
    ],
  },
  arbitrum: {
    pools: [
      { version: "v3", pool: "******************************************" },
    ],
  },
  hyperliquid: {
    pools: [
      { version: "v3", pool: "******************************************" },
      { version: "v3", pool: "******************************************" },
    ],
  },
} as any;

export default async function getTokenPrices(chain: string, timestamp: number) {
  const { pools } = config[chain];
  const writes: Write[] = [];

  const api = await getApi(chain, timestamp, true);
  const pricesObject: any = {};
  for (const { version, pool } of pools) {
    const tokens = await api.call({ abi: abi.getReservesList, target: pool });
    const data = await api.multiCall({
      abi: abi.getReserveData[version],
      calls: tokens,
      target: pool,
    });
    tokens.forEach((underlying: any, i: number) => {
      pricesObject[data[i].variableDebtTokenAddress] = {
        underlying,
        price: -1,
      };
      pricesObject[data[i].stableDebtTokenAddress] = { underlying, price: -1 };
    });
  }

  await getWrites({
    chain,
    timestamp,
    writes,
    pricesObject,
    projectName: "aave-debt",
  });

  return writes.filter((w: Write) => w.symbol?.toLowerCase().includes("debt"));
}

const abi: any = {
  getReservesList: "address[]:getReservesList",
  getReserveData: {
    v3: "function getReserveData(address asset) view returns (((uint256 data) configuration, uint128 liquidityIndex, uint128 currentLiquidityRate, uint128 variableBorrowIndex, uint128 currentVariableBorrowRate, uint128 currentStableBorrowRate, uint40 lastUpdateTimestamp, uint16 id, address aTokenAddress, address stableDebtTokenAddress, address variableDebtTokenAddress, address interestRateStrategyAddress, uint128 accruedToTreasury, uint128 unbacked, uint128 isolationModeTotalDebt))",
    v2: "function getReserveData(address asset) view returns (((uint256 data) configuration, uint128 liquidityIndex, uint128 variableBorrowIndex, uint128 currentLiquidityRate, uint128 currentVariableBorrowRate, uint128 currentStableBorrowRate, uint40 lastUpdateTimestamp, address aTokenAddress, address stableDebtTokenAddress, address variableDebtTokenAddress, address interestRateStrategyAddress, uint8 id))",
  },
};
