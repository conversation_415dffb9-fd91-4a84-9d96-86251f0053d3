import fetch from "node-fetch";
import { Write } from "../../utils/dbInterfaces";
import { addToDBWritesList } from "../../utils/database";
import { chainIdMap } from "../../bridges/celer";

type Asset = {
  chainId: number;
  address: string;
  decimals: number;
  symbol: string;
  price: number;
};

const allAssets: { [chainId: string]: string[] } = {
  1: [
    // ethereum
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
  ],
  56: [
    // bsc
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
  ],
  146: [
    // sonic
    "******************************************",
    "******************************************",
  ],
  8453: [
    // base
    "******************************************",
    // "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
  ],
  42161: [
    // arbitrum
    "0x86aacbed3e7b3d33149d5dcfd2def3c6d8498b8b",
    "0xb6cfcf89a7b22988bfc96632ac2a9d6dab60d641",
    "0x5402b5f40310bded796c7d0f3ff6683f5c0cffdf",
    "0xb7ffe52ea584d2169ae66e7f0423574a5e15056f",
  ],
};

export async function getApiPrices(timestamp: number) {
  if (timestamp != 0) return;
  const writes: Write[] = [];

  await Promise.all(
    Object.keys(allAssets).map(async (chainId: string) => {
      try {
        let addressesString: string = "";
        allAssets[chainId].map(
          (asset: string, i: number) =>
            (addressesString += `${i == 0 ? "" : ","}${asset}`),
        );

        const { prices } = await fetch(
          `https://api-v2.pendle.finance/core/v1/${chainId}/assets/prices?addresses=${addressesString}&skip=0`,
        ).then((r) => r.json());

        const metadatas = await fetch(
          `https://api-v2.pendle.finance/core/v3/${chainId}/assets/all`,
        ).then((r) => r.json());

        Object.keys(prices).map((address: string) => {
          const metadata = metadatas.assets.find(
            (m: Asset) => (m.address = address.toLowerCase()),
          );
          if (!metadata) return;
          const { decimals, symbol } = metadata;

          addToDBWritesList(
            writes,
            chainIdMap[Number(chainId)],
            address,
            prices[address],
            decimals,
            symbol,
            timestamp,
            "pendle-api",
            0.5,
          );
        });
      } catch (e) {
        throw new Error(`Pendle API adapter failed with: ${e}`);
      }
    }),
  );

  return writes;
}
