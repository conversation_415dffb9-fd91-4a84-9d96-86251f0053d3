import getTokenPrices from "./pendle";
import { getPenpiePrices } from "./penpie";

const config: { [chain: string]: { pendleOracle: string } } = {
  ethereum: {
    pendleOracle: "******************************************",
  },
  arbitrum: {
    pendleOracle: "******************************************",
  },
  bsc: {
    pendleOracle: "******************************************",
  },
  optimism: {
    pendleOracle: "******************************************",
  },
  mantle: {
    pendleOracle: "******************************************",
  },
  sonic: {
    pendleOracle: "******************************************",
  },
  base: {
    pendleOracle: "******************************************",
  },
  berachain: {
    pendleOracle: "******************************************",
  },
  hyperliquid: {
    pendleOracle: "******************************************",
  },
};

export async function pendle(timestamp: number = 0) {
  return Promise.all([
    ...Object.keys(config).map((chain: string) =>
      getTokenPrices(timestamp, chain, config[chain]),
    ),
    // getApiPrices(timestamp),
  ]);
}

const masters: { [chain: string]: { target: string; fromBlock: number } } = {
  arbitrum: {
    target: "******************************************",
    fromBlock: 97640252,
  },
  ethereum: {
    target: "******************************************",
    fromBlock: 17406748,
  },
  bsc: {
    target: "******************************************",
    fromBlock: 29693582,
  },
};

export async function penpie(timestamp: number = 0) {
  return Promise.all(
    Object.keys(masters).map((chain: string) =>
      getPenpiePrices(timestamp, chain, masters[chain]),
    ),
  );
}
