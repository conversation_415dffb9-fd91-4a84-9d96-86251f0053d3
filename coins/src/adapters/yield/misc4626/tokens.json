{"ethereum": {"apxETH": "******************************************", "auraweETH/rETH-vault": "******************************************", "sDAI": "******************************************", "dWETH3": "******************************************", "dWBTC3": "******************************************", "dUSDC3": "******************************************", "wa2DAI": "******************************************", "wa2WETH": "******************************************", "maUSDC": "******************************************", "maUSDT": "******************************************", "BB_wstETH4626": "******************************************", "BB_a_WETH": "******************************************", "uCVX": "******************************************", "steakUSDC": "******************************************", "sUSDe": "******************************************", "stUSD": "******************************************", "stUSDs": "******************************************", "sdeUSD": "******************************************", "saETH": "******************************************", "reALT": "******************************************", "wUSDL": "******************************************", "WOETH": "******************************************", "sUSDS": "******************************************", "pWBTC": "******************************************", "maxETH": "******************************************", "aCVX": "******************************************", "fUSDC": "******************************************", "stZENT": "******************************************", "Re7LRT": "******************************************", "amphrETH": "******************************************", "steakLRT": "******************************************", "rstETH": "******************************************", "yUSD": "******************************************", "ylpumpBTC": "******************************************", "ylstETH": "******************************************", "ylBTCLST": "******************************************", "ylrsETH": "******************************************", "waBTC": "******************************************", "yluniBTC": "******************************************", "MC-USR": "******************************************", "eUSD": "******************************************", "fGHO": "******************************************", "eUSD0-4": "******************************************", "fUSDT": "******************************************", "mhyETH": "******************************************", "gwstETHv3": "******************************************", "vyUSD": "******************************************", "gtmsETHc": "******************************************", "gtmsUSDc": "******************************************", "stkGHO.v1": "******************************************", "stkwaEthUSDT.v1": "******************************************", "stkwaEthUSDC.v1": "******************************************", "sYUSD": "******************************************", "AIDaUSDC": "******************************************", "AIDaUSDT": "******************************************", "uptBTC": "******************************************", "wsrUSD": "******************************************", "SYUSD": "******************************************", "upUSDC": "******************************************", "ysUSDC": "******************************************", "sUSDp": "******************************************", "sUSDS+s": "******************************************", "SKY+": "******************************************", "sUSDC": "******************************************", "sparkUSDS": "******************************************", "pUSDe": "******************************************"}, "arbitrum": {"voltGNS": "******************************************", "UVRT": "******************************************", "gDAI": "******************************************", "plvGLP": "******************************************", "waDAI+": "******************************************", "waUSD+": "******************************************", "rf-grainDAI": "0x12f256109E744081F633a827BE80E06d97ff7447", "rf-granUSDT": "******************************************", "rf-grainUSDC": "******************************************", "stUSD": "******************************************", "dUSDC": "******************************************", "dUSDT": "******************************************", "oLP": "******************************************", "fUSDC": "******************************************", "fUSDT": "******************************************", "sUSDC": "******************************************"}, "polygon": {"maxETH": "******************************************", "maxUSDC": "******************************************", "gDAI": "******************************************"}, "avax": {"ggAVA": "******************************************", "GVRT": "******************************************", "UVRT": "******************************************", "sBUIDL": "******************************************"}, "optimism": {"auraBPT-rETH-ETH-vault": "******************************************", "auraECLP-wstETH-WETH-vault": "******************************************", "yUSD": "******************************************", "sUSDC": "******************************************"}, "kava": {"mCurveLP-MIM-USDT": "******************************************"}, "linea": {"wDAI": "******************************************"}, "blast": {"ybETH": "******************************************", "wUSDB": "******************************************", "ybUSDB": "******************************************", "ybETH-2": "******************************************", "ybUSDB-2": "******************************************"}, "celo": {"stEUR": "******************************************"}, "xdai": {"sDAI": "******************************************"}, "base": {"stUSD": "******************************************", "stEUR": "******************************************", "mwETH": "******************************************", "gtWETHc": "******************************************", "sparkUSDC": "******************************************", "fsUSDS": "******************************************", "fUSDC": "******************************************", "gtmsETHc": "******************************************", "sKAITO": "******************************************", "yvBal-GHO-USR": "******************************************", "auraAave USDC-Aave GHO-vault": "******************************************", "sUSDC": "******************************************", "FUSDC": "******************************************", "sUSDp": "******************************************", "CSUSDC": "******************************************", "COUSDC": "******************************************", "SUSDC": "******************************************", "gtUSDCp": "******************************************", "gtUSDCc": "******************************************", "gtUSDCf": "******************************************", "exmUSDC": "******************************************", "steakUSDC": "******************************************"}, "sapphire": {"wstRose": "******************************************"}, "berachain": {"dberaETH": "******************************************", "dHONEY": "******************************************", "dLBTC": "******************************************", "dNECT": "******************************************", "dpumpBTC_bera": "******************************************", "drsETH": "******************************************", "drswETH": "******************************************", "drUSD": "******************************************", "dsUSDa": "******************************************", "dsUSDe": "******************************************", "dstBTC": "******************************************", "dSBTC": "******************************************", "dSolvBTC": "******************************************", "dSolvBTC_BBN": "******************************************", "dSTONE": "******************************************", "duniBTC": "******************************************", "dUSD0": "******************************************", "dUSD0++": "******************************************", "dUSDa": "******************************************", "dUSDe": "******************************************", "dUSDC_e": "******************************************", "dUSD₮0": "******************************************", "dWBERA": "******************************************", "dWBTC": "******************************************", "dWETH": "******************************************", "dylBTCLST": "******************************************", "dylpumpBTC": "******************************************", "dylstETH": "******************************************", "BB.B.UNIBTC": "******************************************", "BB.B.PUMPBTC": "******************************************", "BB.B.SOLVBTC": "******************************************", "BB.B.YLBTCLST": "******************************************", "BB.B.YLSTETH": "******************************************", "BB.B.YLPUMPBTC": "******************************************", "BB.B.BERAETH": "******************************************", "BB.B.kWBTC-HONEY": "******************************************", "BB.B.kWETH-HONEY": "******************************************", "BB.B.kWBTC-WETH": "******************************************", "BB.B.SOLVBTC.BBN": "******************************************", "BB.B.STONEBTC": "******************************************", "BB.B.STONEETH": "******************************************", "BB.B.RSETH": "******************************************", "BB.B.YLRSETH": "******************************************", "BB.B.STBTC": "******************************************", "BB.B.WETH": "******************************************", "oriBGT": "******************************************", "sWBERA": "******************************************", "stLBGT": "******************************************"}, "sonic": {"wanS": "0xfa85fe5a8f5560e9039c04f2b0a90de1415abd70", "wstkscUSD": "0x9fb76f7ce5fceaa2c42887ff441d46095e494206", "wstkscETH": "0xe8a41c62bb4d5863c6eadc96792cfe90a1f37c47", "ysPENDLE-LPT": "0xa12265b63aED57D77B55b6792BE17f7d7d49222d", "ysbpt-anS-SiloWS": "0x00DD13e8940De56DA13Ac5d86F7f58Fa8f0Ee53e", "eUSDC.e-3": "0x196F3C7443E940911EE2Bb88e019Fd71400349D9", "yUSD": "******************************************", "vgUSDC": "0xF6F87073cF8929C206A77b0694619DC776F89885", "Re7scUSD": "0x592D1e187729C76EfacC6dfFB9355bd7BF47B2a7", "sUSDp": "0xe8a3DA6f5ed1cf04c58ac7f6A7383641e877517b"}, "unichain": {"sUSDC": "0x14d9143becc348920b68d123687045db49a016c6"}, "plume_mainnet": {"plrBUCK": "0x40320c97ea304E4E85f666A61f2Ffa469343dA25", "plrWHIN": "0xAe61133ED6cb6820C18308B7B128a8da8E7825af", "XMMF": "0x6688aA2eB549e325C21a16c942827C9c99F40dd9"}, "story": {"AIDaUSDC": "******************************************"}, "hyperliquid": {"sUSDp": "0x9B3a8f7CEC208e247d97dEE13313690977e24459"}, "katana": {"yUSDC": "******************************************"}, "sei": {"sfastUSD": "0xdf77686D99667Ae56BC18f539B777DBc2BBE3E9F"}}