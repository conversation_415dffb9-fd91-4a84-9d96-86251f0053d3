import { getCurrentUnixTimestamp } from "../../utils/date";
import { nullAddress } from "../../utils/shared/constants";
import { Write } from "../utils/dbInterfaces";
import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";

type Config = {
  chain: string;
  rate: (params: any) => Promise<number>;
  address: string;
  underlying?: string;
  underlyingChain?: string;
  symbol?: string;
  decimals?: number;
  confidence?: number;
};

const configs: { [adapter: string]: Config } = {
  osETH: {
    rate: async ({ api }) => {
      const raw = await api.call({
        abi: "uint256:getRate",
        target: "******************************************",
      });
      return raw / 10 ** 18;
    },
    chain: "ethereum",
    address: "******************************************",
    underlying: "******************************************",
  },
  weETH: {
    rate: async ({ api }) => {
      const raw = await api.call({
        abi: "function getEETHByWeETH(uint256) view returns (uint256)",
        target: "******************************************",
        params: [1e10],
      });
      return raw / 10 ** 10;
    },
    chain: "ethereum",
    address: "******************************************",
    underlying: "******************************************",
  },
  weETHarb: {
    rate: async ({ timestamp }) => {
      const api = await getApi("ethereum", timestamp, true);
      const raw = await api.call({
        abi: "function getEETHByWeETH(uint256) view returns (uint256)",
        target: "******************************************",
        params: [1e10],
        chain: "ethereum",
      });
      return raw / 10 ** 10;
    },
    chain: "arbitrum",
    address: "******************************************",
    underlying: "******************************************",
    underlyingChain: "ethereum",
    symbol: "weETH",
    decimals: 18,
  },
  wstmtrg: {
    rate: async ({ api }) => {
      const raw = await api.call({
        target: "******************************************",
        abi: {
          inputs: [],
          name: "stMTRGPerToken",
          outputs: [{ internalType: "uint256", name: "", type: "uint256" }],
          stateMutability: "view",
          type: "function",
        },
      });
      return raw / 10 ** 18;
    },
    chain: "meter",
    address: "******************************************",
    underlying: "******************************************",
    underlyingChain: "bsc",
    symbol: "wstMTRG",
    decimals: 18,
  },
  neth: {
    rate: async ({ api }) => {
      const raw = await api.call({
        abi: "function convertToAssets(uint256 _stakeAmount) external view returns (uint256)",
        target: "******************************************",
        params: [1e10],
      });
      return raw / 1e10;
    },
    chain: "ethereum",
    address: "******************************************",
    underlying: "******************************************",
  },
  rneth: {
    rate: async ({ api }) => {
      const raw = await api.call({
        abi: "function convertToAssets(uint256 _stakeAmount) external view returns (uint256)",
        target: "******************************************",
        params: [1e10],
      });
      return raw / 1e10;
    },
    chain: "ethereum",
    address: "******************************************",
    underlying: "******************************************",
  },
  mmeth: {
    rate: async ({ api }) => {
      const raw = await api.call({
        abi: "uint256:exchangeRateToNative",
        target: "******************************************",
      });
      return raw / 1e18;
    },
    chain: "ethereum",
    address: "******************************************",
    underlying: "******************************************",
  },
  // Re7BTC: {
  //   rate: lrts("******************************************"),
  //   chain: "ethereum",
  //   address: "******************************************",
  // },
  weETHk: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getRate() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  USTB: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function latestAnswer() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e6;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  ETHRDNTUNIV3: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getLpTokenPrice() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e8;
    },
    chain: "base",
    underlying: "******************************************",
    address: "******************************************",
  },
  USTBL: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getLatestPrice() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e6;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  EUTBL: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getLatestPrice() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e6;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  aETH: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function convertToAETH(uint256) external view returns (uint256)",
        target: "******************************************",
        params: "1000000",
      });
      return rate / 1e6;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  iwstETH: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function exchangeRateStored() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "optimism",
    underlying: "******************************************",
    address: "******************************************",
  },
  FIUSD: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function latestAnswer() external view returns (uint256)",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "era",
    underlying: "******************************************",
    address: "******************************************",
  },
  stALT: {
    rate: async ({ api }) => {
      const [supply, balance] = await Promise.all([
        api.call({
          abi: "erc20:totalSupply",
          target: "******************************************",
        }),
        api.call({
          abi: "erc20:balanceOf",
          target: "******************************************",
          params: "******************************************",
        }),
      ]);
      return balance / supply;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  LFT: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function convertToAssets(uint256) external view returns (uint256)",
        target: "******************************************",
        params: 1e12,
      });
      return rate / 1e12;
    },
    chain: "base",
    underlying: "******************************************",
    address: "******************************************",
  },
  USDO: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function convertToAssets(uint256) external view returns (uint256)",
        target: "******************************************",
        params: 1e12,
      });
      return 1e12 / rate;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  asBNB: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function convertToTokens(uint256) external view returns (uint256)",
        target: "******************************************",
        params: 1e12,
      });
      return 1e12 / rate;
    },
    chain: "bsc",
    underlying: "******************************************", // slisBNB
    address: "******************************************", // asBNB
  },
  vIP: {
    rate: async ({ api }) => {
      const target = await api.call({
        abi: "address:stakePool",
        target: "******************************************",
      });
      const rate = await api.call({
        abi: "function calculateIPWithdrawal(uint256) view returns (uint256)",
        target,
        params: 1e12,
      });
      return rate / 1e12;
    },
    chain: "sty",
    underlying: nullAddress, // IP
    address: "0x5267F7eE069CEB3D8F1c760c215569b79d0685aD",
  },
  hywstHYPE: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:balancePerShare",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "hyperliquid",
    underlying: "******************************************",
    address: "******************************************",
    confidence: 1,
  },
  sUSDa: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getAmountByShares(uint256) view returns (uint256)",
        target: "******************************************",
        params: 1e12,
      });
      return rate / 1e12;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
    confidence: 1,
  },
  JSTRY: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function convertToAssets(uint256 shares) external view returns (uint256)",
        target: "******************************************",
        params: 1e12,
      });
      return rate / 1e12;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
    confidence: 1,
  },
  M: {
    rate: async ({ api }) => {
      const [assets, supply] = await Promise.all([
        api.call({
          abi: "erc20:balanceOf",
          target: "******************************************",
          params: "******************************************",
        }),
        api.call({
          abi: "erc20:totalSupply",
          target: "******************************************",
        }),
      ]);
      return supply / assets;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
  mHYPE: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:exchangeRateToUnderlying",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "hyperliquid",
    underlying: "0x0d01dc56dcaaca66ad901c959b4011ec",
    address: "******************************************",
  },
  USH: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:fetchPrice",
        target: "******************************************",
      });
      return rate / 1e18;
    },
    chain: "hyperliquid",
    underlying: "******************************************",
    address: "******************************************",
  },
  eliteRingsScUSD: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:getRate",
        target: "0x13cCc810DfaA6B71957F2b87060aFE17e6EB8034",
      });
      return rate / 1e6;
    },
    chain: "sonic",
    underlying: "0xd3DCe716f3eF535C5Ff8d041c1A41C3bd89b97aE",
    address: "0xd4aA386bfCEEeDd9De0875B3BA07f51808592e22",
  },
  BPRO: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function getUnderlyingPrice(address cToken) external view returns (uint256)",
        target: "0x7fa5500c978e89660bf3bd0526f8f7164de0b38f",
        params: "0x440cd83c160de5c96ddb20246815ea44c7abbca8",
      });
      return rate / 1e18;
    },
    chain: "rsk",
    underlying: "0xEf213441a85DF4d7acBdAe0Cf78004E1e486BB96",
    address: "0x440cd83c160de5c96ddb20246815ea44c7abbca8",
  },
  GPRIME: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:sharePrice",
        target: "0x508c74Ba01cDa8Bf088969398d18e7Ab1ec3B6AA",
      });
      return rate / 1e18;
    },
    chain: "plume_mainnet",
    underlying: "0x78adD880A697070c1e765Ac44D65323a0DcCE913",
    address: "0x508c74Ba01cDa8Bf088969398d18e7Ab1ec3B6AA",
  },
  "stk-ePendle": {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:exchangeRate",
        target: "0xd302d7fd2c9375a433018fdfa5613be6ad3f18e3",
      });
      return rate / 1e18;
    },
    chain: "arbitrum",
    underlying: "0x3EaBE18eAE267D1B57f917aBa085bb5906114600",
    address: "0x37227785a1f4545ed914690e395e4CFE96B8319f",
  },
  vkHYPE: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "uint256:getRate",
        target: "0x74392Fa56405081d5C7D93882856c245387Cece2",
      });
      return rate / 1e18;
    },
    chain: "hyperliquid",
    underlying: "******************************************",
    address: "0x9BA2EDc44E0A4632EB4723E81d4142353e1bB160",
  },
  HiHYPE: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function kHYPEToHYPE(uint256) external view returns (uint256)",
        target: "******************************************",
        params: "1000000000000000000",
      });
      return rate / 1e18;
    },
    chain: "hyperliquid",
    underlying: "******************************************",
    address: "******************************************",
  },
  "SJ-wartBTC": {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function underlyingToWrapper(uint256) external view returns (uint256)",
        target: "******************************************",
        params: 1e6,
      });
      return rate / 1e6;
    },
    chain: "goat",
    underlying: "******************************************",
    address: "******************************************",
  },
  AA_FalconXUSDC: {
    rate: async ({ api }) => {
      const rate = await api.call({
        abi: "function virtualPrice(address) external view returns (uint256)",
        target: "******************************************",
        params: "******************************************",
      });
      return rate / 1e6;
    },
    chain: "ethereum",
    underlying: "******************************************",
    address: "******************************************",
  },
};

export async function derivs(timestamp: number) {
  return Promise.all(
    Object.keys(configs).map((k: string) =>
      deriv(timestamp, k, configs[k]).catch((e) => {
        k;
        e;
      }),
    ),
  );
}

async function deriv(timestamp: number, projectName: string, config: Config) {
  const {
    chain,
    underlying,
    address,
    underlyingChain,
    symbol,
    decimals,
    confidence,
  } = config;
  let t = timestamp == 0 ? getCurrentUnixTimestamp() : timestamp;
  const api = await getApi(chain, t, true);
  const pricesObject: any = {
    [address]: {
      underlying,
      symbol,
      decimals,
      price: await config.rate({ api, timestamp }),
    },
  };

  const writes: Write[] = [];
  return await getWrites({
    underlyingChain,
    chain,
    timestamp,
    pricesObject,
    projectName,
    writes,
    confidence,
  });
}
