import { addToDBWritesList, getTokenAndRedirectData } from "../utils/database";
import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";

const dai = "******************************************";
const pot = "******************************************";

const chain = "ethereum";
const token = "******************************************";
const decimals = 18;
const symbol = "CHAI";

export async function chai(timestamp = 0) {
  const [{ price: daiPrice }] = await getTokenAndRedirectData(
    [dai],
    chain,
    timestamp,
  );

  const api = await getApi(chain, timestamp);
  const chi = await api.call({
    target: pot,
    abi: "function chi() view returns (uint256)",
  });

  const price = daiPrice * (chi / 1e27);
  const confidence = 0.98;

  const writes: Write[] = [];
  addToDBWritesList(
    writes,
    chain,
    token,
    price,
    decimals,
    symbol,
    timestamp,
    "chai",
    confidence,
  );

  return writes;
}
