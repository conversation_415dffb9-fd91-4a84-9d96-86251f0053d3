import { Write, } from "../../utils/dbInterfaces";
import { getApi } from "../../utils/sdk";
import getWrites from "../../utils/getWrites";
import { getConfig } from "../../../utils/cache";

export const config: any = {
  avax: {
    tokens: {
      "YY_AAVE_AVAX": "******************************************",
      "YY_PTP_sAVAX": "******************************************",
      "YY_PNG_AVAX_USDC_LP": "******************************************",
      "YY_PNG_AVAX_ETH_LP": "******************************************",
      "YY_TJ_AVAX_USDC_LP": "******************************************",
      "YY_TJ_AVAX_ETH_LP": "******************************************",
      "YY_TJ_AVAX_sAVAX_LP": "******************************************",
      "YY_fsGLP": "******************************************",
    },
    endpoint: 'https://staging-api.yieldyak.com/43114/farms'
  },
  arbitrum: {
    tokens: {
      "WETH": "******************************************",
      "WBTC": "******************************************",
      "USDC": "******************************************",
      "USDC_WBTC": "******************************************",
      "YY_WOMAT-LP-DAI": "******************************************",
      "YY_WOMAT-LP-USDT": "******************************************",
      "YY GMX sGLP-": "******************************************",
      "YY_WOMAT-LP-USDC.e": "******************************************",
    },
    endpoint: 'https://staging-api.yieldyak.com/42161/farms'
  },
}

export default async function getTokenPrices(chain: string, timestamp: number) {
  const writes: Write[] = [];
  const api = await getApi(chain, timestamp)
  const { tokens, endpoint } = config[chain]
  let calls = Object.values(tokens) as string[]
  const farms = await getConfig('yield-yak/' + chain, endpoint)
  farms.forEach((farm: any) => calls.push(farm.address))
  calls = calls.map((address: string) => address.toLowerCase())
  calls = [...new Set(calls)]

  const totalDeposits = await api.multiCall({ abi: 'uint256:totalDeposits', calls, permitFailure: true })
  const totalSupply = await api.multiCall({ abi: 'uint256:totalSupply', calls, permitFailure: true })
  const depositToken = await api.multiCall({ abi: 'address:depositToken', calls, permitFailure: true })

  const pricesObject: any = {}
  calls.forEach((vault: any, i: any) => {
    const underlying = depositToken[i] 
    const price = totalDeposits[i] / totalSupply[i]
    if (!underlying || isNaN(price) || price === Infinity) return;
    pricesObject[vault] = { underlying: depositToken[i], price: totalDeposits[i] / totalSupply[i] }
  })
  return getWrites({ chain, timestamp, writes, pricesObject, projectName: 'yield-yak' })
}
