import { Write } from "../utils/dbInterfaces";
import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";

const tokens = [
  "******************************************",
  "******************************************",
  "******************************************",
];

export async function eigenpie(timestamp: number) {
  const chain = "ethereum";
  const writes: Write[] = [];

  const api = await getApi(chain, timestamp, true);
  const [rates, underlyings] = await Promise.all([
    api.multiCall({
      abi: "uint256:exchangeRateToLST",
      calls: tokens.map((target: string) => ({ target })),
    }),
    api.multiCall({
      abi: "address:underlyingAsset",
      calls: tokens.map((target: string) => ({ target })),
    }),
  ]);

  const pricesObject: any = {};
  tokens.map((t: string, i: number) => {
    pricesObject[t] = { underlying: underlyings[i], price: rates[i] / 1e18 };
  });

  return getWrites({
    chain,
    timestamp,
    writes,
    pricesObject,
    projectName: "eigenpie",
  });
}
