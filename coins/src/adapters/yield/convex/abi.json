{"operator": {"inputs": [], "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, "poolInfo": {"ethereum": {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolInfo", "outputs": [{"internalType": "address", "name": "lptoken", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "gauge", "type": "address"}, {"internalType": "address", "name": "crvRewards", "type": "address"}, {"internalType": "address", "name": "stash", "type": "address"}, {"internalType": "bool", "name": "shutdown", "type": "bool"}], "stateMutability": "view", "type": "function"}, "arbitrum": {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "poolInfo", "outputs": [{"internalType": "address", "name": "lptoken", "type": "address"}, {"internalType": "address", "name": "gauge", "type": "address"}, {"internalType": "address", "name": "rewards", "type": "address"}, {"internalType": "bool", "name": "shutdown", "type": "bool"}, {"internalType": "address", "name": "factory", "type": "address"}], "stateMutability": "view", "type": "function"}}, "poolLength": {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}}