{"getPricePerFullShare": {"constant": true, "inputs": [], "name": "getPricePerFullShare", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function", "signature": "0x77c7b8fc"}, "pricePerShare": {"name": "pricePerShare", "outputs": [{"type": "uint256", "name": ""}], "inputs": [], "stateMutability": "view", "type": "function"}, "constantPricePerShare": {"constant": true, "inputs": [], "name": "getPricePerFullShare", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, "token": {"constant": true, "inputs": [], "name": "token", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, "asset": {"inputs": [], "name": "ASSET", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}}