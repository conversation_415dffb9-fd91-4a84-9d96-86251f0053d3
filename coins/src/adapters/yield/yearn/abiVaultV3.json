{"asset": {"stateMutability": "view", "type": "function", "name": "asset", "inputs": [], "outputs": [{"name": "", "type": "address"}]}, "symbol": {"stateMutability": "view", "type": "function", "name": "symbol", "inputs": [], "outputs": [{"name": "", "type": "string"}]}, "decimals": {"stateMutability": "view", "type": "function", "name": "decimals", "inputs": [], "outputs": [{"name": "", "type": "uint8"}]}, "pricePerShare": {"stateMutability": "view", "type": "function", "name": "pricePerShare", "inputs": [], "outputs": [{"name": "", "type": "uint256"}]}}