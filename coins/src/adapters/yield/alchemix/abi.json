{"staticToDynamicAmount": {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "staticToDynamicAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "aToken": {"inputs": [], "name": "ATOKEN", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}}