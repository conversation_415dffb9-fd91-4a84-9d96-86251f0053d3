
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";
import { Write } from "../utils/dbInterfaces";
import { getUniqueAddresses } from "@defillama/sdk/build/generalUtil";

const config = {
  ethereum: [
    "******************************************",// wstETH
    "******************************************",// rETH
    "******************************************",// wETH
    "******************************************",// weETH
    "******************************************", // USDC
  ]
}

export default async function getPrices(timestamp: number = 0) {
  const writes: Write[] = []
  for (const [chain, pools] of Object.entries(config)) {
    const api = await getApi(chain, timestamp)
    let balVaults = await api.fetchList({ lengthAbi: 'getUnderlyingPoolsLength', itemAbi: 'function getUnderlyingPool(uint8) view returns (address)', targets: pools, })
    balVaults = getUniqueAddresses(balVaults, chain)

    const supplies = await api.multiCall({ abi: 'erc20:totalSupply', calls: balVaults })
    const bals = await api.multiCall({ abi: 'uint256:totalAssets', calls: balVaults })
    const assets = await api.multiCall({ abi: 'address:asset', calls: balVaults })
    const pricesObject: any = {}
    for (let i = 0; i < balVaults.length; i++) {
      pricesObject[balVaults[i]] = { price: bals[i] / supplies[i], underlying: assets[i] }
    }
    await getWrites({ chain, timestamp, writes, pricesObject, projectName: "opal", })
  }
  return writes
}