import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";

const swETH: string = "******************************************";
const underlying = '******************************************'
const chain: any = "ethereum";

export default async function getTokenPrice(timestamp: number) {
  const api = await getApi('ethereum', timestamp);
  const pricesObject: any = {};
  const writes: Write[] = [];
  const [
    swETHToETHRate,
    eETHPrice,
    svETHPrice,
    mswETHPrice,
  ] = await Promise.all([
    api.call({ target: swETH, abi: 'uint256:swETHToETHRate' }),
    api.call({ target: '******************************************', abi: 'uint256:answer' }),
    api.call({ target: '******************************************', abi: 'uint256:getVectorSharePrice' }),
    api.call({ target: '******************************************', abi: 'uint256:exchangeRateToNative' }),
  ])
  pricesObject[swETH] = { price: swETHToETHRate / 1e18, underlying }
  pricesObject['******************************************'] = { price: eETHPrice / 1e18, underlying }
  pricesObject['******************************************'] = { price: svETHPrice / 1e18, underlying }
  pricesObject['******************************************'] = { price: mswETHPrice / 1e18, underlying }

  await getWrites({ chain, timestamp, writes, pricesObject, projectName: "swETH", })
  return writes;
}
