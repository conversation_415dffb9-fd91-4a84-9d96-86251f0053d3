import { lowercase } from "../../utils/coingeckoPlatforms";

export const contracts: { [chain: string]: { [token: string]: string } } = {
  ethereum: {
    pETH: "******************************************",
    hBTC: "******************************************",
    GVR: "******************************************",
    PSY_SCAM: "******************************************",
    GVR_OLD: "******************************************",
    XRPC: "******************************************",
    LUFFY: "******************************************",
    LUFFY_NEW: "******************************************",
    FEG: "******************************************",
    yUSDT: "******************************************",
    yUSDT_v1: "******************************************",
    ycUSDT_v1: "******************************************",
    yUSDT_yDAI_yUSDT_BUSD: "******************************************",
    crv_yUSDT_yDAI_yUSDT_BUSD: "******************************************",
    ypaxCrv: "******************************************",
    "yDAI+yUSDC+yUSDT+yTUSD": "******************************************",
    "yyDAI+yUSDC+yUSDT+yTUSD": "******************************************",
    yUSD: "******************************************",
    YAM: "******************************************",
    PAW: "******************************************",
    multiBTC: "******************************************",
    FEVR: "******************************************",
    TENET: "******************************************",
    PLQ: "******************************************",
    MINTME: "******************************************",
    GRAIN: "******************************************",
    "pETH-ETH-f": "******************************************",
    "msETH-ETH-f": "******************************************",
    FUNB: "******************************************",
    FLEET: "******************************************",
    SHIB2_0: "******************************************",
    // Euler tokens
    EULER_LP1: "******************************************",
    EULER_LP2: "******************************************",
    EULER_LP3: "******************************************",
    EULER_LP4: "******************************************",
    EULER_LP5: "******************************************",
    // 4626 wrapped eTokens
    EULER_LP6: "******************************************",
    EULER_LP7: "******************************************",
    EULER_LP8: "******************************************",
    EULER_LP9: "******************************************",
    // AIUS: "******************************************",
    PEAK: "******************************************",
    XYZ: "******************************************",
    clBTC: "******************************************", // IS NOT BACKED
    NBTC: "******************************************", // RESERVES NEVER MATCH THE SUPPLY, DELISTED 
    MBTC: "******************************************",  // BACKED BY THE ABOVE, SO SAME 
    crvPlain3andSUSD: '******************************************', // sunset 
    MSTR: '******************************************'
  },
  // beam: {
  //   WMC: '******************************************'
  // },
  harmony: {
    Frax: "******************************************",
    WrappedEther: "******************************************",
    Aave: "******************************************",
    Sushi: "******************************************",
    FXS: "******************************************",
    AAG: "******************************************",
    BUSD: "******************************************",
    DAI: "******************************************",
    Tether: "******************************************",
    WBTC: "******************************************",
    USDC: "******************************************",
    ETH: "******************************************",
    bscBNB: "******************************************",
    bscBUSD: "******************************************",
    BIFI: "******************************************",
  },
  klaytn: {
    USDK: "******************************************",
  },
  arbitrum: {
    GOLD: "******************************************",
    BIFI: "******************************************",
    MINTME: "******************************************",
    GRAIN: "******************************************",
    //'SolvBTC.BBN': "******************************************"
    HAMI: "******************************************",
    KNC: "******************************************",
    clBTC: "******************************************", // IS NOT BACKED
    MBTC: "******************************************"
  },
  bsc: {
    BGEO: "******************************************",
    aBNBb: "******************************************",
    aBNBc: "******************************************",
    DOGECOLA: "******************************************",
    GVR: "******************************************",
    GVR2: "******************************************",
    PANCAKE_LP_ABNB_BNB: "******************************************",
    BTCBR: "******************************************",
    RB: "******************************************",
    MOR: "******************************************",
    $CINO: "******************************************",
    VBSWAP: "******************************************",
    ZEDXION: "******************************************",
    FEG: "******************************************",
    MFI: "******************************************",
    BLT: "******************************************",
    multiBTC: "******************************************",
    DERC: "******************************************",
    LMT: "******************************************",
    DHV: "******************************************",
    MIMIR: "******************************************",
    BCDT: "******************************************",
    DEP: "******************************************",
    DOSE: "******************************************",
    ORBS: "******************************************",
    TCS: "******************************************",
    MIM: "0xfe19f0b51438fd612f6fd59c1dbb3ea319f433ba",
    TOMOE: "0x9a6d5c2e0376572f214a35f832740e412932d277",
    bCOLX: "0xf8acf86194443dcde55fc5b9e448e183c290d8cb",
    FODL: "0x43f5b29d63cedc5a7c1724dbb1d698fde05ada21",
    LIQR: "******************************************",
    PLQ: "0x9c08951397bc02cd66f69eadbb8d491f8bb08c5e",
    MINTME: "0x138218c8e064ed2a011c9ff203759a8a1e23e6c8",
    GRAIN: "0x8f87a7d376821c7b2658a005aaf190ec778bf37a",
    FITFI: "0x7588df009c3d82378be6ab81f2108fa963c10fc8",
    JEFE: "0x80fa6d5384bdde296a28a321f73ab70977575129",
    LORT: "0xd24616870ca41bc01074446988faeb0085a71190",
    BCCOIN: "0x2940566Eb50F15129238f4Dc599ADC4F742D7d8E",
    PNIC: "0x76d36d44dc4595e8d2eb3ad745f175eda134284f",
    "PENDLE-LPT": "0x0921ccc98956b1599003fd9739d5e66bf319a161",
    coBTC: "0x918b3aa73e2D42D96CF64CBdB16838985992dAbc", // TO SUS , BACKING 2 TOKENS, BEING USED ON YIELDNEST + KERNEL ITS PUMPINP METRICS
    ynCoBTCk: "0x132376b153d3cFf94615fe25712DB12CaAADf547", // SAME , backing 2 tokens and aparently also loop
    MBTC: "0x7c1cca5b25fa0bc9af9275fb53cba89dc172b878"
  },
  cronos: {
    CRK: "0x065de42e28e42d90c2052a1b49e7f83806af0e1f",
    MINTME: "0xd652776de7ad802be5ec7bebfafda37600222b48",
  },
  solana: {
    YAKU: "NGK3iHqqQkyRZUj4uhJDQqEyKKcZ7mdawWpqwMffM3s",
    SNS: "SNSNkV9zfG5ZKWQs6x4hxvBRV6s8SqMfSGCtECDvdMd",
    PEEP: "n54ZwXEcLnc3o7zK48nhrLV4KTU5wWD4iq7Gvdt5tik",
    //WIF: "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm",
    OVOL: "4v3UTV9jibkhPfHi5amevropw6vFKVWo7BmxwQzwEwq6",
    TRUMP: "AwRErBEFGTnohzfLeRSBH9HddQEy2oeRxnWLrbvFFh95",
  },
  pulse: {
    GLOBO: "0x13568d812d30359756db5e617a7a8eb7eb3917f5",
    MMC: "0x387776e41b4688d79e1770dceba1f6d854911eb1",
    HUNT: "0xd854f7814b676f1c19ea22d4137d0176413c85b4",
    Wildfire: "0x4db9112fe1c3670a7adb5e206ef6ce26707a2767",
  },
  avax: {
    BPAY: "0x8ccee641c3d937dc89d1d94fe4d8051624042fcf",
    DUEL: "0xc1a49c0b9c10f35850bd8e15eaef0346be63e002",
    ApeU: "0x6b0d2a3c37d551963275bB104F045F6a68AB6374",
    iBFR: "0xcaf870dad882b00f4b20d714bbf7fceada5e4195",
    BIFI: "******************************************",
    ORBS: "0x340fe1d898eccaad394e2ba0fc1f93d27c7b717a",
    BCDT: "0xafb2780cbb58b2af27023eb2a0e60c8ca0eee9bb",
    DEP: "0xd4d026322c88c2d49942a75dff920fcfbc5614c1",
    LIQR: "******************************************",
    GRAIN: "0x9df4ac62f9e435dbcd85e06c990a7f0ea32739a9",
    DDC: "0x38e853f7519d3db2d245ce0d21960f68a6448051",
    MYD: "0xf3797a24dd0ced905e2d08b03cf364aac49caa6d",
    JLP: "0xa56d382c5aa9774d4eb98f1500670d3049fede9b",
    PARR: "******************************************",
  },
  oasis: {
    USDT: "******************************************",
    USDC: "******************************************",
  },
  fantom: {
    CoUSD: "******************************************",
    DEI: "******************************************",
    multiUSDC: "******************************************",
    miMATIC: "******************************************",
    fUSDT: "******************************************",
    WETH: "******************************************",
    BTC: "******************************************",
    ETH: "******************************************",
    USDC: "******************************************",
    "0xMR": "******************************************",
    MIMO: "******************************************",
    DEP: "******************************************",
    sSPELL: "******************************************",
    LIQR: "******************************************",
    BELUGA: "******************************************",
    UST: "******************************************",
    BIFI: "******************************************",
    LUNA: "******************************************",
    FEED: "******************************************",
    GRAIN: "******************************************",
    JEFE: "******************************************",
    DAI: "******************************************",
    BASED: "******************************************",
    // BSHARE: '******************************************'
    plainu: "******************************************",
  },
  heco: {
    BIFI: "******************************************",
  },
  polygon: {
    FEVR: "******************************************",
    BELUGA: "******************************************",
    BIFI: "******************************************",
    MIMIR: "******************************************",
    FEED: "******************************************",
    BANANA: "******************************************",
    DHV: "******************************************",
    multiBTC: "******************************************",
    MIM: "******************************************",
    JUSD: "******************************************",
    SX: "******************************************",
  },
  moonriver: {
    MIM: "******************************************",
    BIFI: "******************************************",
  },
  celo: {
    BIFI: "******************************************",
  },
  fuse: {
    agEUR: "******************************************",
  },
  boba: {
    MIM: "******************************************",
  },
  optimism: {
    MIM: "******************************************",
    GRAIN: "******************************************",
    clBTC: "******************************************" // IS NOT BACKED
  },
  era: {
    MVX: "******************************************",
  },
  metis: {
    BIOS: "******************************************",
    BIFI: "******************************************",
  },
  kava: {
    BUSD: "******************************************",
    SUSHI: "******************************************",
    DAI: "******************************************",
    ETH: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
    WBTC: "******************************************",
    null: "******************************************",
  },
  kardia: {
    USDC: "******************************************",
  },
  step: {
    BNB: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
    ETH: "******************************************",
  },
  godwoken_v1: {
    DAI: "******************************************",
    WBTC: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
  },
  ethpow: {
    DAI: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
    WBTC: "******************************************",
    ETH: "******************************************",
  },
  milkomeda_a1: {
    BUSD: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
  },
  wemix: {
    BNB: "******************************************",
    KLAY: "******************************************",
    USDC: "******************************************",
    USDT: "******************************************",
    WBTC: "******************************************",
    ETH: "******************************************",
  },
  eos_evm: {
    USDC: "******************************************",
    USDT: "******************************************",
    WBTC: "******************************************",
    ETH: "******************************************",
  },
  coingecko: {
    PREMIO: "premio",
    TFBX: "truefeedbackchain",
    //solvBTCBBN: "solv-protocol-solvbtc-bbn",
  },
  bittorrent: {
    WBTT: "******************************************",
  },
  callisto: {
    SOY: "******************************************",
  },
  blast: {
    SSS: "******************************************",
  },
  base: {
    WILDx: "******************************************",
    WANDER: "******************************************",
    AETX: "******************************************",
    clBTC: "******************************************" // IS NOT BACKED
  },
  sonic: {
    wstkscUSD: "******************************************"
  },
  bera: {
    "pumpBTC.bera": "******************************************"
  },
  hyperliquid: {
    LABUBU: "******************************************",
    CUTEPURR: "******************************************"
  },
  goat: {
    UBTC: "******************************************",
    ESBTC: "******************************************"
  },
  hemi: {
    ESBTC: "******************************************",
    UBTC: "******************************************"
  },
 plume: {
    ESBTC: "******************************************",
  },
  xsat: {
    ESBTC: "******************************************",
  }
  // merlin: {
  //   'SolvBTC.BBN': "******************************************"
  // }
};

export const distressedAssets = Object.fromEntries(
  Object.entries(contracts)
    .map(([chain, tokens]) => {
      return Object.entries(tokens).map(([_symbol, address]) => {
        return [`${chain}:${lowercase(address, chain)}`, true];
      });
    })
    .flat(),
);
