import { Write } from "../utils/dbInterfaces";
import { addToDBWritesList } from "../utils/database";

interface TokenInfo {
  symbol: string;
  address: string;
  decimals: number;
  redirect: string;
}
const contracts: { [chain: string]: TokenInfo[] } = {
  evmos: [
    {
      symbol: "axlDAI",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#dai",
    },
    {
      symbol: "axlUSDC",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#usd-coin",
    },
    {
      symbol: "axlUSDT",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#tether",
    },
    {
      symbol: "ceDAI",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#dai",
    },
    {
      symbol: "ibc G-DAI",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#dai",
    },
    {
      symbol: "ibc G-USDC",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#usd-coin",
    },
    {
      symbol: "ibc G-USDT",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#tether",
    },
  ],
  arbitrum: [
    {
      symbol: "mUMAMI",
      address: "******************************************",
      decimals: 9,
      redirect: "coingecko#umami-finance",
    },
    {
      symbol: "fsGLP",
      address: "******************************************",
      decimals: 18,
      redirect: "asset#arbitrum:******************************************",
    },
    {
      symbol: "fGLP",
      address: "******************************************",
      decimals: 18,
      redirect: "asset#arbitrum:******************************************",
    },
    {
      symbol: "OLE",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#openleverage",
    },
  ],
  polygon_zkevm: [
    {
      symbol: "wstETH",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#wrapped-steth",
    },
    {
      symbol: "USDC",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#usd-coin",
    },
    {
      symbol: "USDT",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#tether",
    },
    {
      symbol: "MATIC",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#matic-network",
    },
  ],
  polygon: [
    {
      symbol: "wstETH",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#wrapped-steth",
    },
  ],
  wemix: [
    {
      symbol: "WEMIX$",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#usd-coin",
    },
  ],
  kava: [
    {
      symbol: "DEXI",
      address: "******************************************",
      decimals: 9,
      redirect: "coingecko#dexioprotocol-v2",
    },
    {
      symbol: "ATOM",
      address: "******************************************",
      decimals: 6,
      redirect: "coingecko#cosmos",
    },
    {
      symbol: "WKAVA",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#kava",
    },
  ],
  ethereum: [
    {
      symbol: "sdXAIFRAXBP3CRV-f-vault",
      address: "******************************************",
      decimals: 18,
      redirect: "asset#ethereum:******************************************",
    },
    {
      symbol: "sdXAIFRAXBP3CRV-f-gauge",
      address: "******************************************",
      decimals: 18,
      redirect: "asset#ethereum:******************************************",
    },
    {
      symbol: "eETH",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#ethereum",
    },
    {
      symbol: "GHO",
      address: "******************************************",
      decimals: 18,
      redirect: "coingecko#gho",
    },
  ],
};

export default async function getTokenPrices(chain: string, timestamp: number) {
  const writes: Write[] = [];

  Object.values(contracts[chain]).map((a: TokenInfo) => {
    addToDBWritesList(
      writes,
      chain,
      a.address,
      undefined,
      a.decimals,
      a.symbol,
      timestamp,
      "manual input",
      1.01,
      a.redirect,
    );
  });

  return writes;
}
