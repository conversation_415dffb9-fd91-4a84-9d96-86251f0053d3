import { Write } from "../utils/dbInterfaces";
import { addToDBWritesList, getTokenAndRedirectData } from "../utils/database";
import { call } from "@defillama/sdk/build/abi";
import { getTokenInfo } from "../utils/erc20";
import getBlock from "../utils/block";

const SPELLAddress: string = "******************************************";
const sSPELLAddress: string = "******************************************";
const chain: any = "ethereum";

export default async function getTokenPrices(timestamp: number) {
  const writes: Write[] = [];
  let block: number | undefined = await getBlock(chain, timestamp);

  const [[{ price: SPELLPrice }], stakedBalance, sSPELLInfo] = await Promise.all([
    getTokenAndRedirectData([SPELLAddress], chain, timestamp),
    call({
      target: SPELLAddress,
      params: sSPELLAddress,
      chain,
      abi: "erc20:balanceOf",
      block
    }),
    getTokenInfo(chain, [sSPELLAddress], block, { withSupply: true, })
  ]);

  const price: number =
    (SPELLPrice * stakedBalance.output) / sSPELLInfo.supplies[0].output;

  addToDBWritesList(
    writes,
    chain,
    sSPELLAddress,
    price,
    sSPELLInfo.decimals[0].output,
    sSPELLInfo.symbols[0].output,
    timestamp,
    "abracadabra",
    0.9
  );
  addToDBWritesList(
    writes,
    "fantom",
    "******************************************",
    undefined,
    18,
    sSPELLInfo.symbols[0].output,
    timestamp,
    "abracadabra",
    0.9,
    `asset#${chain}:${sSPELLAddress}`
  );

  return writes;
}
