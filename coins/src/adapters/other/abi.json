{"proxy": {"constant": true, "inputs": [], "name": "proxy", "outputs": [{"internalType": "contract Proxy", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, "pricePrecision": {"inputs": [], "name": "PRICE_PRECISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "getAums": {"inputs": [], "name": "getAums", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, "totalAssets": {"inputs": [], "name": "totalAssets", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "GDpriceToStakedToken": {"inputs": [{"internalType": "uint256", "name": "_pid", "type": "uint256"}], "name": "GDpriceToStakedtoken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "underlying": {"inputs": [], "name": "underlying", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}}