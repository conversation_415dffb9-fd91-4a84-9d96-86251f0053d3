import synthetixAdapter from "./synthetix";
import glp from "./glp";
import abraAdapter from "./abracadabra";
import unknownTokenAdapter from "./unknownToken";
import podsAdapter from "./pods";
import distressedAdapter from "./distressedAssets";
import { contracts } from "./distressed";
import manualInputAdapter from "./manualInput";
import realtAdapter from "./realt";
import metronomeAdapter from "./metronome";
import { contracts as metronomeContracts } from "./metronome";
import { wrappedGasTokens } from "../utils/gasTokens";
import collateralizedAdapter from "./collateralizedAssets";
import swethAdapter from "./sweth";
import gmdAdapter from "./gmd";
import stkaurabalAdapter from "./stkaurabal";
import shlb_ from "./shlb";
import axios from "axios";
import { Write } from "../utils/dbInterfaces";
import { addToDBWritesList } from "../utils/database";
import mooBvmAdapter from "./mooBvmEth";
import defiChainAdapter from "./defichain";
import velgAdapter from "./velgd";
import steadefiEth from "./steadefi_eth";
import steadefiWbtc from "./steadefi_wbtc";
import steadefiUsdArb from "./steadefi_usdc_arb";
import steadefiUsdEth from "./steadefi_usdc_eth";
import steadefiUsdLink from "./steadefi_usdc_link";
import steadefiUsdWbtc from "./steadefi_usdc_wbtc";
import warlordAdapter from "./warlord";
import opdxAdapter from "./odpxWethLP";
import teahouseAdapter from "./teahouse";
import opal from "./opal";
import gmdV2 from "./gmdV2";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";

export { glp };

export const shlb = shlb_;

export function steadefi(timestamp: number = 0) {
  return Promise.all([
    steadefiEth(timestamp),
    steadefiWbtc(timestamp),
    steadefiUsdArb(timestamp),
    steadefiUsdEth(timestamp),
    steadefiUsdLink(timestamp),
    steadefiUsdWbtc(timestamp),
  ]);
}
export function teahouse(timestamp: number = 0) {
  return teahouseAdapter(timestamp);
}
export function opdx(timestamp: number = 0) {
  return opdxAdapter(timestamp);
}
export function defiChain(timestamp: number = 0) {
  return defiChainAdapter(timestamp);
}
export function synthetix(timestamp: number = 0) {
  return synthetixAdapter(timestamp);
}

export function metronome(timestamp: number = 0) {
  return Promise.all(
    Object.keys(metronomeContracts).map((chain) =>
      metronomeAdapter(chain, timestamp),
    ),
  );
}

export function abracadabra(timestamp: number = 0) {
  return abraAdapter(timestamp);
}
export function unknownTokens(timestamp: number = 0) {
  return Promise.all([
    unknownTokenAdapter(
      timestamp,
      "******************************************",
      "******************************************",
      wrappedGasTokens["ethereum"],
      true,
      "ethereum",
    ),
    unknownTokenAdapter(
      timestamp,
      "******************************************",
      "******************************************",
      "******************************************",
      true,
      "jbc",
    ),
  ]);
}
export async function unknownTokens2(timestamp: number = 0) {
  const config: any = {
    sonic: [
      { pool: '******************************************', unknown: '******************************************', known: '******************************************'}
    ],
    beam: [
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", confidence: 1.01 },
    ],
    wemix: [
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
    ],
    klaytn: [
      { pool: "******************************************", unknown: "0x27397bfbefd58a437f2636f80a8e70cfc363d4ff", known: "0x19aac5f612f524b754ca7e7c41cbfa2e981a4432", },
      { pool: "0x9f8a222fd0b75239b32aa8a97c30669e5981db05", unknown: "0x999999999939ba65abb254339eec0b2a0dac80e9", known: "0xff3e7cf0c007f919807b32b30a4a9e7bd7bc4121", },
    ],
    merlin: [
      { pool: "0x501ca56E4b6Af84CBAAaaf2731D7C87Bed32ee65", unknown: "0x7b0400231Cddf8a7ACa78D8c0483890cd0c6fFD6", known: "0x5c46bFF4B38dc1EAE09C5BAc65872a1D8bc87378", },
    ],
    q: [
      { pool: "0x2A36b45be4C04900A5946A1B6bf991aDec93ADdE", unknown: "0xE31DD093A2A0aDc80053bF2b929E56aBFE1B1632", known: "0x79Cb92a2806BF4f82B614A84b6805963b8b1D8BB", },
    ],
    songbird: [
      { pool: "0xcD15C231b8A0Bae40bD7938AE5eA8e43f1e9a15F", unknown: "0x0D94e59332732D18CF3a3D457A8886A2AE29eA1B", known: "0xC348F894d0E939FE72c467156E6d7DcbD6f16e21", },
    ],
    bsc: [
      { pool: "0xa0feB3c81A36E885B6608DF7f0ff69dB97491b58", unknown: "0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56", known: "0x20f663CEa80FaCE82ACDFA3aAE6862d246cE0333", },
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
    ],
    ethereum: [
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
    ],
    arbitrum: [
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
    ],
    avax: [
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
      { pool: "******************************************", unknown: "******************************************", known: "******************************************", },
    ],
    fantom: [
      { pool: "******************************************", unknown: "0x07bb65faac502d4996532f834a1b7ba5dc32ff96", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x44C85D45EB17C8A6b241807BE5c9c48201F91837", unknown: "0x5c725631FD299703D0A74C23F89a55c6B9A0C52F", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x6bB685358BC3991D9279562710F3a44B7e5F2D9b", unknown: "0x3dc57B391262e3aAe37a08D91241f9bA9d58b570", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0xaCf56C6aadDc1408A11AbAb3140b90b57Fc6Aaf7", unknown: "0x248CB87DDA803028dfeaD98101C9465A2fbdA0d4", known: "0x04068DA6C83AFCFA0e13ba15A6696662335D5B75", },
      { pool: "0xaCf56C6aadDc1408A11AbAb3140b90b57Fc6Aaf7", unknown: "0x248CB87DDA803028dfeaD98101C9465A2fbdA0d4", known: "0x04068DA6C83AFCFA0e13ba15A6696662335D5B75", },
      { pool: "0x4733bc45eF91cF7CcEcaeeDb794727075fB209F2", unknown: "0x4cdF39285D7Ca8eB3f090fDA0C069ba5F4145B37", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x89d9bC2F2d091CfBFc31e333D6Dc555dDBc2fd29", unknown: "0xb3654dc3D10Ea7645f8319668E8F54d2574FBdC8", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x89d9bC2F2d091CfBFc31e333D6Dc555dDBc2fd29", unknown: "0xb3654dc3D10Ea7645f8319668E8F54d2574FBdC8", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0xde62A6CdD8d5A3988495317CfFac9F3fED299383", unknown: "0xC17c30e98541188614dF99239cABD40280810cA3", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0xa7010b3ba9efb1AF9Fa8a30efe74C16A93891775", unknown: "0x6626c47c00F1D87902fc13EECfaC3ed06D5E8D8a", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x011732f65e2f28C50F528e32420A2F69937b9e68", unknown: "0x89346B51A54263cF2e92dA79B1863759eFa68692", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x43E1059c05D3153B5D74303DD6474a43BC87E73e", unknown: "0xd7028092c830b5C8FcE061Af2E593413EbbC1fc1", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0xCC656162f9f157860bB7309B72374eCE447e327a", unknown: "0xF24Bcf4d1e507740041C9cFd2DddB29585aDCe1e", known: "0xd7028092c830b5C8FcE061Af2E593413EbbC1fc1", },
      { pool: "0xEc7178F4C41f346b2721907F5cF7628E388A7a58", unknown: "0x841FAD6EAe12c286d1Fd18d1d525DFfA75C7EFFE", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
      { pool: "0x0b9589A2C1379138D4cC5043cE551F466193c8dE", unknown: "0x5c4fdfc5233f935f20d2adba572f770c2e377ab0", known: "0x04068DA6C83AFCFA0e13ba15A6696662335D5B75", },
      { pool: "0x15aFDbDb27767d58A58459ae159814b6bBe6f506", unknown: "0x9fb9a33956351cf4fa040f65a13b835a3c8764e3", known: "0x21be370D5312f44cB42ce377BC9b8a0cEF1A4C83", },
    ],
    base: [
      { pool: "0xd33805985642Fa9338e1Fa150cA811406F96F0dE", unknown: "0x2e1d332893Dc16124194E598a29866D2d5e8786b", known: "0x4200000000000000000000000000000000000006", },
      { pool: "0x53713F956A4DA3F08B55A390B20657eDF9E0897B", unknown: "0xd386a121991E51Eab5e3433Bf5B1cF4C8884b47a", known: "0x4200000000000000000000000000000000000006", },
      { pool: "0xb5E331615FdbA7DF49e05CdEACEb14Acdd5091c3", unknown: "0xCc7FF230365bD730eE4B352cC2492CEdAC49383e", known: "0xCfA3Ef56d303AE4fAabA0592388F19d7C3399FB4", },
    ],
    optimism: [
      { pool: "0xf3C45b45223Df6071a478851B9C17e0630fDf535", unknown: "0x1e925De1c68ef83bD98eE3E130eF14a50309C01B", known: "0x4200000000000000000000000000000000000006", },
      { pool: "0x62191C893DF8d26aC295BA1274a00975dc07190C", unknown: "0x676f784d19c7F1Ac6C6BeaeaaC78B02a73427852", known: "0x4200000000000000000000000000000000000006", },
      { pool: "0xAE6c9B2A2777D0396cbE7E13Fc9ACEAC0D052e00", unknown: "0xc38464250f51123078bbd7ea574e185f6623d037", known: "0x676f784d19c7F1Ac6C6BeaeaaC78B02a73427852", },
    ],
    // eni: [
    //   { pool: "0x70fafce741d7f49f03434690fcb73d1933a90c1d", unknown: "0x6d1e851446f4d004ae2a72f9afed85e8829a205e", known: "0xdc1a8a35b0baa3229b13f348ed708a2fd50b5e3a", },
    // ], 
    pulse: [
      { pool: "0xdca85EFDCe177b24DE8B17811cEC007FE5098586", unknown: "0x30be72a397667FDfD641E3e5Bd68Db657711EB20", known: "0xA1077a294dDE1B09bB078844df40758a5D0f9a27", },
      { pool: "0xA0126Ac1364606BAfb150653c7Bc9f1af4283DFa", unknown: "0xBc91E5aE4Ce07D0455834d52a9A4Df992e12FE12", known: "0x6B175474E89094C44Da98b954EedeAC495271d0F", },
      { pool: "0x24264d580711474526e8f2a8ccb184f6438bb95c", unknown: "0x47c3038ad52E06B9B4aCa6D672FF9fF39b126806", known: "0x95B303987A60C71504D99Aa1b13B4DA07b0790ab", },
    ]
  }
  const projectName = 'unknownTokensV2';

  return Promise.all(
    Object.keys(config).map((chain) => getTokenPrice(chain, timestamp)),
  );

  async function getTokenPrice(chain: string, timestamp: number) {
    const api = await getApi(chain, timestamp)
    const pricesObject: any = {}
    const data: any = config[chain]
    const pools = data.map((d: any) => d.pool)
    const unknowns = data.map((d: any) => d.unknown)
    const knowns = data.map((d: any) => d.known)
    const knownBals = await api.multiCall({ abi: 'erc20:balanceOf', calls: knowns.map((k: any, i: number) => ({ target: k, params: pools[i] })) })
    const unknownBals = await api.multiCall({ abi: 'erc20:balanceOf', calls: unknowns.map((u: any, i: number) => ({ target: u, params: pools[i] })) })
    const knownDecimals = await api.multiCall({ abi: 'erc20:decimals', calls: knowns })
    const unknownDecimals = await api.multiCall({ abi: 'erc20:decimals', calls: unknowns })

    knownBals.forEach((_: any, i: number) => {
      const token = unknowns[i].toLowerCase()
      let underlying = knowns[i]
      let price = (knownBals[i] / unknownBals[i]) * 10 ** (unknownDecimals[i] - knownDecimals[i])
      pricesObject[token] = { underlying, price }
    })
    return getWrites({ chain, timestamp, pricesObject, projectName, })
  }
}

export function pods(timestamp: number = 0) {
  return podsAdapter(timestamp);
}
export function distressed(timestamp: number = 0) {
  return Promise.all(
    Object.keys(contracts).map((chain: string) =>
      distressedAdapter(chain, timestamp),
    ),
  );
}
export function manualInput(timestamp: number = 0) {
  return Promise.all([
    manualInputAdapter("evmos", timestamp),
    manualInputAdapter("arbitrum", timestamp),
    manualInputAdapter("polygon", timestamp),
    manualInputAdapter("kava", timestamp),
    manualInputAdapter("polygon_zkevm", timestamp),
    manualInputAdapter("ethereum", timestamp),
  ]);
}
export function realt(timestamp: number = 0) {
  return Promise.all([
    realtAdapter("ethereum", timestamp),
    realtAdapter("xdai", timestamp),
  ]);
}
export function collateralizedAssets(timestamp: number = 0) {
  return collateralizedAdapter("arbitrum", timestamp, [
    {
      token: "******************************************", // DSU
      vault: "******************************************",
      collateral: "******************************************",
    },
  ]);
}
export function sweth(timestamp: number = 0) {
  return swethAdapter(timestamp);
}
export function gmd(timestamp: number = 0) {
  return gmdAdapter(timestamp);
}
export function stkaurabal(timestamp: number = 0) {
  return stkaurabalAdapter(timestamp);
}

export async function buck(timestamp: number = 0) {
  const THIRY_MINUTES = 1800;
  if (+timestamp !== 0 && timestamp < +new Date() / 1e3 - THIRY_MINUTES)
    throw new Error("Can't fetch historical data");
  const writes: Write[] = [];
  const {
    data: {
      result: {
        data: {
          content: {
            fields: { type_names, normalized_balances, coin_decimals },
          },
        },
      },
    },
  } = await axios.post("https://fullnode.mainnet.sui.io", {
    jsonrpc: "2.0",
    id: 1,
    method: "sui_getObject",
    params: [
      "0xeec6b5fb1ddbbe2eb1bdcd185a75a8e67f52a5295704dd73f3e447394775402b",
      {
        showContent: true,
      },
    ],
  });
  const usdt =
    "5d4b302506645c37ff133b98c4b50a5ae14841659738d6d733d59d0d217a93bf::coin::COIN";
  const buck =
    "ce7ff77a83ea0cb6fd39bd8748e2ec89a3f41e8efdc3f4eb123e0ca37b184db2::buck::BUCK";
  const usdtBal = normalized_balances[type_names.indexOf(usdt)];
  const buckBal = normalized_balances[type_names.indexOf(buck)];
  const buckDecimals = coin_decimals[type_names.indexOf(buck)];
  const usdtDecimals = coin_decimals[type_names.indexOf(usdt)];
  addToDBWritesList(
    writes,
    "sui",
    "0x" + buck,
    (usdtBal * 10 ** (buckDecimals - usdtDecimals)) / buckBal,
    buckDecimals,
    "BUCK",
    timestamp,
    "buck",
    0.9,
  );

  return writes;
}

export async function mooBvm(timestamp: number = 0) {
  return mooBvmAdapter(timestamp);
}

export async function velgd(timestamp: number = 0) {
  return velgAdapter(timestamp);
}

export async function warlord(timestamp: number = 0) {
  return warlordAdapter(timestamp);
}

export async function salt(timestamp: number = 0) {
  const writes: Write[] = [];
  await wNLXCore(timestamp, writes);
  const chain = "ethereum";
  const api = await getApi(chain, timestamp);
  const price = await api.call({
    abi: "uint256:priceSALT",
    target: "******************************************",
  });
  const egETH = "******************************************";
  const egETHPrice = await api.call({
    abi: "uint256:exchangeRateToNative",
    target: egETH,
  });
  const pricesObject = {
    [egETH]: {
      price: egETHPrice / 1e18,
      underlying: "******************************************",
    },
  };
  await getWrites({
    chain,
    timestamp,
    writes,
    pricesObject,
    projectName: "salt",
  });
  addToDBWritesList(
    writes,
    chain,
    "******************************************",
    price / 1e18,
    18,
    "SALT",
    timestamp,
    "other",
    0.95,
  );
  return writes;
}

async function wNLXCore(timestamp: number = 0, writes: Write[] = []) {
  const chain = "core";
  const api = await getApi(chain, timestamp);
  const wNLXCore = "******************************************";
  const supply = await api.call({
    abi: "uint256:totalSupply",
    target: wNLXCore,
  });
  const balance = await api.call({
    abi: "function getEthBalance(address) view returns (uint256)",
    target: "******************************************",
    params: wNLXCore,
  });
  const pricesObject = {
    [wNLXCore]: {
      price: balance / supply,
      underlying: "******************************************",
    },
  };
  await getWrites({
    chain,
    timestamp,
    writes,
    pricesObject,
    projectName: "other",
  });
}

async function dsu(timestamp: number = 0, writes: Write[] = []) {
  const config: {[chain: string]: { DSU: string, USDC: string, treasury: string }} = {
    arbitrum: {
      DSU: '******************************************', 
      USDC: '******************************************', 
      treasury: "******************************************"
    }, 
    perennial: {
      DSU: '******************************************', 
      USDC: '******************************************', 
      treasury: '******************************************'
    }
  }
  await Promise.all(Object.keys(config).map(async (chain: string) => {
    const { DSU, USDC, treasury } = config[chain]
    const api = await getApi(chain, timestamp);
    const supply = await api.call({ abi: "uint256:totalSupply", target: DSU });
    const balance = await api.call({
      abi: "erc20:balanceOf",
      target: USDC,
      params: treasury,
    });
    const pricesObject = {
      [DSU]: { price: (balance * 1e12) / supply, underlying: USDC },
    };
    await getWrites({
      chain,
      timestamp,
      writes,
      pricesObject,
      projectName: "dsu",
    });
  }))

  return writes
}

// price taken from unknownTokensV3 instead
async function kernel(timestamp: number = 0, writes: Write[] = []) {
  const chain = "ethereum";
  const ETH = "******************************************";
  const api = await getApi(chain, timestamp);
  const tokens = [
    {
      address: "******************************************",
      oracle: "******************************************",
      abi: "uint256:kUSDPerToken",
      underlying: "******************************************",
    },
    {
      address: "******************************************",
      oracle: "******************************************",
    },
    {
      address: "******************************************",
      oracle: "******************************************",
    },
  ];
  const pricesObject: any = {};
  for (const {
    address,
    oracle,
    abi = "uint256:getRate",
    underlying = ETH,
  } of tokens) {
    const rate = await api.call({ abi, target: oracle });
    pricesObject[address] = { price: rate / 1e18, underlying };
  }
  return getWrites({
    chain,
    timestamp,
    pricesObject,
    projectName: "other",
    writes,
  });
}

async function reyaUSD(timestamp: number = 0, writes: Write[] = []) {
  const chain = "reya";
  const api = await getApi(chain, timestamp);
  const pricesObject: any = {};
  const usdc = "******************************************";
  const rUSD = "******************************************";
  const usdBal = await api.call({
    abi: "erc20:balanceOf",
    target: usdc,
    params: rUSD,
  });
  const supply = await api.call({ abi: "erc20:totalSupply", target: rUSD });
  pricesObject[rUSD] = { price: usdBal / supply, underlying: usdc };
  return getWrites({
    chain,
    timestamp,
    pricesObject,
    projectName: "other",
    writes,
  });
}

async function symboitic(timestamp: number = 0, writes: Write[] = []) {
  const chain = 'ethereum'
  const api = await getApi(chain, timestamp)
  const factory = '******************************************'
  const entities = await api.fetchList({ lengthAbi: 'totalEntities', itemAbi: 'entity', target: factory })
  const underlyings = await api.multiCall({ abi: 'address:asset', calls: entities })
  const supplies = await api.multiCall({ abi: 'erc20:totalSupply', calls: entities })
  const bals = await api.multiCall({ abi: 'erc20:balanceOf', calls: underlyings.map((underlying: any, i: any) => ({ target: underlying, params: entities[i] })) })
  const decimalsAll = await api.multiCall({ abi: 'erc20:decimals', calls: entities })
  const uDecimalsAll = await api.multiCall({ abi: 'erc20:decimals', calls: underlyings })
  const pricesObject = {} as any
  entities.forEach((entity: any, i: any) => {
    const underlying = underlyings[i]
    const bal = bals[i]
    const supply = supplies[i]
    const decimals = decimalsAll[i]
    const uDecimals = uDecimalsAll[i]
    const price = bal * 10 ** (uDecimals - decimals) / supply
    if (isNaN(price)) return;
    pricesObject[entity] = { price, underlying, }
  })
  return getWrites({ chain, timestamp, pricesObject, projectName: "other", writes, })
}


async function karakWrapped(timestamp: number = 0, writes: Write[] = []) {
  const chain = 'ethereum'
  const api = await getApi(chain, timestamp)
  const wrappedTokens = {
    '******************************************': '******************************************',
    '******************************************': '******************************************',
    '******************************************': '******************************************',
  }
  const tokens = Object.keys(wrappedTokens)
  const uTokens = Object.values(wrappedTokens)
  const supplies = await api.multiCall({ abi: 'erc20:totalSupply', calls: tokens })
  const bals = await api.multiCall({ abi: 'erc20:balanceOf', calls: uTokens.map((v, i) => ({ target: v, params: tokens[i] })) })
  const decimalsAll = await api.multiCall({ abi: 'erc20:decimals', calls: tokens })
  const uDecimalsAll = await api.multiCall({ abi: 'erc20:decimals', calls: uTokens })
  const pricesObject = {} as any
  tokens.forEach((entity: any, i: any) => {
    const underlying = uTokens[i]
    const bal = bals[i]
    const supply = supplies[i]
    const decimals = decimalsAll[i]
    const uDecimals = uDecimalsAll[i]
    const price = bal * 10 ** (uDecimals - decimals) / supply
    if (isNaN(price)) return;
    pricesObject[entity] = { price, underlying, }
  })
  return getWrites({ chain, timestamp, pricesObject, projectName: "other", writes, })
}

async function matrixdock(timestamp: number = 0, writes: Write[] = []) {
  const chain = 'ethereum'
  const api = await getApi(chain, timestamp)
  // get gold price from chainlink oracle
  const price = (await api.call({ abi: 'uint256:latestAnswer', target: '******************************************' })) / 1e8
  const TROY_OUNCE_CONVERSION = 1.097142857;
  const goldPriceInTroyOunces = price * TROY_OUNCE_CONVERSION;
  const ethereumPricesObject = {
    '******************************************': { price: goldPriceInTroyOunces, }
  }
  const bscPricesObject = {
    '******************************************': { price: goldPriceInTroyOunces, }
  }

  await getWrites({ chain, timestamp, pricesObject: ethereumPricesObject, projectName: "other", writes, })
  return getWrites({ chain: 'bsc', timestamp, pricesObject: bscPricesObject, projectName: "other", writes, })
}


export const adapters = {
  symboitic,
  defiChain,
  shlb,
  metronome,
  buck,
  synthetix,
  glp,
  abracadabra,
  unknownTokens,
  unknownTokens2,
  pods,
  distressed,
  manualInput,
  realt,
  collateralizedAssets,
  sweth,
  gmd,
  stkaurabal,
  mooBvm,
  velgd,
  steadefi,
  teahouse,
  opdx,
  gmdV2,
  salt,
  warlord,
  opal,
  // kernel,   // price taken from unknownTokensV3 instead
  reyaUSD,
  karakWrapped,
  matrixdock,
  dsu
};
