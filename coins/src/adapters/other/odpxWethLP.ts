import { addToDBWritesList, getTokenAndRedirectData } from "../utils/database";
import { Write } from "../utils/dbInterfaces";
import { getTokenInfo } from "../utils/erc20";
import getBlock from "../utils/block";
import { call } from "@defillama/sdk/build/abi/index";
// odpxWETH-USDC
const chain = "arbitrum";
const orangeVault = "******************************************";
const odpx_vault = "******************************************";

const targets = [orangeVault];

export default async function getTokenPrice(timestamp: number) {
  const block: number | undefined = await getBlock(chain, timestamp);
  const writes: Write[] = [];
  await contractCalls(targets, block, writes, timestamp);
  return writes;
}

async function contractCalls(
  targets: string[],
  block: number | undefined,
  writes: Write[],
  timestamp: number,
) {
  const [balance, tokenInfos] = await Promise.all([
    call({
      target: targets[0],
      params: odpx_vault,
      chain,
      abi: abi.balanceOf,
      block,
    }),
    getTokenInfo(chain, [targets[0]], block),
  ]);

  const [val] = await Promise.all([
    call({
      target: targets[0],
      params: balance.output,
      chain,
      abi: abi.convertToAssets,
      block,
    }),
  ]);

  const [{ price: priceEth }] = await getTokenAndRedirectData(
    ["******************************************"],
    "arbitrum",
    timestamp,
  );

  let price = (val.output * priceEth) / balance.output;

  if (isNaN(price)) return;

  addToDBWritesList(
    writes,
    chain,
    targets[0],
    price,
    tokenInfos.decimals[0].output,
    tokenInfos.symbols[0].output,
    timestamp,
    "odpxWETH-USDC",
    1,
  );
}

const abi = {
  balanceOf: {
    constant: true,
    inputs: [
      {
        name: "_owner",
        type: "address",
      },
    ],
    name: "balanceOf",
    outputs: [
      {
        name: "balance",
        type: "uint256",
      },
    ],
    payable: false,
    type: "function",
  },
  convertToAssets: {
    constant: true,
    inputs: [{ name: "", type: "uint256" }],
    name: "convertToAssets",
    outputs: [{ name: "", type: "uint256" }],
    payable: false,
    stateMutability: "view",
    type: "function",
  },
};
