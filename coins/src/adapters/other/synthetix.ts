import { addToDBWritesList } from "../utils/database";
import { Write } from "../utils/dbInterfaces";
import axios from "axios";
import { getTokenInfo } from "../utils/erc20";
import getBlock from "../utils/block";
import { multiCall } from "@defillama/sdk/build/abi";
import {
  readFromPGCache,
  writeToPGCache,
} from "../../../../defi/src/api2/cache/file-cache";
import dayjs from "dayjs";
import { getCurrentUnixTimestamp } from "../../utils/date";

const abi = require("./abi.json");
const now = getCurrentUnixTimestamp();
const margin = 12 * 3600; // 12hrs

async function getForexRates(
  uniqueTickers: string[],
  timestamp: number,
): Promise<{ [string: string]: number }> {
  const date = dayjs(timestamp == 0 ? Date.now() : timestamp * 1000)
    .locale("US")
    .format("YYYY-MM-DD");

  const symbols = [...uniqueTickers, "USD"]
    .reduce((p: string, c: string) => `${p},${c}`, "")
    .substring(1);

  const cachedRates = await readFromPGCache("coins-forexRates");
  let rates: any;
  if (
    cachedRates &&
    "timestamp" in cachedRates &&
    now - cachedRates.timestamp < margin
  ) {
    rates = cachedRates.data;
  } else {
    rates = (
      await axios.get(
        `http://data.fixer.io/api/${date}?access_key=${process.env.FIXER_IO_KEY}&symbols=${symbols}`,
      )
    ).data.rates;
    await writeToPGCache("coins-forexRates", { data: rates, timestamp: now });
  }

  const forexPrices: { [string: string]: number } = {};
  Object.keys(rates).map(
    (r: string) => (forexPrices[r] = rates["USD"] / rates[r]),
  );
  return forexPrices;
}
const tokens = {
  optimism: {
    EUR: "******************************************",
    USD: "******************************************",
    INR: "******************************************",
  },
  ethereum: {
    AUD: "******************************************",
    CHF: "******************************************",
    EUR: "******************************************",
    GBP: "******************************************",
    JPY: "******************************************",
    KRW: "******************************************",
    //USD: "******************************************"
  },
};
async function getProxies(
  chain: string,
  tokens: any,
  block: number | undefined,
) {
  return (
    await multiCall({
      abi: abi.proxy,
      calls: tokens.map((target: string) => ({
        target: target,
      })),
      chain: chain as any,
      block,
    })
  ).output.map((r: any) => r.output);
}
export default async function getTokenPrices(timestamp: number = 0) {
  const writes: Write[] = [];
  const uniqueTickers: string[] = [];
  Object.values(tokens).map((c: any) => {
    Object.keys(c).map((s) => {
      if (!uniqueTickers.includes(s)) uniqueTickers.push(s);
    });
  });

  const forexPrices = await getForexRates(uniqueTickers, timestamp);

  for (let addresses of Object.entries(tokens)) {
    const chain = addresses[0];
    const tickers = Object.keys(addresses[1]);
    const tokens = Object.values(addresses[1]);
    const block = await getBlock(chain, timestamp);

    const [tokenInfos, tokenAddresses] = await Promise.all([
      getTokenInfo(chain, tokens, block),
      getProxies(chain, tokens, block),
    ]);

    tickers.map((t: string, i: number) => {
      if (!Object.keys(forexPrices).includes(t)) {
        return;
      }
      addToDBWritesList(
        writes,
        chain,
        tokenAddresses[i].toLowerCase(),
        forexPrices[t],
        tokenInfos.decimals[i].output,
        tokenInfos.symbols[i].output,
        timestamp,
        "synthetix",
        0.9,
      );
    });
  }

  return writes;
}
