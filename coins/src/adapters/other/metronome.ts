import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";

export const contracts: any = {
  ethereum: {
    synths: ["******************************************"],
  },
  optimism: {
    synths: [
      "******************************************",
      "******************************************",
      "******************************************",
      "******************************************",
    ],
  },
  base: {
    synths: [
      "******************************************",
      "******************************************",
      "******************************************",
    ],
  },
};

export default async function getTokenPrice(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp);
  const pricesObject: any = {};
  const writes: Write[] = [];
  const { synths } = contracts[chain];
  if (["optimism", 'base'].includes(chain)) {
    let underlyings = await api.multiCall({
      calls: synths,
      abi: "address:token",
    });
    const uDecimals = await api.multiCall({
      calls: underlyings,
      abi: "erc20:decimals",
    });
    let prices = await api.multiCall({
      calls: synths,
      abi: "uint256:pricePerShare",
    });
    underlyings.forEach((underlying: any, i: number) => {
      pricesObject[synths[i]] = {
        underlying,
        price: prices[i] / 10 ** uDecimals[i],
      };
    });
  } else {
    let underlyings = await api.multiCall({
      calls: synths,
      abi: "address:underlying",
    });
    const uDecimals = await api.multiCall({
      calls: underlyings,
      abi: "erc20:decimals",
    });
    const udata = await api.multiCall({
      calls: synths.map((v: any, i: number) => ({
        target: v,
        params: "" + 10 ** uDecimals[i],
      })),
      abi: "function quoteWithdrawIn(uint256) view returns (uint256 amount,uint256 fee)",
    });
    underlyings.forEach((underlying: any, i: number) => {
      pricesObject[synths[i]] = {
        underlying,
        price: udata[i].amount / 10 ** uDecimals[i],
      };
    });
  }

  return getWrites({
    chain,
    timestamp,
    writes,
    pricesObject,
    projectName: "metronome-synth",
  });
}
