import { call } from "@defillama/sdk/build/abi/index";
import getBlock from "../utils/block";
import { Write } from "../utils/dbInterfaces";
import { addToDBWritesList, getTokenAndRedirectData } from "../utils/database";
import abi from "./abi.json";
import { getTokenInfo } from "../utils/erc20";

const stETHvv: string = "******************************************";
const stETH: string = "******************************************";
const chain = "ethereum";

export default async function getTokenPrices(timestamp: number) {
  const writes: Write[] = [];
  const block: number | undefined = await getBlock(chain, timestamp);

  const [
    { output: totalAssets },
    { output: totalSupply },
    [{ price: stEthPrice }],
    stEthVvInfo
  ] = await Promise.all([
    call({
      target: stETHvv,
      abi: abi.totalAssets,
      block,
      chain
    }),
    call({
      target: stETHvv,
      abi: "erc20:totalSupply",
      block,
      chain
    }),
    getTokenAndRedirectData([stETH], chain, timestamp),
    getTokenInfo(chain, [stETHvv], block)
  ]);
  const price: number =
    (parseInt(totalAssets) / totalSupply) * stEthPrice;

  addToDBWritesList(
    writes,
    chain,
    stETHvv,
    price,
    stEthVvInfo.decimals[0].output,
    stEthVvInfo.symbols[0].output,
    timestamp,
    "stETHvv",
    0.9
  );

  return writes;
}
