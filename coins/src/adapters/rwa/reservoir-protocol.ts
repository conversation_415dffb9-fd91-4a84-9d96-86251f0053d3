import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";

const abi =
  "function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)";

const config: any = {
  ethereum: {
    tokens: [
      "******************************************", // rUSD
      "******************************************", // srUSD
      // "******************************************", // termIssuer
    ],
    underlying: "******************************************",
    target: "******************************************",
  },
};

async function getTokenPrices(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp);
  let { tokens, underlying, target } = config[chain];
  const pricesObject: any = {};
  const price = (await api.call({ abi, target })).answer / 1e8;
  if (isNaN(price)) throw new Error(`no latestRoundData.answer`);
  tokens.forEach(async (contract: any) => {
    pricesObject[contract] = { price, underlying };
  });

  return getWrites({
    chain,
    timestamp,
    pricesObject,
    projectName: "reservoir-protocol",
  });
}

export function reservoirprotocol(timestamp: number = 0) {
  return Promise.all(
    Object.keys(config).map((i) => getTokenPrices(i, timestamp)),
  );
}
