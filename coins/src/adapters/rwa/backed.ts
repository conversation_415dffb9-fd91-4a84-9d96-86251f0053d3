import { Write } from "../utils/dbInterfaces";
import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";
import abi from "./abi.json";

const oracles: any = [
  {
    key: "bCSPX",
    oracle: "******************************************",
    chain: "ethereum",
    ethToken: "******************************************",
  },
  {
    key: "bCOIN",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bNIU",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bIB01",
    oracle: "******************************************",
    chain: "ethereum",
    ethToken: "******************************************",
  },
  {
    key: "bIBTA",
    oracle: "******************************************",
    chain: "ethereum",
    ethToken: "******************************************",
  },
  {
    key: "bHIGH",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bC3M",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bERNA",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bERNX",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bZPR1",
    oracle: "******************************************",
    chain: "arbitrum",
    ethToken: "******************************************",
  },
  {
    key: "bNVDA",
    oracle: "******************************************",
    chain: "ethereum",
    ethToken: "******************************************",
  },
];

async function getTokenPrices(chain: string, timestamp: number) {
  const ethApi = await getApi("ethereum", timestamp);
  const arbiApi = await getApi("arbitrum", timestamp);
  const ethOracles = oracles.filter((o: any) => o.chain === "ethereum");
  const arbiOracles = oracles.filter((o: any) => o.chain === "arbitrum");
  const ethPrices = await ethApi.multiCall({
    abi: abi.latestAnswer,
    calls: ethOracles.map((o: any) => o.oracle),
  });
  const arbiPrices = await arbiApi.multiCall({
    abi: abi.latestAnswer,
    calls: arbiOracles.map((o: any) => o.oracle),
  });
  ethOracles.forEach(
    (oracle: any, i: any) => (oracle.price = ethPrices[i] / 1e8),
  );
  arbiOracles.forEach(
    (oracle: any, i: any) => (oracle.price = arbiPrices[i] / 1e8),
  );
  const pricesObject: any = {};
  const writes: Write[] = [];
  oracles.forEach((contract: any) => {
    pricesObject[contract.ethToken] = { price: contract.price };
  });

  writes.push(
    ...(await getWrites({
      chain,
      timestamp,
      writes,
      pricesObject,
      projectName: "backed",
    })),
  );

  writes.map((w: Write) => {
    writes.push({
      ...w,
      PK: `asset#avax:${w.PK.substring(w.PK.indexOf(":") + 1)}`,
    });
    writes.push({
      ...w,
      PK: `asset#base:${w.PK.substring(w.PK.indexOf(":") + 1)}`,
    });
    writes.push({
      ...w,
      PK: `asset#polygon:${w.PK.substring(w.PK.indexOf(":") + 1)}`,
    });
    writes.push({
      ...w,
      PK: `asset#xdai:${w.PK.substring(w.PK.indexOf(":") + 1)}`,
    });
  });
  return writes;
}

export function backed(timestamp: number = 0) {
  return getTokenPrices("ethereum", timestamp);
}
