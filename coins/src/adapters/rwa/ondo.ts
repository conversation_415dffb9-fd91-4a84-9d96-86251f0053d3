import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import { addToDBWritesList } from "../utils/database";

const config: { [chain: string]: { [symbol: string]: string } } = {
  ethereum: {
    OUSG: "******************************************",
    USDYc: "******************************************",
    USDY: "******************************************",
  },
  polygon: {
    OUSG: "******************************************",
  },
};

export async function ondo(timestamp: number): Promise<Write[]> {
  const ethApi = await getApi("ethereum", timestamp);
  const tokenPrices: { [address: string]: number } = {
    OUSG:
      (await ethApi.call({
        abi: "uint256:rwaPrice",
        target: "******************************************",
      })) / 1e18,
    USDYc: 
      (await ethApi.call({
        abi: "uint256:getPrice",
        target: "******************************************",
      })) / 1e18,
    USDY:
      (await ethApi.call({
        abi: "uint256:getPrice",
        target: "******************************************",
      })) / 1e18,
  };

  const writes: Write[] = [];

  Object.keys(config).map((chain: string) =>
    Object.keys(config[chain]).map((symbol: string) => {
      const price: number = tokenPrices[symbol];
      const address: string = config[chain][symbol];
      addToDBWritesList(
        writes,
        chain,
        address,
        price,
        18,
        symbol,
        timestamp,
        "ondo-rwa",
        0.8,
      );
    }),
  );

  return writes;
}
