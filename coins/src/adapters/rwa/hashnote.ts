import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import { addToDBWritesList } from "../utils/database";
const abi = 'function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)'

export async function hashnote(timestamp: number): Promise<Write[]> {
    const symbol = 'USYC'
    const api = await getApi("ethereum", timestamp);
    const tokenPrice = (await api.call({ abi, target: "******************************************", })).answer / 1e8;

    const writes: Write[] = [];
    addToDBWritesList(writes, 'canto', '******************************************', tokenPrice, 6, symbol, timestamp, "hashnote-rwa", 0.8,);
    addToDBWritesList(writes, 'ethereum', '******************************************', tokenPrice, 6, symbol, timestamp, "hashnote-rwa", 0.8,);

    return writes;
}
