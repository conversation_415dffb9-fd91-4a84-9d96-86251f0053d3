import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";

const config: {
  [chain: string]: {
    [symbol: string]: {
      address: string;
      target: string;
      decimals: number;
    };
  };
} = {
  ethereum: {
    AA_idle_Fasanara: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
    AA_BastionUSDC: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
    AA_AdaptiveFrontierUSDC: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
  },
  optimism: {
    AA_FalconXUSDC: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
  },
  arbitrum: {
    AA_BastionUSDT: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
  },
  polygon: {
    AA_BastionUSDT: {
      address: "******************************************",
      target: "******************************************",
      decimals: 6,
    },
  },
};

export async function getTokenPrices(
  chain: string,
  timestamp: number
): Promise<Write[]> {
  const tokens = config[chain];
  const ethApi = await getApi(chain, timestamp);

  const targets = Object.values(tokens).map((t) => t.target);
  const [prices, underlyings] = await Promise.all([
    ethApi.multiCall({
      abi: "uint256:priceAA",
      calls: targets,
    }),
    ethApi.multiCall({
      abi: "address:token",
      calls: targets,
    }),
  ]);

  const pricesObject = prices.reduce((acc, price, index) => {
    const tokenName = Object.keys(tokens)[index];
    const tokenInfo = tokens[tokenName];
    const underlying = underlyings[index];
    return {
      ...acc,
      [tokenInfo.address]: {
        underlying,
        decimals: 18,
        symbol: tokenName,
        price: Number(price) / Number(`1e${tokenInfo.decimals}`),
      },
    };
  }, {});

  return getWrites({
    chain,
    timestamp,
    pricesObject,
    projectName: "pareto",
  });
}

export function pareto(timestamp: number = 0) {
  return Promise.all(
    Object.keys(config).map((chain) => getTokenPrices(chain, timestamp))
  );
}
