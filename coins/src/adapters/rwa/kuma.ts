import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import { addToDBWritesList } from "../utils/database";

const config: { [chain: string]: { [symbol: string]: string } } = {
  ethereum: {
    USK: "******************************************",
    EGK: "******************************************"
  },
  polygon: {
    FRK: "******************************************",
  },
  linea: {
    USK: "******************************************"
  },
  telos: {
    USK: "******************************************"
  }
};

const abi = "function latestRoundData() view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)";

export async function kuma(timestamp: number): Promise<Write[]> {
  const ethApi = await getApi("ethereum", timestamp);
  const polyApi = await getApi("polygon", timestamp);
  const prices: { [symbol: string]: number } = {
    USK:
      (await ethApi.call({
        abi,
        target: "******************************************",
      })).answer / 1e8,
    FRK: 
      (await polyApi.call({
        abi,
        target: "******************************************",
      })).answer / 1e8,
  };
  // KUMA tokens are redeemable for the bond NFT at any time, therefore their value can be assumed to be stable
  // the currently issued tokens in this list are all being held until the bond matures so there's no other way to price them
  const redirects: { [symbol: string]: string } = {
    EGK: 'coingecko#euro-coin'
  }

  const writes: Write[] = [];

  Object.keys(config).map((chain: string) =>
    Object.keys(config[chain]).map((symbol: string) => {
      const price = prices[symbol];
      const redirect = redirects[symbol];
      const address: string = config[chain][symbol];
      addToDBWritesList(
        writes,
        chain,
        address,
        price,
        18,
        symbol,
        timestamp,
        "kuma-protocol",
        1,
        redirect
      );
    }),
  );

  return writes;
}