import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";

const config: { [chain: string]: { [symbol: string]: string } } = {
  canto: {
    fbill: "******************************************",
    fCOIN: "******************************************",
    ifBill: "******************************************",
  },
  arbitrum: {
    fbill: "******************************************",
    fCOIN: "******************************************",
    ifBill: "******************************************",
  },
  blast: {
    fbill: "******************************************",
    fCOIN: "******************************************",
    ifBill: "******************************************",
  },
  ethereum: {
    fBill: "******************************************",
    ifBill: "******************************************",
    fCOIN: "******************************************",
    ifCOIN: "******************************************",
    fHOOD: "******************************************",
    ifHOOD: "******************************************",
    fSPQQQ: "******************************************",
    ifSPQQQ: "******************************************",
    fHV1: "******************************************",
    ifHV1: "******************************************",
  },
};

async function getTokenPrices(chain: string, timestamp: number) {
  const api = await getApi(chain, timestamp);
  let tokens = Object.values(config[chain]) as any;
  const supplies = await api.multiCall({
    abi: "uint256:totalSupply",
    calls: tokens,
  });
  const nav = await api.multiCall({ abi: "uint256:nav", calls: tokens });
  const decimals = await api.multiCall({
    abi: "uint8:decimals",
    calls: tokens,
  });
  const pricesObject: any = {};
  tokens.forEach((contract: any, idx: number) => {
    if (!+supplies[idx] || !+nav[idx]) return;
    const price = nav[idx] / (supplies[idx] / 10 ** (decimals[idx] - 18));
    if (isNaN(price)) return;
    pricesObject[contract] = { price };
  });

  return getWrites({
    chain,
    timestamp,
    pricesObject,
    projectName: "fortunafi",
  });
}

export function fortunafi(timestamp: number = 0) {
  return Promise.all(
    Object.keys(config).map((i) => getTokenPrices(i, timestamp)),
  );
}
