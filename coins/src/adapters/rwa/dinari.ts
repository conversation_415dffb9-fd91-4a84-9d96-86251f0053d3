import { Write } from "../utils/dbInterfaces";
import getWrites from "../utils/getWrites";
import { getApi } from "../utils/sdk";

const _getTokensAbi = "function getDShares() external view returns (address[] memory, address[] memory)"
const _latestPriceAbi = "function latestFillPrice(address assetToken, address paymentToken) view returns (tuple(uint256 price, uint64 blocktime))"
const config: any = {
  arbitrum: {
    factory: "******************************************",
    processor: "******************************************",
    quoteToken: "******************************************",
    usdplus: "******************************************",
  },
  ethereum: {
    factory: "******************************************",
    processor: "******************************************",
    quoteToken: "******************************************",
    usdplus: "******************************************",
  },
  blast: {
    factory: "******************************************",
    processor: "******************************************",
    quoteToken: "******************************************",
  },
  kinto: {
    factory: "******************************************",
    processor: "******************************************",
    quoteToken: "******************************************",
    usdplus: "******************************************",
  },
  base: {
    factory: "******************************************",
    processor: "******************************************",
    quoteToken: "******************************************",
    usdplus: "******************************************",
  },
  plume_mainnet: {
    factory: "0x7a861Ae8C708DC6171006C57c9163BD2BB57a8Aa",
    processor: "0x68Dd74e23461d99b7312Bfb5baddfd3Fa28404c7",
    quoteToken: "0x1fA3671dF7300DF728858B88c7216708f22dA3Fb",
    usdplus: "0x1fA3671dF7300DF728858B88c7216708f22dA3Fb",
  }
};

async function getTokenPrices(chain: string, timestamp: number, writes: Write[] = []): Promise<Write[]> {
  const api = await getApi(chain, timestamp);
  const { getTokensAbi = _getTokensAbi, latestPriceAbi = _latestPriceAbi, factory, processor, quoteToken, usdplus } = config[chain];
  // dShares prices
  let [tokens] = await api.call({ target: factory, abi: getTokensAbi, })
  const supplies = await api.multiCall({  abi: 'erc20:totalSupply', calls: tokens})
  tokens = tokens.filter((_: any, idx: number) => +supplies[idx] > 0)
  const prices = (await api.multiCall({
    abi: latestPriceAbi,
    target: processor,
    calls: tokens.map((token: any) => ({ params: [token, quoteToken], })),
  })).map((p: any) => p.price / 1e18);

  // USD+
  if (usdplus) {
    tokens.push(usdplus);
    prices.push(1);
  }

  // convert to writes
  const pricesObject: any = {};
  tokens.forEach((contract: any, idx: number) => {
    pricesObject[contract] = { price: prices[idx] };
  });
  return getWrites({ chain, timestamp, pricesObject, projectName: "dinari", writes,})

}

export async function dinari(timestamp: number = 0): Promise<Write[]> {
  const writes: Write[] = [];
  for (const chain of Object.keys(config)) {
    await getTokenPrices(chain, timestamp, writes)
  }
  return writes;
}
