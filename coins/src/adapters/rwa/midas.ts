import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";
import getWrites from "../utils/getWrites";

const DATA_FEED_ABI = "function getDataInBase18() external view returns (int256 answer)";
const AGGREGATOR_ABI = "function latestRoundData() external view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)";

const contracts: {
  [chain: string]: { name: string; token: string; oracle?: string; requiresConversion?: boolean }[];
} = {
  ethereum: [
    { name: "mTBILL", token: "******************************************", oracle: "******************************************" },
    { name: "mBASI<PERSON>", token: "******************************************", oracle: "******************************************" },
    { name: "mBTC", token: "******************************************", oracle: "******************************************", requiresConversion: true },
    { name: "mEDGE", token: "******************************************", oracle: "******************************************" },
    { name: "mMEV", token: "******************************************", oracle: "******************************************" },
    { name: "mRe7YIELD", token: "******************************************", oracle: "******************************************" },
    { name: "mF-ONE", token: "******************************************", oracle: "******************************************" },
    { name: "mHYPER", token: "******************************************", oracle: "******************************************" },
  ],
  base: [
    { name: "mTBILL", token: "******************************************", oracle: "******************************************" },
    { name: "mBASIS", token: "******************************************", oracle: "******************************************" },
  ],
  sapphire: [
    { name: "mTBILL", token: "******************************************" },
  ],
  etlk: [
    { name: "mTBILL", token: "******************************************" },
    { name: "mBASIS", token: "******************************************" },
  ],
  rsk: [
    { name: "mTBILL", token: "******************************************" },
    { name: "mBTC", token: "******************************************" },
  ],
  plume_mainnet: [
    { name: "mTBILL", token: "******************************************", oracle: "******************************************" },
    { name: "mBASIS", token: "******************************************", oracle: "******************************************" },
    { name: "mEDGE", token: "******************************************", oracle: "******************************************" },
    { name: "mMEV", token: "******************************************", oracle: "******************************************" },
  ],
};

const btcToUsdOracleEth = "******************************************";

async function getBtcToUsdPrice(timestamp: number): Promise<number> {
  const api = await getApi("ethereum", timestamp);
  const response = await api.call({ abi: AGGREGATOR_ABI, target: btcToUsdOracleEth });
  return response.answer / 1e8;
}

async function getTokenPrices(chain: string, timestamp: number, ethereumPrices: Record<string, number>): Promise<Write[]> {
  const api = await getApi(chain, timestamp);
  const tokens = contracts[chain] || [];
  const btcToUsdPrice = await getBtcToUsdPrice(timestamp);

  const tokensWithOracles = tokens.filter(t => t.oracle);
  const tokensWithoutOracles = tokens.filter(t => !t.oracle);

  let prices: Record<string, { price: number }> = {};
  if (tokensWithOracles.length > 0) {
    const dataFeedResponses = await api.multiCall({
      abi: DATA_FEED_ABI,
      calls: tokensWithOracles.map(({ oracle }) => oracle as string),
    });

    prices = tokensWithOracles.reduce((acc, { token, requiresConversion, name }, i) => {
      const rawVal = dataFeedResponses[i];
      if (rawVal !== null && rawVal !== undefined) {
        const price = (rawVal / 1e18) * (requiresConversion ? btcToUsdPrice : 1);
        acc[token] = { price };

        if (chain === 'ethereum') {
          ethereumPrices[name] = price;
        }
      }
      return acc;
    }, {} as Record<string, { price: number }>);
  }

  for (const t of tokensWithoutOracles) {
    const { name, token } = t;
    const fallbackPrice = ethereumPrices[name];
    if (fallbackPrice !== undefined) {
      prices[token] = { price: fallbackPrice };
    }
  }

  return getWrites({
    chain,
    timestamp,
    pricesObject: prices,
    projectName: "midas",
  });
}

export async function midas(timestamp: number = 0): Promise<Write[]> {
  const ethereumPrices: Record<string, number> = {};

  const chains = Object.keys(contracts);
  const ethereumIndex = chains.indexOf('ethereum');

  let ethereumWrites: Write[] = [];
  let otherChainWrites: Write[] = [];

  if (ethereumIndex !== -1) {
    ethereumWrites = await getTokenPrices('ethereum', timestamp, ethereumPrices);
    chains.splice(ethereumIndex, 1);
  }

  if (chains.length > 0) {
    const results = await Promise.all(chains.map(c => getTokenPrices(c, timestamp, ethereumPrices)));
    otherChainWrites = results.flat();
  }

  return [...ethereumWrites, ...otherChainWrites];
}
