import { getCurrentUnixTimestamp } from "../../utils/date";
import { addToDBWritesList } from "../utils/database";
import { Write } from "../utils/dbInterfaces";
import { getApi } from "../utils/sdk";

const vbillAddresses: { [key: string]: string } = {
  ethereum: "******************************************",
  avax: "******************************************",
  bsc: "******************************************",
  solana: "********************************************",
};

export async function vbill(timestamp: number = 0) {
  const api = await getApi("ethereum", timestamp);
  const priceData = await api.call({
    abi: "function latestRoundData() view returns (uint80 roundId, int256 answer, uint256 startedAt, uint256 updatedAt, uint80 answeredInRound)",
    target: "******************************************",
  });

  if (
    priceData.updatedAt <
    (timestamp == 0 ? getCurrentUnixTimestamp() : timestamp) - 3 * 60 * 60
  )
    throw new Error("VBILL price is stale");

  const writes: Write[] = [];
  Object.keys(vbillAddresses).map(async (chain) => {
    addToDBWritesList(
      writes,
      chain,
      vbillAddresses[chain],
      priceData.answer / 10 ** 8,
      6,
      "VBILL",
      timestamp,
      "vbill",
      1,
    );
  });

  return writes;
}
