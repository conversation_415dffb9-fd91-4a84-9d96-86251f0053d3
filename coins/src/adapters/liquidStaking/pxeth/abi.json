{"getStakingValidatorCount": {"inputs": [], "name": "getStakingValidatorCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "pendingDeposit": {"inputs": [], "name": "pendingDeposit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, "buffer": {"inputs": [], "name": "buffer", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}}