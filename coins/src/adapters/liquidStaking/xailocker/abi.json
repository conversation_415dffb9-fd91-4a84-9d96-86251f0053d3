{"curvePoolQuoteAlXaiToXai": {"stateMutability": "view", "type": "function", "name": "get_dy", "inputs": [{"name": "i", "type": "int128"}, {"name": "j", "type": "int128"}, {"name": "dx", "type": "uint256"}], "outputs": [{"name": "", "type": "uint256"}]}, "previewSharesToLiquidity": {"type": "function", "name": "previewSharesToLiquidity", "inputs": [{"name": "amount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}}