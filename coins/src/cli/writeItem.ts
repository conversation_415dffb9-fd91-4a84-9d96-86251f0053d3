import dynamodb from "../utils/shared/dynamodb";

async function main() {
    await dynamodb.put({
        PK: `asset#cardano:fbae99b8679369079a7f6f0da14a2cf1c2d6bfd3afdf3a96a64ab67a0014df1047454e5358`,
        SK: 0,
        symbol: 'GENSX',
        created: 1690064168,
        confidence: 0.99,
        decimals: 6,
        redirect: 'coingecko#genius-x',
    })
}
main()

// Example:
// tableName='prod-coins-table' AWS_REGION='eu-central-1' npx ts-node src/cli/writeItem.ts