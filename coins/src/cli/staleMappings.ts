import mappings from "../adapters/tokenMapping_added.json";
import PromisePool from "@supercharge/promise-pool";
import fetch from "node-fetch";

async function main() {
  const array: string[] = [];
  Object.keys(mappings).map((chain: any) =>
    Object.keys(mappings[chain as keyof typeof mappings]).map(
      (address: string) => array.push(`${chain}:${address}`),
    ),
  );
  const errors: string[] = [];
  await PromisePool.withConcurrency(5)
    .for(array)
    .process(async (key: string) => {
      const res = await fetch(
        `https://coins.llama.fi/prices/current/${key}?apikey=${process.env.COINS_KEY}`,
      ).then((r) => r.json());
      if (!Object.keys(res.coins).length) errors.push(key);
    })
    .catch((e) => {
      e;
    });

  return;
}
main(); // ts-node coins/src/cli/staleMappings.ts
const errors = [
  "nova:0x657a66332a65b535da6c5d67b8cd1d410c161a08",
  "nova:******************************************",
  "metis:0x5801d0e1c7d977d78e4890880b8e579eb4943276",
  "velas:0xaadbaa6758fc00dec9b43a0364a372605d8f1883",
  "optimism:0xc5db22719a06418028a40a9b5e9a7c02959d0d08",
  "bsc:0xa35d95872d8eb056eb2cbd67d25124a6add7455e",
  "bsc:0x808a3f2639a5cd54d64ed768192369bcd729100e",
  "bsc:0xc5fb6476a6518dd35687e0ad2670cb8ab5a0d4c5",
  "bsc:******************************************",
  "bsc:******************************************",
  "bsc:******************************************",
  "bsc:******************************************",
  "bsc:******************************************",
  "bsc:******************************************",
  "fantom:******************************************",
  "fantom:******************************************",
  "bittorrent:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "klaytn:******************************************",
  "klaytn:******************************************",
  "klaytn:******************************************",
  "oasis:******************************************",
  "oasis:******************************************",
  "harmony:******************************************",
  "harmony:******************************************",
  "avax:******************************************",
  "moonbeam:******************************************",
  "moonbeam:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "polygon:******************************************",
  "arbitrum:******************************************",
  "arbitrum:******************************************",
  "arbitrum:******************************************",
  "solana:JET6zMJWkCN9tpRT2v2jfAmm5VnQFDpUBCyaKojmGtz",
  "solana:PUhuAtMHsKavMTwZsLaDeKy2jb7ciETHJP7rhbKLJGY",
  "astar:******************************************",
  "astar:******************************************",
  "astar:******************************************",
  "astar:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "wemix:******************************************",
  "step:******************************************",
  "step:******************************************",
  "step:******************************************",
  "step:******************************************",
  "zyx:******************************************",
  "terra:terra1dy9kmlm4anr92e42mrkjwzyvfqwz66un00rwr5",
  "terra:terra15k5r9r8dl8r7xlr29pry8a9w7sghehcnv5mgp6",
  "terra:terra17y9qkl8dfkeg4py7n0g5407emqnemc3yqk5rup",
  "terra:terra1hzh9vpxhsk8253se0vv5jj6etdvxu3nv8z07zu",
  "terra:terra1kc87mu460fwkqte29rquh4hc20m54fxwtsx7gp",
  "terra:terra1dzhzukyezv0etz22ud940z7adyv7xgcjkahuun",
  "terra:terra1vwz7t30q76s7xx6qgtxdqnu6vpr3ak3vw62ygk",
  "orai:orai1gzvndtzceqwfymu2kqhta2jn6gmzxvzqwdgvjw",
  "near:marmaj.tkn.near",
  "near:token.marmaj.near",
  "cube:******************************************",
  "hoo:******************************************",
  "hoo:0x3eff9d389d13d6352bfb498bcf616ef9b1beac87",
  "cube:0x9d3f61338d6eb394e378d28c1fd17d5909ac6591",
  "milkomeda_a1:******************************************",
  "milkomeda_a1:******************************************",
  "milkomeda_a1:******************************************",
  "bitgert:0x0eb9036cbe0f052386f36170c6b07ef0a0e3f710",
  "bitgert:0x11203a00a9134db8586381c4b2fca0816476b3fd",
  "echelon:******************************************",
  "echelon:0xadee5159f4f82a35b9068a6c810bdc6c599ba6a8",
  "kekchain:0x54bd9d8d758ac3717b37b7dc726877a23aff1b89",
  "kekchain:******************************************",
  "kekchain:0x71ec0cb8f7dd4f4c5bd4204015c4c287fbdaa04a",
  "aptos:0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::BnbCoin",
  "dogechain:0x1df5c9b7789bd1416d005c15a42762481c95edc2",
  "dogechain:0xbfbb7b1d22ff521a541170cafe0c9a7f20d09c3b",
  "canto:0x80a16016cc4a2e6a2caca8a4a498b1699ff0f844",
  "canto:0x38d11b40d2173009adb245b869e90525950ae345",
  "algorand:6547014",
  "algorand:239444645",
  "algorand:300208676",
  "algorand:342889824",
  "algorand:163650",
  "algorand:297995609",
  "algorand:463554836",
  "algorand:511484048",
  "algorand:684649988",
  "algorand:692085161",
  "algorand:792313023",
  "algorand:871930188",

  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-17",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-11",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-19",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-1",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-20",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-5",
  "asset#tezos:kt18fp5rctw7mbwdmzfwjlduhs5mejmagdsz-18",
  "asset#tezos:kt1ussfaxyqcjsvpeid7u1bwgky3tayn7nwy-3",
  "asset#tezos:kt1ussfaxyqcjsvpeid7u1bwgky3tayn7nwy-4",
  "asset#tezos:kt1ussfaxyqcjsvpeid7u1bwgky3tayn7nwy-5",

  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-17",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-11",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-19",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-1",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-20",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-5",
  "tezos:KT18fp5rcTW7mbWDmzFwjLDUhs5MeJmagDSZ-18",
  "tezos:KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-3",
  "tezos:KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-4",
  "tezos:KT1UsSfaXyqcjSVPeiD7U1bWgKy3taYN7NWY-5",
  "godwoken_v1:******************************************",
  "godwoken_v1:******************************************",
  "godwoken_v1:0xb44a9b6905af7c801311e8f4e76932ee959c663c",
  "godwoken_v1:******************************************",
  "waves:DSbbhLsSTeDg5Lsiufk2Aneh3DjVqJuPr2M9uU1gwy5p",
  "waves:9sQutD5HnRvjM1uui5cVC4w9xkMPAfYEV8ymug3Mon2Y",
  "waves:DHgwrRvVyqJsepd32YbBqUeDH4GJ1N984X8QoekjgH8J",
  "songbird:0x70ad7172ef0b131a1428d0c1f66457eb041f2176",
  "curio:0x134ebab7883dfa9d04d20674dd8a8a995fb40ced",
  "gochain:0x67bbb47f6942486184f08a671155fcfa6cad8d71",
  "smartbch:0x24d8d5cbc14fa6a740c3375733f0287188f8df3b",
  "vision:0x79ffbc4fff98b821d59dbd7b33f91a2783006b6f",
  "kava:0x332730a4f6e03d9c55829435f10360e13cfa41ff",
  "kava:0xb44a9b6905af7c801311e8f4e76932ee959c663c",
  "kava:******************************************",
  "kava:******************************************",
  "kava:******************************************",
  "kava:******************************************",
  "kava:******************************************",
  "kava:******************************************",
  "callisto:******************************************",
  "iotex:******************************************",
  "iotex:******************************************",
  "iotex:******************************************",
  "tlchain:******************************************",
  "ethpow:******************************************",
  "ethpow:******************************************",
  "ethpow:******************************************",
  "ethpow:******************************************",
  "ethpow:******************************************",
  "xdc:******************************************",
  "elrond:KOSON-5dd4fa",
  "elrond:CYC-b4ed61",
  "elrond:LPAD-84628f",
  "elrond:FITY-73f8fc",
  "elrond:UPARK-982dd6",
  "elrond:TLC-1a2357",
  "elrond:erd1qqqqqqqqqqqqqpgq3ahw8fctzfnwgvq2g4hjsqzkkvgl9ksr2jps646dnj",
  "dexit:******************************************",
  "dexit:******************************************",
  "ibc:DBF5FA602C46392DE9F4796A0FC7D02F3A8A3D32CA3FAA50B761D4AA6F619E95",
  "ibc:764D1629980B02BAFF3D25BEE4FB1E0C5E350AFA252FDF66E68E10D2179A826A",
  "ibc:1620B95419728A7DEF482DEB9462DD6B9FA120BCB49CCCF74209A56AB9835E59",
  "ibc:4171A6F59F8A708D807E03B43FA0E16EC7041C189557B7A8E519757424367D41",
  "bitindi:******************************************",
  "bitindi:******************************************",
  "europa:******************************************",
  "stacks:SPSCWDV3RKV5ZRN1FQD84YE1NQFEDJ9R1F4DYQ11.newyorkcitycoin-token-v2::newyorkcitycoin",
  "stacks:SP1H1733V5MZ3SZ9XRW9FKYGEZT0JDGEB8Y634C7R.miamicoin-token-v2::miamicoin",
  "stacks:SP2H8PY27SEZ03MWRKS5XABZYQN17ETGQS3527SA5.newyorkcitycoin-token::newyorkcitycoin",
  "stacks:SP466FNC0P7JWTNM2R9T199QRZN1MYEDTAR0KP27.miamicoin-token::miamicoin",
  "map:0x141b30Dd30FAFb87ec10312d52e5dbD86122FE14",
  "map:0x640a4C0AaA4870BaDE2F646B7Da87b3D53819C4f",
  "eos_evm:0x922d641a426dcffaef11680e5358f34d97d112e1",
  "eos_evm:******************************************",
  "eos_evm:******************************************",
  "eos_evm:******************************************",
  "onus:0xfe39fdc0012dcf10c9f674ea7e74889e4d71a226",
  "manta:0x387660BC95682587efC12C543c987ABf0fB9778f",
  // "fraxtal:0xfc00000000000000000000000000000000000008",
  // "fraxtal:0xfc00000000000000000000000000000000000007",
  // "fraxtal:0xfc00000000000000000000000000000000000004",
];
