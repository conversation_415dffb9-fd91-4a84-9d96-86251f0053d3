import chainToCoingeckoId from "../../common/chainToCoingeckoId";
import { successResponse, wrap, IResponse } from "./utils/shared";
export const adaptersRepoChainsJson: string[] = [
  "abstract",
  "acala",
  "ace",
  "aelf",
  "aeternity",
  "agoric",
  "ailayer",
  "airdao",
  "aleph_zero",
  "alephium",
  "algorand",
  "alv",
  "ancient8",
  "ao",
  "apechain",
  "aptos",
  "arbitrum",
  "arbitrum_nova",
  "archway",
  "area",
  "artela",
  "assetchain",
  "astar",
  "astrzk",
  "aura",
  "aurora",
  "avax",
  "babylon",
  "band",
  "base",
  "basecamp",
  "beam",
  "berachain",
  "bevm",
  "bfc",
  "bifrost",
  "binance",
  "bitchain",
  "bitcoin",
  "bitcoincash",
  "bitgert",
  "bitindi",
  "bitkub",
  "bitrock",
  "bittensor",
  "bittorrent",
  "blast",
  "bob",
  "boba",
  "boba_avax",
  "boba_bnb",
  "bone",
  "borrowed",
  "bostrom",
  "bouncebit",
  "bsc",
  "bsquared",
  "btn",
  "btr",
  "bytomsidechain",
  "callisto",
  "candle",
  "canto",
  "carbon",
  "cardano",
  "celestia",
  "celo",
  "chainflip",
  "chainx",
  "chihuahua",
  "chz",
  "civitia",
  "clover",
  "clv",
  "cmp",
  "comdex",
  "concordium",
  "conflux",
  "core",
  "corn",
  "cosmos",
  "coti",
  "crab",
  "crescent",
  "cronos",
  "cronos_zkevm",
  "crossfi",
  "csc",
  "cube",
  "curio",
  "cyeth",
  "darwinia",
  "dash",
  "defichain",
  "defichain_evm",
  "defiverse",
  "degen",
  "dexalot",
  "dexit",
  "dfk",
  "dfs",
  "doge",
  "dogechain",
  "dsc",
  "duckchain",
  "dydx",
  "dymension",
  "echelon",
  "echelon_initia",
  "eclipse",
  "edg",
  "elastos",
  "elrond",
  "elsm",
  "elys",
  "empire",
  "energi",
  "energyweb",
  "enuls",
  "eon",
  "eos",
  "eos_evm",
  "equilibrium",
  "era",
  "ergo",
  "ethereum",
  "ethereumclassic",
  "ethf",
  "ethpow",
  "etlk",
  "etn",
  "europa",
  "everscale",
  "evmos",
  "fantom",
  "fhe",
  "filecoin",
  "findora",
  "firechain",
  "flare",
  "flame",
  "flow",
  "fluence",
  "formnetwork",
  "fraxtal",
  "fsc",
  "ftn",
  "fuel",
  "functionx",
  "fuse",
  "fusion",
  "genesys",
  "genshiro",
  "goat",
  "gochain",
  "godwoken",
  "godwoken_v1",
  "goerli",
  "gravity",
  "gravitybridge",
  "grove",
  "ham",
  "harmony",
  "haven1",
  "heco",
  "hedera",
  "heiko",
  "hela",
  "hemi",
  "hoo",
  "hpb",
  "hsk",
  "hydra",
  "hydradx",
  "hyperliquid",
  "icon",
  "icp",
  "idex",
  "imx",
  "inevm",
  "initia",
  "injective",
  "ink",
  "interlay",
  "iota",
  "iotaevm",
  "iotex",
  "islm",
  "jbc",
  "joltify",
  "juno",
  "kadena",
  "karak",
  "kardia",
  "karura",
  "kava",
  "kcc",
  "kekchain",
  "kinto",
  "kintsugi",
  "klaytn",
  "kopi",
  "kroma",
  "kujira",
  "kusama",
  "lac",
  "lachain",
  "lamden",
  "lbry",
  "lens",
  "libre",
  "lightlink_phoenix",
  "linea",
  "liquidchain",
  "lisk",
  "litecoin",
  "loop",
  "lukso",
  "lung",
  "manta",
  "manta_atlantic",
  "mantle",
  "mantra",
  "map",
  "massa",
  "matchain",
  "mayachain",
  "meer",
  "merlin",
  "meta",
  "meter",
  "metis",
  "migaloo",
  "milkomeda",
  "milkomeda_a1",
  "milkyway",
  "milkyway_rollup",
  "mint",
  "mixin",
  "mode",
  "moonbeam",
  "moonriver",
  "morph",
  "move",
  "mtt_network",
  "multivac",
  "muuchain",
  "mvc",
  "mxczkevm",
  "nahmii",
  "naka",
  "namada",
  "near",
  "neo",
  "neo3",
  "neon_evm",
  "neox",
  "neutron",
  "new",
  "nibiru",
  "noble",
  "nolus",
  "nos",
  "nova",
  "nuls",
  "oas",
  "oasis",
  "obyte",
  "occ",
  "odyssey",
  "okexchain",
  "omax",
  "ontology",
  "ontology_evm",
  "onus",
  "ogpu",
  "op_bnb",
  "openzk",
  "optimism",
  "orai",
  "ore",
  "osmosis",
  "ox_chain",
  "ozone",
  "palette",
  "palm",
  "parallel",
  "parex",
  "penumbra",
  "perennial",
  "persistence",
  "pg",
  "pgn",
  "planq",
  "plume",
  "plume_mainnet",
  "pokt",
  "polis",
  "polkadex",
  "polkadot",
  "polygon",
  "polygon_zkevm",
  "polynomial",
  "pool2",
  "posi",
  "prom",
  "proton",
  "pryzm",
  "pulse",
  "q",
  "qom",
  "quasar",
  "qubic",
  "quicksilver",
  "radixdlt",
  "rari",
  "rbn",
  "real",
  "redstone",
  "reef",
  "regen",
  "rei",
  "reichain",
  "renec",
  "reya",
  "ripple",
  "rollux",
  "ronin",
  "rpg",
  "rsk",
  "rss3_vsl",
  "rvn",
  "saakuru",
  "saga",
  "sanko",
  "sapphire",
  "scroll",
  "secret",
  "sei",
  "shape",
  "shibarium",
  "shiden",
  "shido",
  "shimmer_evm",
  "sifchain",
  "silicon_zk",
  "smartbch",
  "solana",
  "sommelier",
  "soneium",
  "songbird",
  "sonic",
  "sophon",
  "sora",
  "spn",
  "stacks",
  "stafi",
  "staking",
  "starcoin",
  "stargaze",
  "starknet",
  "stellar",
  "step",
  "stratis",
  "stride",
  "sty",
  "sui",
  "sseed",
  "supra",
  "svm",
  "swan",
  "swellchain",
  "sx",
  "sxr",
  "syscoin",
  "taiko",
  "tara",
  "telos",
  "tenet",
  "terra",
  "terra2",
  "tezos",
  "theta",
  "thorchain",
  "thundercore",
  "titan",
  "tlchain",
  "tombchain",
  "tomochain",
  "ton",
  "treasure",
  "tron",
  "ubiq",
  "ultra",
  "ultron",
  "umee",
  "unichain",
  "unit0",
  "vana",
  "vechain",
  "velas",
  "venom",
  "verus",
  "vinu",
  "vision",
  "vite",
  "vive",
  "wan",
  "water",
  "waves",
  "wax",
  "wc",
  "wemix",
  "winr",
  "xai",
  "xdai",
  "xdc",
  "xlayer",
  "xpla",
  "xp",
  "xsat",
  "zeniq",
  "zero_network",
  "zeta",
  "zilliqa",
  "zircuit",
  "zkcandy",
  "zkcro",
  "zkfair",
  "zklink",
  "zksync",
  "zora",
  "zyx",
  "xpr",
];
export const handler = async (): Promise<IResponse> => {
  const allChains = [
    ...new Set([...adaptersRepoChainsJson, ...Object.keys(chainToCoingeckoId)]),
  ];
  return successResponse(allChains, 3600);
};
export default wrap(handler);
