name: Defi

on:
  push:
    branches: [master]
  repository_dispatch:
  workflow_dispatch:

defaults:
  run:
    working-directory: defi

jobs:
  changes:
    runs-on: ubuntu-latest
    outputs:
      defi: ${{ steps.filter.outputs.defi }}
    steps:
      - uses: actions/checkout@v2
      - uses: dorny/paths-filter@4512585405083f25c027a35db413c2b3b9006d50
        id: filter
        with:
          filters: |
            defi:
              - 'defi/**'
  deploy:
    needs: changes
    if: ${{ needs.changes.outputs.defi == 'true' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: git submodule update --init --recursive
      - name: Get Node.js
        uses: actions/setup-node@v1
        with:
          node-version: '16'
      - run: echo "TODAY=$(date +%Y%m%d)" >> $GITHUB_ENV
      - name: Get jest cache
        uses: actions/cache@v3
        with:
          path: /tmp/jest-cache
          key: ${{ runner.os }}-jest-${{ env.TODAY }}
      - run: cd DefiLlama-Adapters && git checkout main && git pull
      - run: cd dimension-adapters && git checkout master && git pull
      - run: cd emissions-adapters && git checkout master && git pull
      - run: npm  ci || npm i
      - name: Run tests
        run: npm t data && npx esbuild src/protocols/import.test.ts --bundle --log-limit=0 --platform=node | node
        env:
          ETHEREUM_RPC: FAKE_ETHEREUM_RPC
          GRAPH_API_KEY: FAKE_GRAPH_API_KEY
      - name: Deploy infrastructure stack
        run: npm run deploy:prod
        env:
          ETHEREUM_RPC: ${{ secrets.ETHEREUM_RPC }}
          POLYGON_RPC: ${{ secrets.POLYGON_RPC }}
          BSC_RPC: ${{ secrets.BSC_RPC }}
          FANTOM_RPC: ${{ secrets.FANTOM_RPC }}
          ARBITRUM_RPC: ${{ secrets.ARBITRUM_RPC }}
          OPTIMISM_RPC: ${{ secrets.OPTIMISM_RPC }}
          XDAI_RPC: ${{ secrets.XDAI_RPC }}
          HARMONY_RPC: ${{ secrets.HARMONY_RPC }}
          SOLANA_RPC: ${{ secrets.SOLANA_RPC }}
          MOONRIVER_RPC: ${{ secrets.MOONRIVER_RPC }}
          CRONOS_RPC: ${{ secrets.CRONOS_RPC }}
          COVALENT_KEY: ${{ secrets.COVALENT_KEY }}
          OUTDATED_WEBHOOK: ${{ secrets.OUTDATED_WEBHOOK }}
          HOURLY_OUTDATED_WEBHOOK: ${{ secrets.HOURLY_OUTDATED_WEBHOOK }}
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          SPIKE_WEBHOOK: ${{ secrets.SPIKE_WEBHOOK }}
          CLOUDWATCH_WEBHOOK_URL: ${{ secrets.CLOUDWATCH_WEBHOOK_URL }}
          DROPS_WEBHOOK: ${{ secrets.DROPS_WEBHOOK }}
          EULER_MONGODB_APIKEY: ${{ secrets.EULER_MONGODB_APIKEY }}
          INFLUXDB_TOKEN: ${{ secrets.INFLUXDB_TOKEN }}
          FATHOM_SECRET: ${{ secrets.FATHOM_SECRET }}
          DAILY_GROWTH_WEBHOOK: ${{ secrets.DAILY_GROWTH_WEBHOOK }}
          MONITOR_WEBHOOK: ${{ secrets.MONITOR_WEBHOOK }}
          STALE_COINS_ADAPTERS_WEBHOOK: ${{ secrets.STALE_COINS_ADAPTERS_WEBHOOK }}
          TEAM_WEBHOOK: ${{ secrets.TEAM_WEBHOOK }}
          PROD_VYBE_API_KEY: ${{ secrets.PROD_VYBE_API_KEY }}
          BIT_QUERY_API_KEY: ${{ secrets.BIT_QUERY_API_KEY }}
          VOLUMES_WEBHOOK: ${{ secrets.VOLUMES_WEBHOOK }}
          FEES_WEBHOOK: ${{ secrets.FEES_WEBHOOK }}
          DERIVATIVES_WEBHOOK: ${{ secrets.DERIVATIVES_WEBHOOK }}
          AGGREGATORS_WEBHOOK: ${{ secrets.AGGREGATORS_WEBHOOK }}
          OPTIONS_WEBHOOK: ${{ secrets.OPTIONS_WEBHOOK }}
          ZEROx_API_KEY: ${{ secrets.ZEROx_API_KEY }}
          PANCAKESWAP_OPBNB_SUBGRAPH: ${{ secrets.PANCAKESWAP_OPBNB_SUBGRAPH }}
          PERENNIAL_V2_SUBGRAPH_API_KEY: ${{ secrets.PERENNIAL_V2_SUBGRAPH_API_KEY }}
          INDEXER_DB: ${{ secrets.INDEXER_DB }}
          AIRTABLE_API_KEY: ${{ secrets.AIRTABLE_API_KEY }}
          AGGREGATOR_DB_URL: ${{ secrets.AGGREGATOR_DB_URL }}
          R2_ACCESS_KEY_ID: ${{ secrets.R2_ACCESS_KEY_ID }}
          R2_SECRET_ACCESS_KEY: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          R2_ENDPOINT: ${{ secrets.R2_ENDPOINT }}
          INDEXA_DB: ${{ secrets.INDEXA_DB }}
          DL_NEWS_API: ${{ secrets.DL_NEWS_API }}
          DL_NEWS_ACCESS_TOKEN: ${{ secrets.DL_NEWS_ACCESS_TOKEN }}
          COINS_DB: ${{ secrets.COINS_DB }}
          ACCOUNTS_DB: ${{ secrets.ACCOUNTS_DB }}
          CG_KEY: ${{ secrets.CG_KEY }}
          ERROR_DB: ${{ secrets.ERROR_DB }}
          ERROR_REPORTS_WEBHOOK: ${{ secrets.ERROR_REPORTS_WEBHOOK }}
          ERROR_REPORTS_DB: ${{ secrets.ERROR_REPORTS_DB }}
          ALLIUM_API_KEY: ${{ secrets.ALLIUM_API_KEY }}
          FLIPSIDE_API_KEY: ${{ secrets.FLIPSIDE_API_KEY }}
          COINGLASS_KEY: ${{ secrets.COINGLASS_KEY }}
          UNLOCKS_WEBHOOK: ${{ secrets.UNLOCKS_WEBHOOK }}
          SMARDEX_SUBGRAPH_API_KEY: ${{ secrets.SMARDEX_SUBGRAPH_API_KEY }}
          DUNE_API_KEYS: ${{ secrets.DUNE_API_KEYS }}
          APIKEY: ${{ secrets.APIKEY }}
          SEARCH_MASTER_KEY: ${{ secrets.SEARCH_MASTER_KEY }}
          FRONT_API_TOKEN: ${{ secrets.FRONT_API_TOKEN }}
