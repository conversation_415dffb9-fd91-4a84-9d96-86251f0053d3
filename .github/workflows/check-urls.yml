name: Check URLs

on:
  schedule:
    - cron: '0 8 * * 0'
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Ruby
        uses: ruby/setup-ruby@354a1ad156761f5ee2b7b13fa8e09943a5e8d252
        with:
          ruby-version: 2.6
      - name: Install awesome_bot
        run: gem install awesome_bot -v 1.20.0
      - name: Check URLs
        run: awesome_bot defi/src/protocols/* --allow-redirect --request-delay 0.2
