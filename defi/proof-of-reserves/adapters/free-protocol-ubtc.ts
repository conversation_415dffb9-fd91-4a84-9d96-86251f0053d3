import { getBitcoinBridgeLockAndMintAdapter } from '../utils/bridge';

const protocolId = 'free-protocol-ubtc';

const mintedTokens = [
  // uBTC Bridge
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'mode',
    address: '******************************************',
  },
  {
    chain: 'core',
    address: '******************************************',
  },
  {
    chain: 'op_bnb',
    address: '******************************************',
  },
  {
    chain: 'hemi',
    address: '******************************************',
  },
  {
    chain: 'sei',
    address: '******************************************',
  },
  {
    chain: 'goat',
    address: '******************************************',
  },
]

const reservesTokens = [
  // uBTC Bridge
  {
    chain: 'bsquared',
    address: '******************************************',
    owners: ['******************************************'],
  },
]

export default getBitcoinBridgeLockAndMintAdapter(protocolId, mintedTokens, reservesTokens);
