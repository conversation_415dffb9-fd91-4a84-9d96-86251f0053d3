import { getBitcoinBridgeLockAndMintAdapter } from '../utils/bridge';

const protocolId = 'free-protocol-bbtc';

const mintedTokens = [
  // BounceBit BBTC Bridge
  {
    chain: 'zklink',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
]

const reservesTokens = [
  // BounceBit BBTC Bridge
  {
    chain: 'bouncebit',
    address: '******************************************',
    owners: ['******************************************'],
  },
]

export default getBitcoinBridgeLockAndMintAdapter(protocolId, mintedTokens, reservesTokens);
