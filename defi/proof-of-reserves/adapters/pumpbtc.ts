import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'pumpbtc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'morph',
    address: '******************************************',
  },
  {
    chain: 'sei',
    address: '******************************************',
  },
  {
    chain: 'avax',
    address: '******************************************',
  },
  {
    chain: 'zircuit',
    address: '******************************************',
  },
  {
    chain: 'optimism',
    address: '******************************************',
  },
  {
    chain: 'soneium',
    address: '******************************************',
  },
  {
    chain: 'hemi',
    address: '******************************************',
  },
  {
    chain: 'klaytn',
    address: '******************************************',
  },
  {
    chain: 'ink',
    address: '******************************************',
  },
  {
    chain: 'core',
    address: '******************************************',
  },
  {
    chain: 'corn',
    address: '******************************************',
  },
  {
    chain: 'zeta',
    address: '******************************************',
  },
  {
    chain: 'zklink',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
  {
    chain: 'taiko',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
