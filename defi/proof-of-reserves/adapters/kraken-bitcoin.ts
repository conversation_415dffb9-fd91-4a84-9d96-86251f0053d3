import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'kraken-bitcoin';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'optimism',
    address: '******************************************',
  },
  {
    chain: 'ink',
    address: '******************************************',
  },
  {
    chain: 'unichain',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
