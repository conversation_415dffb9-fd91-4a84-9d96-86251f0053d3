import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'teleswap';

const mintedTokens = [
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'polygon',
    address: '******************************************',
  },
  {
    chain: 'bsquared',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
