import { getBitcoinBridgeLockAndMintAdapter } from '../utils/bridge';

const protocolId = 'free-protocol-mbtc';

const mintedTokens = [
  // M-BTC Bridge
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'zklink',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
  {
    chain: 'scroll',
    address: '******************************************',
  },
  {
    chain: 'manta',
    address: '******************************************',
  },
  {
    chain: 'mode',
    address: '******************************************',
  },
  {
    chain: 'era',
    address: '******************************************',
  },
  // {
  //   chain: 'bsquared',
  //   address: '******************************************',
  // },
  {
    chain: 'btr',
    address: '******************************************',
  },
  {
    chain: 'taiko',
    address: '******************************************',
  },
  {
    chain: 'kava',
    address: '******************************************',
  },
  {
    chain: 'klaytn',
    address: '******************************************',
  },
  {
    chain: 'sei',
    address: '******************************************',
  },
  {
    chain: 'duckchain',
    address: '******************************************',
  },
  {
    chain: 'iotex',
    address: '******************************************',
  },
  {
    chain: 'lisk',
    address: '******************************************',
  },
]

const reservesTokens = [
  // M-BTC Bridge
  {
    chain: 'merlin',
    address: '******************************************',
    owners: ['******************************************', '******************************************'],
  },
]

export default getBitcoinBridgeLockAndMintAdapter(protocolId, mintedTokens, reservesTokens);
