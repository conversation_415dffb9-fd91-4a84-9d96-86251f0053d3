import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'bedrock-unibtc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'zeta',
    address: '******************************************',
  },
  {
    chain: 'iotex',
    address: '******************************************',
  },
  {
    chain: 'taiko',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'optimism',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'mode',
    address: '******************************************',
  },
  {
    chain: 'merlin',
    address: '******************************************',
  },
  {
    chain: 'btr',
    address: '******************************************',
  },
  // {
  //   chain: 'bsquared',
  //   address: '******************************************',
  // },
  {
    chain: 'corn',
    address: '******************************************',
  },
  {
    chain: 'berachain',
    address: '******************************************',
  },
  {
    chain: 'sonic',
    address: '******************************************',
  },
  {
    chain: 'hemi',
    address: '******************************************',
  },
  {
    chain: 'duckchain',
    address: '******************************************',
  },
  {
    chain: 'rsk',
    address: '******************************************',
  },
  {
    chain: 'sei',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
