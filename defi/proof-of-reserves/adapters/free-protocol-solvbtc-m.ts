import { getBitcoinBridgeLockAndMintAdapter } from '../utils/bridge';

const protocolId = 'free-protocol-solvbtc-m';

const mintedTokens = [
  // SolvBTC.m Bridge
  {
    chain: 'zklink',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
  {
    chain: 'core',
    address: '******************************************',
  },
  {
    chain: 'btr',
    address: '******************************************',
  },
  // {
  //   chain: 'bsquared',
  //   address: '******************************************',
  // },
  {
    chain: 'scroll',
    address: '******************************************',
  },
]

const reservesTokens = [
  // SolvBTC.m Bridge
  {
    chain: 'merlin',
    address: '******************************************',
    owners: ['******************************************', '******************************************'],
  },
]

export default getBitcoinBridgeLockAndMintAdapter(protocolId, mintedTokens, reservesTokens);
