import { TokenConfig } from "../../types";

// https://docs.universal.xyz/docs/developers/contract-addresses
const tokenAddresses: Array<string> = [
  '0xa3A34A0D9A08CCDDB6Ed422Ac0A28a06731335aA',
  '0x3a51f2a377EA8B55FAf3c671138A00503B031Af3',
  '0x7bE0Cc2cADCD4A8f9901B4a66244DcDd9Bd02e0F',
  '0xF1143f3A8D76f1Ca740d29D5671d365F66C44eD1',
  '0x12E96C2BFEA6E835CF8Dd38a5834fa61Cf723736',
  '0x0F813f4785b2360009F9aC9BF6121a85f109efc6',
  '0x3EB097375fc2FC361e4a472f5E7067238c547c52',
  '0x5ed25E305E08F58AFD7995EaC72563E6BE65A617',
  '0x9B8Df6E244526ab5F6e6400d331DB28C8fdDdb55',
  '0x2615a94df961278DcbC41Fb0a54fEc5f10a693aE',
  '0x1cff25B095cf6595afAbe35Dd7e5348666e57C11',
  '0xd6a34b430C05ac78c24985f8abEE2616BC1788Cb',
  '0x239b9C1F24F3423062B0d364796e07Ee905E9FcE',
  '0xd403D1624DAEF243FbcBd4A80d8A6F36afFe32b2',
  '0xE868C3d83EC287c01Bcb533A33d197d9BFa79DAD',
  '0xfb3CB973B2a9e2E09746393C59e7FB0d5189d290',
  '0x9c0e042d65a2e1fF31aC83f404E5Cb79F452c337',
  '0x4b92eA5A2602Fba275150db4201A6047056F6913',
  '0x30F16E3273AB6e4584B79B76fD944E577e49a5c8',
  '0xa260BA5fd9FF3FaE55Ac4930165A9C33519dE694',
  '0xb0505e5a99abd03d94a1169e638B78EDfEd26ea4',
  '0x71a67215a2025F501f386A49858A9ceD2FC0249d',
  '0xE5c436B0a34DF18F1dae98af344Ca5122E7d57c4',
  '0x8CCf84dE79dF699A373421C769f1900Aa71200B0',
  '0x378c326A472915d38b2D8D41e1345987835FaB64',
  '0xc79e06860Aa9564f95E08fb7E5b61458d0C63898',
  '0x40318eE213227894b5316E5EC84f6a5caf3bBEDd',
  '0xf383074c4B993d1ccd196188d27D0dDf22AD463c',
  '0xD61BCF79b26787AE993f75B064d2e3b3cc738C5d',
  '0xAcbF16f82753F3d52A2C87e4eEDA220c9A7A3762',
  '0x6e934283DaE5D5D1831cbE8d557c44c9B83F30Ee',
  '0xDB18Fb11Db1b972A54bD89cE04bAd61855c07788',
  '0x0935b271CA903ADA3FFe1Ac1353fC4A49E7EE87b',
  '0x3d00283AF5AB11eE7f6Ec51573ab62b6Fb6Dfd8f',
  '0x8989377fd349ADFA99E6CE3Cb6c0D148DfC7F19e',
  '0x135Ff404bA56E167F58bc664156beAa0A0Fd95ac',
  '0x893ADcbdC7FcfA0eBb6d3803f01Df1eC199Bf7C5',
  '0x1B94330EEc66BA458a51b0b14f411910D5f678d0',
  '0xD7D5c59457d66FE800dBA22b35e9c6C379D64499',
  '0x31d664ebd97A50d5a2Cd49B16f7714AB2516Ed25',
  '0x05f191a4Aac4b358AB99DB3A83A8F96216ecb274',
  '0x5A03841C2e2f5811f9E548cF98E88e878e55d99E',
  '0x508e751fdCf144910074Cc817a16757F608DB52A',
  '0x16275fD42439A6671b188bDc3949a5eC61932C48',
  '0x9AF46F95a0a8be5C2E0a0274A8b153C72d617E85',
  '0x83f31af747189c2FA9E5DeB253200c505eff6ed2',
  '0xc5cDEb649ED1A7895b935ACC8EB5Aa0D7a8492BE',
  '0xD6A746236F15E18053Dd3ae8c27341B44CB08E59',
  '0x3ECb91ac996E8c55fe1835969A4967F95a07Ca71',
  '0xe3AE3EE16a89973D67b678aaD2c3bE865Dcc6880',
  '0x2f2041c267795a85B0De04443E7B947A6234fEe8',
  '0x44951C66dFe920baED34457A2cFA65a0c7ff2025',
  '0x3a6B4b4F2250B8CCe56cED4ca286a2ebe6F479A2',
  '0x1B0DcC586323C0e10f8Be72EcC104048f25FD625',
  '0x444Fa322DA64A49A32D29ccd3a1f4DF3De25cF52',
  '0x6ca225AE2C92c8A7E9c3162cFcAaA55aD0B09701',
  '0xd045be6AB98D17A161cfCfc118a8b428D70543Ff',
  '0x6814e4BE03aEB33fe135Fe0e85CA6b0A03247519',
  '0x2198b777D5Cb8CD5Aa01d5C4d70f8F28fED9BC05',
  '0x90131D95a9a5b48b6a3eE0400807248bEcf4B7A4',
  '0xFa15f1b48447D34B107C8A26cC065E1e872b1a9D',
  '0xFDf116c8bEf1D4060e4117092298ABFF80B170CA',
  '0xf413aF1169516A3256504977b8ed0248fBd48f23',
  '0xcB474f3dEE195a951f3584B213d16d2d4D4ee503',
  '0xED1A31BB946F0B86Cf9d34A1c90546Ca75b091b0',
  '0xdf5913632251585a55970134Fad8A774628E9388',
  '0x8f2bD24a6406142cbAE4b39E14bE8efc8157D951',
  '0x17f8d5Aa7779094c32536fEcb177f93B33b3C3e2',
  '0x0340ff1765f0099b3BD1c4664CE03d8Fd794Fad1',
  '0xf653E8B6Fcbd2A63246c6B7722d1e9d819611241',
  '0x901754d839CF91eaa3ff7CB11408750fc94174E4',
  '0xf56Ce53561a9cc084e094952232bBfE1e5fb599e',
  '0xD01CB4171A985571dEFF48c9dC2F6E153A244d64',
  '0x3c07eF1bD575B5f5b1ffCb868353f5BC501ed482',
  '0xf081701aF06a8d4EcF159C9C178b5cA6A78B5548',
  '0x544F87A5aa41Fcd725EF7C78a37cd9C1c4bA1650',
  '0xD76d45358B79564817aa87F02F3b85338B96f06a',
  '0xFdCa15bd55F350a36E63C47661914d80411d2C22',
  '0x12A063BeF460bEA2b4D0B504bd78BBa58fB3da7e',
  '0xA2FD26586610955955b9a6E4BE477433224a35Bf',
  '0x3c569273572706380785e079C8BC16df6a31BC51',
  '0xdef3369Cb0B783a5F8Ee93aaF9674ddE53C3CE2a',
  '0xf5c9E46A87093fB29e40A9e498B078058bBADC74',
  '0xede6B57341c88652e5970229bd84FC316dB1C85D',
  '0xdbBC41FFD1e4D219E2cfd5Bfe9e4623cd4274532',
];

const mintedTokens: Array<TokenConfig> = [];
for (const tokenAddress of tokenAddresses) {
  for (const chain of ['base', 'polygon', 'wc', 'katana']) {
    if (tokenAddress === '0x3EB097375fc2FC361e4a472f5E7067238c547c52' && chain === 'wc') {
      continue;
    }
    mintedTokens.push({
      chain: chain,
      address: tokenAddress,
      llamaCoinPriceId: `base:${tokenAddress}`,
    })
  }
}

export default mintedTokens;
