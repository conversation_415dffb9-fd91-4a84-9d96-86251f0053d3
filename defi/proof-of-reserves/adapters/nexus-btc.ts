import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'nexus-btc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'core',
    address: '******************************************',
  },
  {
    chain: 'hemi',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
