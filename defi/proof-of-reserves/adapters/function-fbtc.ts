import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'function-fbtc';

// https://fbtc.com/proof-of-assets
// https://docs.fbtc.com/ecosystem/locked-fbtc-token/locked-fbtc-protocols-information
const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'mantle',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'sonic',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'berachain',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
