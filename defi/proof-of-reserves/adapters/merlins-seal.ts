import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'merlins-seal';

const mintedTokens = [
  {
    chain: 'merlin',
    address: '******************************************',
  },
  // {
  //   chain: 'ethereum',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'era',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'sei',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'klaytn',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'taiko',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'lisk',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'linea',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'mode',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'bsc',
  //   address: '******************************************',
  // },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens, ['******************************************']);
