import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'bedrock-brbtc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'berachain',
    address: '******************************************',
  },
]

export default {
  disabled: true,
  ...getBitcoinReservesAdapter(protocolId, mintedTokens),
};
