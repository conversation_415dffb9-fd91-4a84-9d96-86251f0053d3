import { getBitcoinReservesAdapter } from '../utils/bitcoin';

// lorenzo stBTC
const protocolId = 'lorenzo';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'btr',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens, ['******************************************']);
