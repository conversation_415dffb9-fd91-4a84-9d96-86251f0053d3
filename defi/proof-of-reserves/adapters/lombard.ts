import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'lombard';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'etlk',
    address: '******************************************',
  },
  {
    chain: 'katana',
    address: '******************************************', // BTCK
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
