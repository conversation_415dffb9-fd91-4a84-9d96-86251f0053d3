import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'unirouter';

const mintedTokens = [
  {
    chain: 'bsquared',
    address: '******************************************',
  },
  // {
  //   chain: 'bsc',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'core',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'op_bnb',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'hemi',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'sei',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'mode',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'swellchain',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'soneium',
  //   address: '******************************************',
  // },
  // {
  //   chain: 'goat',
  //   address: '******************************************',
  // },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
