import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'obeliskbtc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'bsquared',
    address: '******************************************',
  },
  {
    chain: 'mode',
    address: '******************************************',
  },
  {
    chain: 'taiko',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
