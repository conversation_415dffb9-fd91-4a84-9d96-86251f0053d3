import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'coinbase-btc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'solana',
    address: 'cbbtcf3aa214zXHbiAZQwf4122FBYbraNdFqgw4iMij',
    decimals: 8,
  },
]

export default {
  whitelisted: true,
  ... getBitcoinReservesAdapter(protocolId, mintedTokens),
};
