import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'ibtc-network';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'optimism',
    address: '******************************************',
  },
  {
    chain: 'avax',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
