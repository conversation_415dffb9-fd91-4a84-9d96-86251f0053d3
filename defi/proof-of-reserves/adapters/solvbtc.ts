import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'solvbtc';

const mintedTokens = [
  {
    chain: 'ethereum',
    address: '******************************************',
  },
  {
    chain: 'arbitrum',
    address: '******************************************',
  },
  {
    chain: 'bsc',
    address: '******************************************',
  },
  {
    chain: 'avax',
    address: '******************************************',
  },
  {
    chain: 'bob',
    address: '******************************************',
  },
  {
    chain: 'base',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
  {
    chain: 'taiko',
    address: '******************************************',
  },
  {
    chain: 'btr',
    address: '******************************************',
  },
  {
    chain: 'mode',
    address: '******************************************',
  },
  {
    chain: 'corn',
    address: '******************************************',
  },
  {
    chain: 'sonic',
    address: '******************************************',
  },
  {
    chain: 'soneium',
    address: '******************************************',
  },
  {
    chain: 'rsk',
    address: '******************************************',
  },
  {
    chain: 'berachain',
    address: '******************************************',
  },
  {
    chain: 'sei',
    address: '******************************************',
  },
  {
    chain: 'era',
    address: '******************************************',
  },
  {
    chain: 'hyperliquid',
    address: '******************************************',
  },
  {
    chain: 'ink',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
