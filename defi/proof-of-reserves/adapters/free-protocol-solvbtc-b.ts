import { getBitcoinBridgeLockAndMintAdapter } from '../utils/bridge';

const protocolId = 'free-protocol-solvbtc-b';

const mintedTokens = [
  // SolvBTC.b Bridge
  {
    chain: 'zklink',
    address: '******************************************',
  },
  {
    chain: 'linea',
    address: '******************************************',
  },
  {
    chain: 'core',
    address: '******************************************',
  },
  {
    chain: 'scroll',
    address: '******************************************',
  },
]

const reservesTokens = [
  // SolvBTC.b Bridge
  {
    chain: 'bsc',
    address: '******************************************',
    owners: ['******************************************'],
  },
] 

export default getBitcoinBridgeLockAndMintAdapter(protocolId, mintedTokens, reservesTokens);
