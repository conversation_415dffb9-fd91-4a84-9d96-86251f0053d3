import { getBitcoinReservesAdapter } from '../utils/bitcoin';

const protocolId = 'exsat-staking-btc';

const mintedTokens = [
  {
    chain: 'xsat',
    address: '******************************************',
  },
  {
    chain: 'plume_mainnet',
    address: '******************************************',
  },
  {
    chain: 'hemi',
    address: '******************************************',
  },
  {
    chain: 'goat',
    address: '******************************************',
  },
]

export default getBitcoinReservesAdapter(protocolId, mintedTokens);
