{"name": "defillama-server", "private": true, "version": "1.0.2", "scripts": {"aws-sdk": "^2.1692.0", "deploy:env": "npm run prebuild && sls deploy --stage $NODE_ENV", "deploy:dev": "export NODE_ENV=dev && npm run deploy:env", "deploy:prod": "export NODE_OPTIONS=--max-old-space-size=6144 && export NODE_ENV=prod && npm run deploy:env", "deploy": "npm run deploy:dev", "format": "prettier --write \"src/**/*.ts\"", "serve": "node --max-old-space-size=8192 node_modules/serverless/bin/serverless offline start", "test": "jest", "test:watch": "jest --watch", "prebuild": "npx ts-node --logError --transpile-only src/cli/buildRequires.ts", "postinstall": "npm update @defillama/sdk", "pretest": "npm run prebuild", "build": "sls package", "updateAdapters": "cd DefiLlama-Adapters && git checkout main && git pull", "clear-cache": "export HISTORICAL=true && export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError --transpile-only  src/cli/clearProtocolCache.ts", "fillOld": "export HISTORICAL=true && export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError --transpile-only  src/cli/fillOld.ts", "fillOldChain": "export HISTORICAL=true && export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError --transpile-only  src/cli/fillOldChain.ts", "fillLast": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError --transpile-only  src/cli/fillLast.ts", "delete-tvl": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/cli/deleteTvl.ts", "displayData": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/cli/displayData.ts", "customFill": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/cli/customFill.ts", "update-submodules": "git submodule update --init --recursive --remote --merge && npm run prebuild ", "update-metadata-file": "bash src/api2/scripts/updateMetadataScript.sh", "get-snapshot-ids": "npx ts-node --logError src/governance/getSnapshotIds.ts", "get-overview-local": "npm run update-submodules && export AWS_REGION='eu-central-1' && export tableName='prod-table' && export runLocal='true' && npx ts-node --logError --transpile-only src/adaptors/cli/runGetOverview.ts", "interactive": "npx ts-node --logError --transpile-only src/cli/interactive.ts", "task-tvl": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && NODE_OPTIONS=\"--max-old-space-size=10144\" npx ts-node --logError --transpile-only src/storeTvlTask.ts", "store-chain-assets": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/storeChainAssets.ts", "override-chain-assets": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/overrideChainAssets.ts", "store-chain-asset-historical-flows": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/storeChainAssetFlows.ts", "store-emissions": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/storeEmissions.ts", "store-emissions-index": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError src/storeEmissionsIndex.ts", "cache-config": "npx ts-node --logError --transpile-only src/cacheConfig.ts", "update-search": "npx ts-node --logError src/updateSearch.ts", "run-local": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx ts-node --logError --transpile-only src/runLocalTVL.ts", "api2-build": "bash src/api2/scripts/build.sh", "api2-dev": "ts-node --logError --transpile-only src/api2/index.ts", "api2-prod": "bash src/api2/scripts/prod_start.sh", "api2-cron-task": "npx ts-node --logError --transpile-only src/api2/cron-task/index.ts", "run-governance": "npx ts-node --logError --transpile-only src/governance/test.ts", "cron-dimensions": "export AWS_REGION='eu-central-1' && export tableName='prod-table' && npx  --node-options='--max-old-space-size=20144' ts-node --logError --transpile-only src/api2/cron-task/dimensions.ts", "cron-raises": "npx ts-node --logError --transpile-only src/api2/cron-task/raises.ts", "extension-updateExtensionTwitterConfig": "export AWS_REGION='eu-central-1' && npx ts-node --logError --transpile-only src/api2/scripts/updateExtensionTwitterConfig.ts", "dimensions-store-all": "npx  --node-options='--max-old-space-size=30144' ts-node --logError --transpile-only src/adaptors/handlers/storeAdaptorData/storeAll.ts  2>&1 | tee dimensionsRun.log; node src/api2/scripts/dimensions/printDimensionRunStats.js  2>&1 | tee dimensionsRunStats2.log", "fillOld-dimensions": "npx ts-node --logError --transpile-only src/adaptors/handlers/storeAdaptorData/refillScript.ts", "fillOld-dimensions-interactive": "npm run update-submodules; npx ts-node --logError --transpile-only src/cli/interactive-dimensions.ts", "fillOld-dimensions-all": "refill_all_missing_protocols=true NODE_OPTIONS=\"--max-old-space-size=8144\"  npx ts-node --logError --transpile-only src/adaptors/handlers/storeAdaptorData/refillScript.ts", "notify-outdated": "npx ts-node --logError --transpile-only src/server-scripts/notifyOutdated.ts", "notify-stablecoin-spike": "npx ts-node --logError --transpile-only src/server-scripts/notifyStablecoinSpikes.ts", "dead-protocols-check": "npx ts-node --logError --transpile-only src/api2/scripts/dimensions/checkForDeadProtocols.ts", "twitter-update": "npx ts-node --logError --transpile-only src/twitter/updateTwitterData.ts", "local-test-por": "npx ts-node --logError --transpile-only proof-of-reserves/cli/test.ts", "run-check-por": "npx ts-node --logError --transpile-only proof-of-reserves/cli/check.ts", "ui-tool": "npm run update-submodules; cd ui-tool; npm run start-server"}, "devDependencies": {"@babel/preset-env": "^7.18.2", "@babel/preset-typescript": "^7.17.12", "babel-jest": "^29.0.0", "babel-loader": "^8.2.5", "esbuild": "^0.14.42", "inquirer": "^8.2.6", "inquirer-autocomplete-prompt": "^2.0.0", "inquirer-date-prompt": "^2.0.1", "inquirer-search-list": "^1.2.6", "jest": "^29.0.0", "jest-dynalite": "^3.5.1", "prettier": "^2.6.2", "serverless": "^3.0.0", "serverless-esbuild": "^1.30.0", "serverless-offline": "^13.3.2", "serverless-plugin-lambda-insights": "^1.6.0", "serverless-plugin-log-retention": "^2.0.0", "serverless-prune-plugin": "^2.0.0", "ts-jest": "^29.0.0", "ts-loader": "^9.0.0", "ts-node": "^10.8.0", "typescript": "^5.0.0"}, "dependencies": {"@aws-sdk/client-s3": "^3.245.0", "@defillama/adapters": "file:./DefiLlama-Adapters", "@defillama/dimension-adapters": "file:./dimension-adapters", "@defillama/emissions-adapters": "file:./emissions-adapters", "@defillama/sdk": "^5.0.126", "@elastic/elasticsearch": "^8.13.1", "@supercharge/promise-pool": "^2.3.2", "@types/async-retry": "^1.4.8", "@types/aws-lambda": "^8.10.97", "@types/inquirer-autocomplete-prompt": "^2.0.0", "@types/jest": "^27.5.1", "@types/lodash": "^4.14.202", "@types/node": "^18.0.0", "@types/node-fetch": "^2.5.10", "@types/promise.allsettled": "^1.0.6", "axios": "^1.6.5", "bignumber.js": "^9.0.1", "buffer-layout": "^1.2.2", "colord": "^2.9.3", "dotenv": "^16.0.1", "ethers": "^6.0.0", "hyper-express": "^6.8.7", "lodash": "^4.17.21", "node-fetch": "^2.6.1", "p-limit": "^3.0.0", "pg-promise": "^11.4.0", "pm2": "^5.3.0", "postgres": "^3.3.1", "promise.allsettled": "^1.0.5", "reflect-metadata": "^0.1.13", "sequelize": "^6.33.0", "typeorm": "^0.3.11"}}