service: defillama

package:
  individually: true
  excludeDevDependencies: true

provider:
  name: aws
  runtime: nodejs16.x
  lambdaHashingVersion: 20201221
  memorySize: 250
  region: eu-central-1
  endpointType: REGIONAL # Set to regional because the api gateway will be behind a cloudfront distribution
  stage: dev # Default to dev if no stage is specified
  iamRoleStatements:
    - Effect: Allow # X-Ray permissions
      Action:
        - xray:PutTraceSegments
        - xray:PutTelemetryRecords
      Resource: "*"
    - Effect: "Allow"
      Action:
        - dynamodb:DescribeTable
        - dynamodb:Query
        - dynamodb:Scan
        - dynamodb:GetItem
        - dynamodb:PutItem
        - dynamodb:UpdateItem
        - dynamodb:DeleteItem
        - dynamodb:BatchGetItem
      Resource:
        - "Fn::GetAtt": [DynamoTable, Arn]
    - Effect: "Allow"
      Action:
        - dynamodb:GetItem
      Resource:
        - "arn:aws:dynamodb:eu-central-1:************:table/secrets"
    - Effect: "Allow"
      Action:
        - dynamodb:GetItem
        - dynamodb:BatchGetItem
      Resource:
        - "arn:aws:dynamodb:eu-central-1:************:table/prod-coins-table"
    - Effect: "Allow"
      Action:
        - dynamodb:GetItem
        - dynamodb:BatchGetItem
      Resource:
        - "arn:aws:dynamodb:eu-central-1:************:table/auth"
    - Effect: Allow # Lambda logs on cloudwatch
      Action:
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
      Resource:
        - "Fn::Join":
            - ":"
            - - "arn:aws:logs"
              - Ref: "AWS::Region"
              - Ref: "AWS::AccountId"
              - "log-group:/aws/lambda/*:*:*"
    # For warm-up functions
    - Effect: "Allow"
      Action:
        - "lambda:InvokeFunction"
      Resource: "*"
    - Effect: "Allow"
      Action:
        - "s3:ListBucket"
        - "s3:*Object*"
      Resource: "*"
  environment:
    ETHEREUM_RPC: ${file(./env.js):ETHEREUM_RPC}
    BSC_RPC: ${file(./env.js):BSC_RPC}
    POLYGON_RPC: ${file(./env.js):POLYGON_RPC}
    FANTOM_RPC: ${file(./env.js):FANTOM_RPC}
    ARBITRUM_RPC: ${file(./env.js):ARBITRUM_RPC}
    OPTIMISM_RPC: ${file(./env.js):OPTIMISM_RPC}
    XDAI_RPC: ${file(./env.js):XDAI_RPC}
    HARMONY_RPC: ${file(./env.js):HARMONY_RPC}
    SOLANA_RPC: ${file(./env.js):SOLANA_RPC}
    MOONRIVER_RPC: ${file(./env.js):MOONRIVER_RPC}
    CRONOS_RPC: ${file(./env.js):CRONOS_RPC}
    OUTDATED_WEBHOOK: ${file(./env.js):OUTDATED_WEBHOOK}
    SPIKE_WEBHOOK: ${file(./env.js):SPIKE_WEBHOOK}
    #CLOUDWATCH_WEBHOOK_URL: ${file(./env.js):CLOUDWATCH_WEBHOOK_URL}
    DROPS_WEBHOOK: ${file(./env.js):DROPS_WEBHOOK}
    #EULER_MONGODB_APIKEY: ${file(./env.js):EULER_MONGODB_APIKEY}
    #FATHOM_SECRET: ${file(./env.js):FATHOM_SECRET}
    #DAILY_GROWTH_WEBHOOK: ${file(./env.js):DAILY_GROWTH_WEBHOOK}
    STALE_COINS_ADAPTERS_WEBHOOK: ${file(./env.js):STALE_COINS_ADAPTERS_WEBHOOK}
    DEFILLAMA_SDK_MUTED: true
    #PROD_VYBE_API_KEY: ${file(./env.js):PROD_VYBE_API_KEY}
    #INDEXER_DB: ${file(./env.js):INDEXER_DB}
    AGGREGATOR_DB_URL: ${file(./env.js):AGGREGATOR_DB_URL}
    tableName: ${self:custom.tableName}
    stage: ${self:custom.stage}
    R2_ACCESS_KEY_ID: ${file(./env.js):R2_ACCESS_KEY_ID}
    R2_SECRET_ACCESS_KEY: ${file(./env.js):R2_SECRET_ACCESS_KEY}
    R2_ENDPOINT: ${file(./env.js):R2_ENDPOINT}
    INDEXA_DB: ${file(./env.js):INDEXA_DB}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
    APIKEY: ${file(./env.js):APIKEY}

custom:
  stage: ${opt:stage, self:provider.stage}
  esbuild:
    bundle: true
    minify: false
    concurrency: 4
    exclude:
      - pg-native
      - pg-hstore
    # for debugging
    #keepOutputDirectory: true
  prune:
    automatic: true
    number: 5 # Number of versions to keep
  tableName: prod-table
  domainMap:
    prod:
      domain: api.llama.fi
      certificateArn: "arn:aws:acm:us-east-1:************:certificate/b4209013-30a6-417e-847e-6e630c3e77fe"
      hostedZone: llama.fi
    dev:
      domain: staging-api.llama.fi
      certificateArn: "arn:aws:acm:us-east-1:************:certificate/b4209013-30a6-417e-847e-6e630c3e77fe"
      hostedZone: llama.fi
  domain: ${self:custom.domainMap.${self:custom.stage}.domain}
  certificateArn: ${self:custom.domainMap.${self:custom.stage}.certificateArn}
  hostedZone: ${self:custom.domainMap.${self:custom.stage}.hostedZone}
  logRetentionInDays: 30

functions:
  fallback:
    handler: src/fallback.default
    events:
      - http:
          path: /{params+}
          method: any
  # chart:  # moved to api2
  #   handler: src/getChart.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: charts
  #         method: get
  #     - http:
  #         path: charts/{chain}
  #         method: get
  # defaultChart:  # moved to api2
  #   handler: src/getDefaultChart.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: /v2/historicalChainTvl
  #         method: get
  #     - http:
  #         path: /v2/historicalChainTvl/{chain}
  #         method: get
  reportError:
    handler: src/reportError.default
    timeout: 30
    events:
      - http:
          path: reportError
          method: post
    environment:
      ERROR_REPORTS_DB: ${env:ERROR_REPORTS_DB}
      ERROR_REPORTS_WEBHOOK: ${env:ERROR_REPORTS_WEBHOOK}
      FRONT_API_TOKEN: ${env:FRONT_API_TOKEN}
  storeAggregatorEvent:
    handler: src/storeAggregatorEvent.default
    timeout: 30
    events:
      - http:
          path: storeAggregatorEvent
          method: post
  getSwapDailyVolume:
    handler: src/getSwapDailyVolume.default
    timeout: 30
    events:
      - http:
          path: getSwapDailyVolume
          method: get
  getSwapTotalVolume:
    handler: src/getSwapTotalVolume.default
    timeout: 30
    events:
      - http:
          path: getSwapTotalVolume
          method: get
  getSwapsHistory:
    handler: src/getSwapsHistory.default
    timeout: 30
    events:
      - http:
          path: getSwapsHistory
          method: get
  getLatestSwap:
    handler: src/getLatestSwap.default
    timeout: 30
    events:
      - http:
          path: getLatestSwap
          method: get
  getBlackListedTokens:
    handler: src/getBlackListedTokens.default
    timeout: 30
    events:
      - http:
          path: getBlackListedTokens
          method: get
  storeBlacklistPermit:
    handler: src/storeBlacklistPermit.default
    timeout: 30
    events:
      - http:
          path: storeBlacklistPermit
          method: post
  historicalLiquidity:
    handler: src/getHistoricalLiquidity.default
    timeout: 30
    events:
      - http:
          path: historicalLiquidity/{token}
          method: get
  protocolsLiquidity:
    handler: src/storeProtocolsLiquidity.default
    memorySize: 1000
    timeout: 900
    events:
      - schedule: cron(18 * * * ? *)
    environment:
      CG_KEY: ${env:CG_KEY}
  contractName:
    handler: src/getContractName.default
    timeout: 30
    events:
      - http:
          path: contractName2/{chain}/{contract}
          method: get
  # protocol:  # moved to api2
  #   handler: src/getProtocol.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: protocol/{protocol}
  #         method: get
  tokenProtocols:
    handler: src/getTokenInProtocols.default
    timeout: 30
    memorySize: 2000
    events:
      - http:
          path: tokenProtocols/{symbol}
          method: get
  # treasury:  # moved to api2
  #   handler: src/getTreasury.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: treasury/{protocol}
  #         method: get
  # updatedProtocol:  # moved to api2
  #   handler: src/getUpdatedProtocol.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: updatedProtocol/{protocol}
  #         method: get
  # updatedProtocolDraft:  # moved to api2
  #   handler: src/getUpdatedProtocol.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: updatedProtocolDraft/{protocol}
  #         method: get
  # debugProtocol:  # moved to api2
  #   handler: src/getUpdatedProtocol.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: debugProtocol/{protocol}
  #         method: get
  # protocolTvl:  # moved to api2
  #   handler: src/getProtocolTvl.default
  #   events:
  #     - http:
  #         path: tvl/{protocol}
  #         method: get
  # smolConfig:  # moved to api2
  #   handler: src/getSmolConfig.default
  #   events:
  #     - http:
  #         path: config/smol/{protocol}
  #         method: get
  coins:
    handler: src/getCoins.default
    timeout: 30
    memorySize: 2000
    events:
      - http:
          path: coins
          method: post
  # chains:  # moved to api2
  #   handler: src/getChains.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: chains
  #         method: get
  #     - http:
  #         path: v2/chains
  #         method: get
  # chains2:  # moved to api2
  #   handler: src/getFormattedChains.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: chains2/{category}
  #         method: get
  inflows:
    handler: src/getInflows.default
    events:
      - http:
          path: inflows/{protocol}/{timestamp}
          method: get
  # langs:  # moved to api2
  #   handler: src/storeLangs.default
  #   timeout: 60
  #   memorySize: 2000
  #   events:
  #     - schedule: cron(30 0,12 * * ? *)
  # raises:  # moved to api2
  #   handler: src/getRaises.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: raises
  #         method: get
  #   environment:
  #     AIRTABLE_API_KEY: ${file(./env.js):AIRTABLE_API_KEY}
  # hacks:  # moved to api2
  #   handler: src/getHacks.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: hacks
  #         method: get
  #   environment:
  #     AIRTABLE_API_KEY: ${file(./env.js):AIRTABLE_API_KEY}
  # oracles:  # moved to api2
  #   handler: src/getOracles.default
  #   timeout: 120
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: oracles
  #         method: get
  # forks:  # moved to api2
  #   handler: src/getForks.default
  #   timeout: 120
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: forks
  #         method: get
  # categories:  # moved to api2
  #   handler: src/getCategories.default
  #   timeout: 30
  #   memorySize: 2000
  #   events:
  #     - http:
  #         path: categories
  #         method: get
  storeLiquidations:
    handler: src/storeLiquidations.default
    timeout: 900
    memorySize: 4500
    events:
      # update latest every 20 minutes
      - schedule: cron(0/20 * * * ? *)
  # fetchLiquidations:
  #   handler: src/fetchLiquidations.default
  #   timeout: 900
  #   memorySize: 1000
  # triggerFetchLiquidations:
  #   handler: src/triggerFetchLiquidations.default
  #   events:
  #     - schedule: cron(40 * * * ? *)
  # dataset:  # moved to api2
  #   handler: src/getDataset.default
  #   timeout: 30
  #   memorySize: 10240
  #   events:
  #     - http:
  #         path: dataset/{protocol}
  #         method: get
  # simpleDataset:  # moved to api2
  #   handler: src/getSimpleChainDataset.default
  #   timeout: 30
  #   memorySize: 3000
  #   events:
  #     - http:
  #         path: simpleChainDataset/{chain}
  #         method: get
  # config:  # moved to api2
  #   handler: src/getConfig.default
  #   timeout: 10
  #   events:
  #     - http:
  #         path: config
  #         method: get
  storeSortedTokenlist:
    handler: src/storeSortedTokenlist.default
    timeout: 60
    events:
      - schedule: cron(43 * * * ? *)
    environment:
      CG_KEY: ${env:CG_KEY}
  storeFullTokenlist:
    handler: src/storeFullTokenlist.default
    timeout: 60
    events:
      - schedule: cron(30 0,12 * * ? *)
    environment:
      tableName: prod-coins-table
  hourly:
    handler: src/getHourlyData.default
    timeout: 30
    memorySize: 2000
    events:
      - http:
          path: hourly/{protocol}
          method: get
  corsPreflight:
    handler: src/corsPreflight.default
    events:
      - http:
          path: /{params+}
          method: options
  cacheExternalResponse:
    handler: src/externalEndpointsCache/index.cacheExternalResponseHandler
    timeout: 180
  cachedExternalResponse: # used in defillama-app repo for cex volume
    handler: src/externalEndpointsCache/index.default
    timeout: 30
    memorySize: 2000
    events:
      - http:
          path: cachedExternalResponse
          method: get
  # storeDataset:
  #   handler: src/storeCsvDataset.default
  #   timeout: 900
  #   memorySize: 10240
  #   events:
  #     - schedule: rate(1 day)
  notifyStaleCoins:
    handler: src/notifyStaleCoins.default
    timeout: 900
    memorySize: 1024
    events:
      - schedule: cron(15 * * * ? *)
    environment:
      TEAM_WEBHOOK: ${file(./env.js):TEAM_WEBHOOK}
      STALE_COINS_ADAPTERS_WEBHOOK: ${file(./env.js):STALE_COINS_ADAPTERS_WEBHOOK}
  sendDiscordMessage:
    handler: src/sendDiscordMessage.default
    timeout: 900
  growthReport:
    handler: src/growthReport.daily
    events:
      - schedule: cron(01 0 * * ? *)
  tokenlist:
    handler: src/storeTokenList.default
    timeout: 900
    memorySize: 512
    events:
      - schedule: cron(20 * * * ? *)
  #updateSearch:
  #  handler: src/updateSearch.default
  #  timeout: 900
  #  memorySize: 512
  #  events:
  #    - schedule: cron(30 * * * ? *)
  #  environment:
  #    SEARCH_MASTER_KEY: ${env:SEARCH_MASTER_KEY}
  emissions:
    handler: src/getEmissions.default
    timeout: 30
    events:
      - http:
          path: emissions
          method: get
  emission:
    handler: src/getEmissionProtocol.default
    timeout: 30
    events:
      - http:
          path: emission/{protocol}
          method: get
  emissionsList:
    handler: src/getEmissionsList.default
    timeout: 30
    events:
      - http:
          path: emissionsList
          method: get
  emissionsBreakdown:
    handler: src/getEmissionsBreakdown.default
    timeout: 30
    events:
      - http:
          path: emissionsBreakdown
          method: get
  storeActiveUsers:
    handler: src/storeActiveUsers.default
    timeout: 900
    memorySize: 1000
    events:
      - schedule: cron(0 * * * ? *)
    environment:
      ACCOUNTS_DB: ${env:ACCOUNTS_DB}
      ALLIUM_API_KEY: ${env:ALLIUM_API_KEY}
  storeActiveUsersRetrieve:
    handler: src/storeActiveUsersRetrieve.default
    timeout: 900
    memorySize: 1000
    events:
      - schedule: cron(40 * * * ? *)
    environment:
      ACCOUNTS_DB: ${env:ACCOUNTS_DB}
      ALLIUM_API_KEY: ${env:ALLIUM_API_KEY}
  activeUsers:
    handler: src/getActiveUsers.default
    timeout: 90
    environment:
      ACCOUNTS_DB: ${env:ACCOUNTS_DB}
    events:
      - http:
          path: activeUsers
          method: get
  protocolActiveUsers:
    handler: src/getProtocolUsers.default
    timeout: 90
    environment:
      ACCOUNTS_DB: ${env:ACCOUNTS_DB}
    events:
      - http:
          path: userData/{type}/{protocolId}
          method: get
  totalProtocolUsers:
    handler: src/getTotalProtocolUsersData.default
    timeout: 90
    environment:
      ACCOUNTS_DB: ${env:ACCOUNTS_DB}
    events:
      - http:
          path: totalProtocolUsers
          method: get
  storeChainNftVolume:
    handler: src/storeChainNftVolume.default
    timeout: 900
    memorySize: 1000
    events:
      - schedule: cron(30 * * * ? *)
    environment:
      ALLIUM_API_KEY: ${env:ALLIUM_API_KEY}
      FLIPSIDE_API_KEY: ${env:FLIPSIDE_API_KEY}
  newsArticles:
    handler: src/getNewsArticles.default
    timeout: 30
    events:
      - http:
          path: news/articles
          method: get
    environment:
      DL_NEWS_API: ${file(./env.js):DL_NEWS_API}
      DL_NEWS_ACCESS_TOKEN: ${file(./env.js):DL_NEWS_ACCESS_TOKEN}
  chainAssets:
    handler: src/getChainAssets.default
    timeout: 30
    memorySize: 1000
    events:
      - http:
          path: chain-assets/chains
          method: get
  chainAssetsChart: 
    handler: src/getChainAssetsChart.default
    timeout: 30
    memorySize: 1000
    events:
      - http:
          path: chain-assets/chart/{chain}
          method: get
  chainAssetFlows: 
    handler: src/getChainAssetFlows.default
    timeout: 30
    memorySize: 1000
    events:
      - http:
          path: chain-assets/flows/{period}
          method: get
  chainAssetHistoricalFlows: 
    handler: src/getChainAssetHistoricalFlows.default
    timeout: 30
    memorySize: 1000
    events:
      - http:
          path: chain-assets/historical-flows/{chain}/{period}
          method: get
  # depositedContracts: 
  #   handler: src/getDepositedContracts.default
  #   timeout: 30
  #   memorySize: 1000
  #   events:
  #     - http:
  #         path: deposited-contracts/{addresses}/{threshold}
  #         method: get

resources:
  # CORS for api gateway errors
  - ${file(resources/api-gateway-errors.yml)}
  # DynamoDB
  - ${file(resources/dynamodb-table.yml)}
  # Cloudfront API distribution
  - ${file(resources/api-cloudfront-distribution.yml)}

plugins:
  - serverless-esbuild
  - serverless-offline
  - serverless-prune-plugin
  - serverless-plugin-log-retention
  - serverless-plugin-lambda-insights
