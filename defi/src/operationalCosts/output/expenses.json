[{"protocolId": "182", "sources": ["https://research.lido.fi/t/lido-dao-core-contributors-provisional-budget/2556"], "headcount": 83, "annualUsdCost": {"OpEx": 8447800, "salaries": 8366560}, "annualTokenCosts": {"coingecko:lido-dao": {"salaries": 7692199}}, "notes": ["Budget set in July 2022"], "lastUpdate": "2023-04-29"}, {"protocolId": "118", "sources": ["https://expenses.makerdao.network/", "https://expenses.makerdao.network/recognized-delegates/finances/reports?viewMonth=Feb2023"], "headcount": 97, "annualUsdCost": {"others": 7908768, "salaries": 18544512, "delegates": 1205124}, "notes": ["Based on data from Feb 2023"], "lastUpdate": "2023-04-29"}, {"protocolId": "parent#sushi", "sources": ["https://twitter.com/jaredgrey/status/1602701300520652800"], "headcount": 15, "annualUsdCost": {"freelancers": 294000, "subscriptions": 475204, "salaries": 4298000, "legal": 150000}, "notes": ["Budget for 2023"], "lastUpdate": "2023-04-29"}, {"protocolId": "340", "sources": ["https://lookerstudio.google.com/u/0/reporting/cb74b814-7ce1-4449-88e5-eac57637b934/page/ZK0YC"], "headcount": 8, "annualUsdCost": {"subscriptions": 19384, "salaries": 580392, "legal": 69593, "hypernative": 60000, "audits": 91000}, "notes": ["Using FY2024 data from Looker Studio"], "lastUpdate": "2025-05-20"}, {"protocolId": "326", "sources": ["https://vote.beefy.finance/#/proposal/0x2a7500179f484c265bc027b664b8bffb67132583541c70b89773d949e8a74cc4"], "headcount": 20, "annualUsdCost": {"salaries": 1572000, "others": 960880}, "lastUpdate": "2023-04-29"}, {"protocolId": "113", "sources": ["https://llamapay.io/yearn"], "headcount": 25, "annualUsdCost": {"salaries": 3810420}, "annualTokenCosts": {"coingecko:yearn-finance": {"salaries": 316.52628}}, "lastUpdate": "2023-04-29"}, {"protocolId": "1108", "sources": ["https://gearboxprotocol.notion.site/Monthly-Reports-********************************"], "headcount": 13, "annualUsdCost": {"risk": 132000, "salaries": 761760}, "annualTokenCosts": {"coingecko:gearbox": {"salaries": 94368012}}, "lastUpdate": "2023-04-29"}, {"protocolId": "173", "sources": ["https://lookerstudio.google.com/u/0/reporting/24f03640-4369-4687-a92f-979fe946bb8b/page/UPVxC"], "headcount": 15, "annualUsdCost": {"consulting": 546852, "contractors": 3235344, "expenses": 112116, "gas": 227580, "grants": 10954, "hosting": 11210, "marketing": 25068}, "notes": ["Using data from Feb 2023"], "lastUpdate": "2023-04-29"}, {"protocolId": "parent#inverse-finance", "headcount": 11, "sources": ["https://www.inverse.finance/transparency/dao"], "annualUsdCost": {"salaries": 1308840, "audits": 96000}, "lastUpdate": "2023-04-29"}, {"protocolId": "parent#aave", "sources": ["https://aave.tokenlogic.xyz/runway"], "annualUsdCost": {"all": 18069700}, "annualTokenCosts": {"coingecko:aave": {"Technical Spend": 4800}, "coingecko:aave-polygon-wmatic": {"Other Spend": 280000}, "coingecko:chainlink": {"Operations Spend": 4000}, "coingecko:ethereum": {"Growth Spend": 678}}, "lastUpdate": "2024-09-24"}, {"protocolId": "587", "sources": ["https://maple.finance/news/q422-treasuryreport/"], "annualUsdCost": {"salaries": 7021784, "legal": 293200, "marketing": 485848, "product": 542504, "operations": 194876}, "notes": ["Based on expenses for Q4 '22, one-off expenses such as one acquisition were ignored"], "lastUpdate": "2023-05-01"}, {"protocolId": "2618", "sources": ["https://docs.google.com/spreadsheets/d/1lWlQavTbbKcyuaES3wOV9lvfba00XiNrHybZIyhNDYA/htmlview#gid=83043161"], "annualUsdCost": {"salaries": 707000}, "lastUpdate": "2023-05-06"}, {"protocolId": "1933", "sources": ["https://gov.timelessfi.com/discussion/10011-trc-00302-fund-the-12-month-operational-cost-of-the-timeless-engineering-unit"], "annualUsdCost": {"salaries": 835000, "reimbursement": 185000, "legal": 85000, "gasCosts": 50000}, "lastUpdate": "2023-05-06"}, {"protocolId": "parent#sphere", "sources": ["https://www.sphere.finance/treasury-report"], "annualUsdCost": {"staffPayroll": 319610, "operationalCosts": 43000, "subscriptionsServices": 600, "additionalMarketing": 49795, "ylSPHEREDispersments": 134331}, "notes": ["Using data from Q3 2023"], "lastUpdate": "2023-12-19"}, {"protocolId": "parent#marinade-finance", "sources": null, "headcount": 8, "annualUsdCost": {"salaries": 820000, "operationalCosts": 14000}, "notes": ["Using data from Q1 2024"], "lastUpdate": "2024-2-16"}]