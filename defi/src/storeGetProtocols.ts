import { craftProtocolsResponse } from "./getProtocols";
import { getProtocolTvl } from "./utils/getProtocolTvl";
import parentProtocolsList from "./protocols/parentProtocols";
import type { IParentProtocol } from "./protocols/types";
import type { IProtocol, LiteProtocol, ProtocolTvls } from "./types";
import { replaceChainNamesForOraclesByChain } from "./utils/normalizeChain";
import { extraSections } from "./utils/normalizeChain";
import fetch from "node-fetch";
import { excludeProtocolInCharts, hiddenCategoriesFromUISet, } from "./utils/excludeProtocols";
import protocols from "./protocols/data";

export async function storeGetProtocols({
  getCoinMarkets,
  getLastHourlyRecord,
  getLastHourlyTokensUsd,
  getYesterdayTvl,
  getLastWeekTvl,
  getLastMonthTvl,
  getYesterdayTokensUsd,
  getLastWeekTokensUsd,
  getLastMonthTokensUsd,
}: any = {}) {
  const idToName: Record<string, string> = {};
  for (const p of protocols) {
    if (p.id && p.name) idToName[p.id] = p.name;
  }
  for (const parent of parentProtocolsList) {
    if (parent.id && parent.name) idToName[parent.id] = parent.name;
  }

  const response = await craftProtocolsResponse(true, undefined, {
    getCoinMarkets,
    getLastHourlyRecord,
    getLastHourlyTokensUsd,
  });

  const trimmedResponse: LiteProtocol[] = (
    await Promise.all(
      response.map(async (protocol: IProtocol) => {
        const protocolTvls: ProtocolTvls = await getProtocolTvl(protocol, true, {
          getLastHourlyRecord,
          getLastHourlyTokensUsd,
          getYesterdayTvl,
          getLastWeekTvl,
          getLastMonthTvl,
          getYesterdayTokensUsd,
          getLastWeekTokensUsd,
          getLastMonthTokensUsd,
        });
        let forkedFrom = protocol.forkedFrom;
        if (Array.isArray(protocol.forkedFromIds)) {
          forkedFrom = protocol.forkedFromIds.map((id) => idToName[id] || id);
        }
        return {
          category: protocol.category,
          ...(protocol.tags ? { tags: protocol.tags } : {}),
          chains: protocol.chains,
          oracles: protocol.oraclesBreakdown && protocol.oraclesBreakdown.length > 0
            ? protocol.oraclesBreakdown.map((x) => x.name)
            : protocol.oracles,
          oraclesByChain: replaceChainNamesForOraclesByChain(true, protocol.oraclesByChain),
          forkedFrom,
          listedAt: protocol.listedAt,
          mcap: protocol.mcap,
          name: protocol.name,
          symbol: protocol.symbol,
          logo: protocol.logo,
          url: protocol.url,
          referralUrl: protocol.referralUrl,
          tvl: protocolTvls.tvl,
          tvlPrevDay: protocolTvls.tvlPrevDay,
          tvlPrevWeek: protocolTvls.tvlPrevWeek,
          tvlPrevMonth: protocolTvls.tvlPrevMonth,
          chainTvls: protocolTvls.chainTvls,
          parentProtocol: protocol.parentProtocol,
          defillamaId: protocol.id,
          governanceID: protocol.governanceID,
          geckoId: protocol.gecko_id,
          ...(protocol.deprecated ? {deprecated: protocol.deprecated} : {})
        };
      })
    )
  ).filter((p) => !hiddenCategoriesFromUISet.has(p.category ?? ""));

  const chains = {} as { [chain: string]: number };
  const protocolCategoriesSet: Set<string> = new Set();

  trimmedResponse.forEach((p) => {
    if (!p.category) return;

    protocolCategoriesSet.add(p.category);
    if (!excludeProtocolInCharts(p.category)) {
      p.chains.forEach((c: string) => {
        chains[c] = (chains[c] ?? 0) + (p.chainTvls[c]?.tvl ?? 0);

        if (p.chainTvls[`${c}-liquidstaking`]) {
          chains[c] = (chains[c] ?? 0) - (p.chainTvls[`${c}-liquidstaking`]?.tvl ?? 0);
        }

        if (p.chainTvls[`${c}-doublecounted`]) {
          chains[c] = (chains[c] ?? 0) - (p.chainTvls[`${c}-doublecounted`]?.tvl ?? 0);
        }

        if (p.chainTvls[`${c}-dcAndLsOverlap`]) {
          chains[c] = (chains[c] ?? 0) + (p.chainTvls[`${c}-dcAndLsOverlap`]?.tvl ?? 0);
        }
      });
    }
  });

  const getParentCoinMarkets = () =>
    fetch("https://coins.llama.fi/mcaps", {
      method: "POST",
      body: JSON.stringify({
        coins: parentProtocolsList
          .filter((parent) => typeof parent.gecko_id === "string")
          .map((parent) => `coingecko:${parent.gecko_id}`),
      }),
    }).then((r) => r.json());

  const _getCoinMarkets = getCoinMarkets ?? getParentCoinMarkets;
  const coinMarkets = await _getCoinMarkets();

  const extendedParentProtocols = [] as any[];
  const parentProtocols: IParentProtocol[] = parentProtocolsList.map((parent) => {
    const chains: Set<string> = new Set();

    const children = response.filter((protocol) => protocol.parentProtocol === parent.id);
    let symbol = "-",
      tvl = 0,
      chainTvls = {} as { [chain: string]: number };
    children.forEach((child) => {
      if (child.symbol !== "-") {
        symbol = child.symbol;
      }
      tvl += child.tvl ?? 0;
      Object.entries(child.chainTvls).forEach(([chain, chainTvl]) => {
        chainTvls[chain] = (chainTvls[chain] ?? 0) + chainTvl;
      });
      child.chains?.forEach((chain: string) => chains.add(chain));
    });

    const mcap = parent.gecko_id ? coinMarkets?.[`coingecko:${parent.gecko_id}`]?.mcap ?? null : null;
    extendedParentProtocols.push({
      id: parent.id,
      name: parent.name,
      symbol,
      //category,
      tvl,
      chainTvls,
      mcap,
      gecko_id: parent.gecko_id,
      isParent: true,
    });
    return {
      ...parent,
      chains: Array.from(chains),
      mcap,
    };
  });

  const protocols2Data = {
    protocols: trimmedResponse,
    chains: Object.entries(chains)
      .sort((a, b) => b[1] - a[1])
      .map((c) => c[0]),
    protocolCategories: [...protocolCategoriesSet].filter((category) => category),
    parentProtocols,
  };

  const v2ProtocolData = response
    .filter((p) => !hiddenCategoriesFromUISet.has(p.category ?? ""))
    .map((protocol) => ({
      id: protocol.id,
      name: protocol.name,
      symbol: protocol.symbol,
      category: protocol.category,
      ...(protocol.tags ? { tags: protocol.tags } : {}),
      tvl: protocol.tvl,
      chainTvls: Object.fromEntries(
        Object.entries(protocol.chainTvls).filter((c) => !c[0].includes("-") && !extraSections.includes(c[0]))
      ),
      mcap: protocol.mcap,
      gecko_id: protocol.gecko_id,
      parent: protocol.parentProtocol,
      ...(protocol.deprecated ? { deprecated: true } : {})
    }))
    .concat(extendedParentProtocols);

  return { protocols2Data, v2ProtocolData };
}
