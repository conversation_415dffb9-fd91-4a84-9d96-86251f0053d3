// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Protocol adaptor list is complete AGGREGATORS 1`] = `
Array [
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/vantagepointreports/releases/blob/main/VPQ-20220418%20-%20Deflex%20-%20Smart%20Contract%20Audit%20of%20Deflex%20Protocol_Public_V1.1.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Algorand",
    "chains": Array [
      "algorand",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2420",
    },
    "description": "Deflex is an exchange infrastructure for best-in-class DeFi trading, leveraging Algorand's renowned efficiency by optimizing and combining liquidity of existing DEXs.",
    "disabled": false,
    "displayName": "Deflex",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2420",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/deflex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/aggregators/deflex",
    "module": "deflex",
    "name": "Deflex",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "deflexfi",
    "url": "https://deflex.fi",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/BUIDLHub/dexible-contracts/blob/master/Audit%20Report%20-%20Dexible%20%5B16%20August%202021%5D.pdf",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "avax",
      "bsc",
      "fantom",
      "optimism",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2249",
      "protocolsData": Object {
        "Dexible_v2": Object {
          "displayName": "Dexible V2",
          "enabled": true,
          "id": "2249",
        },
      },
      "startFrom": 1630022400,
    },
    "description": " DeFi's execution management system. Dexible is a DEX aggregator that also provides algo and automated order types normally only seen in CeFi.",
    "disabled": false,
    "displayName": "Dexible V2",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2249",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dexible.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/aggregators/dexible",
    "module": "dexible",
    "name": "Dexible V2",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "DexibleApp",
    "url": "https://dexible.io",
    "versionKey": "Dexible_v2",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2141",
    },
    "description": "The best swap aggregator & infrastructure for Solana -  powering best price, token selection and UX for all users and devs",
    "disabled": false,
    "displayName": "Jupiter Aggregator",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2141",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/jupiter-aggregator.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/aggregators/jupiter-aggregator",
    "module": "jupiter-aggregator",
    "name": "Jupiter Aggregator",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "JupiterExchange",
    "url": "https://jup.ag/",
  },
]
`;
