import aggregators_imports from "../../../utils/imports/aggregators_adapters"
import { AdaptorRecordType, AdaptorRecordTypeMapReverse } from "../types";

// TODO: needs to be optimized. Currently loads to memory all adaptors
export const importModule = (module: string) => aggregators_imports[module].module

// KEYS USED TO MAP ATTRIBUTE -> KEY IN DYNAMO
export const KEYS_TO_STORE = {
    [AdaptorRecordType.dailyVolume]: AdaptorRecordTypeMapReverse[AdaptorRecordType.dailyVolume],
    [AdaptorRecordType.totalVolume]: AdaptorRecordTypeMapReverse[AdaptorRecordType.totalVolume]
}

export { default as config } from "./config";

export { default as imports } from "../../../utils/imports/aggregators_adapters"

 