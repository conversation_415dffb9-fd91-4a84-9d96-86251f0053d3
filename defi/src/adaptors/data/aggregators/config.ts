import { AdaptorsConfig } from "../types";

export default {
  "jupiter-aggregator": {
    id: "2141",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1741564800": true,
      },
    },
  },
  "dexible": {
    startFrom: 1630022400,
    id: "2249",
    displayName: "Dexible V2",
  },
  "deflex": {
    id: "2420",
  },
  // "dforce": {
  //   id: "123",
  // },
  "plexus": {
    id: "2740",
    cleanRecordsConfig: {
      genuineSpikes: {
        1706313600: false
      }
    }
  },
  "avnu": {
    id: "3154",
  },
  // "bitkeep": {
  //   id: "3207", // duplicate with bitgetwallet
  // },
  "jumper-exchange": {
    id: "3524",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1698883200": true,
      },
    },
  },
  "slingshot": {
    id: "3681",
  },
  "caviarnine-agg": {
    id: "5064",
  },
  "aggre": {
    id: "3809",
  },
  // "llamaswap": { // duplicate with volume with other aggregators
  //   id: "3847",
  // },
  "openocean": {
    id: "533",
  },
  "arcane-dex": {
    id: "3885",
  },
  "1inch-agg": {
    id: "189",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1747699200": true,
      },
    },
  },
  "zrx": {
    id: "4628",
    cleanRecordsConfig: {
      genuineSpikes: {
        1674172800: true,
        1680739200: true,
        1678492800: true,
      }
    }
  },
  "cowswap": {
    id: "2643",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1722816000": true,
      },
    },
  },
  "kyberswap": {
    id: "3982",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1704153600": true,
        "1704067200": true,
      },
    },
  },
  "yield-yak": {
    id: "475",
  },
  "bebop": {
    id: "3927",
  },
  "dodo-agg": {
    id: "5062",
  },
  "paraswap": {
    id: "894",
  },
  "tokenlon-agg": {
    id: "5063",
  },
  "aftermath-aggregator": {
    id: "3981",
  },
  "dexhunter": {
    id: "3979",
  },
  "conveyor": {
    id: "3980",
    cleanRecordsConfig: {
      genuineSpikes: {
        1722729600: false
      }
    }
  },
  // "unidex": {
  //   "id": "1833",
  //   }
  // },
  "swapgpt": {
    id: "4008",
  },
  "kanalabs": {
    id: "4016",
  },
  "odos": {
    "id": "3951",
    "cleanRecordsConfig": {
      "genuineSpikes": {
        "1708128000": true,
        "1708214400": true,
        "1708300800": true,
        "1708387200": true
      }
    }
  },
  "wowmax": {
    "id": "4192"
  },
  "opt-agg": {
    "id": "4277"
  },
  "fibrous-finance": {
    "id": "4278"
  },
  "aperture-swap": {
    "id": "3554"
  },
  "magpie": {
    "id": "4457"
  },
  "etaswap": {
    "id": "4475"
  },
  "bountive": {
    "id": "4516"
  },
  "rubic": {
    "id": "1282"
  },
  "eisen": {
    "id": "4691"
  },
  "udex-agg": {
    "id": "4704"
  },
  "injex": {
    "id": "4762"
  },
  "hop-aggregator": {
    id: "4791",
  },
  "hallswap": {
    id: "4824",
    cleanRecordsConfig: {
      genuineSpikes: {
        1724457600: true
      }
    }
  },
  "flowx-aggregator": {
    id: "4825",
  },
  "sushiswap-agg": {
    id: "5061"
  },
  "7k-aggregator": {
    id: "4868",
  },
  "akka": {
    id: "4926",
  },
  "cetus-aggregator": {
    "id": "4958",
    cleanRecordsConfig: {
      genuineSpikes: {
        1724803200: true
      }
    }
  },
  "chainspot": {
    id: "5028",
  },
  "scallop": {
    id: "5087",
  },
  "lumia": {
    id: "5098",
  },
  "rainbow-swap": {
    id: "5110",
  },
  "wolfswap": {
    id: "5138",
  },
  "swap-coffee": {
    id: "5150",
  },
  "tondiamonds": {
    id: "5161",
  },
  "okx": {
    id: "5201",
    cleanRecordsConfig: {
      genuineSpikes: {
        1746403200: true
      }
    }
  },
  "jeton": {
    id: "5213",
  },
  "hinkal": {
    id: "4487"
  },
  "unizen": {
    id: "1336",
  },
  "navi": {
    id: "5326",
  },
  "moki": {
    id: "5356",
  },
  "titan": {
    id: "5394",
  },
  "bitgetwallet": {
    id: "3207",
  },
  "thetis-market": {
    id: "5483"
  },
  "swing": {
    id: "5474"
  },
  "anqa": {
    id: "5621"
  },
  "joe-agg": {
    id: "5618"
  },
  "ooia": {
    id: "5630"
  },
  "symphony": {
    id: "5637"
  },
  "starbase": {
    id: "5661"
  },
  "superswap": {
    id: "5668",
  },
  "enso": {
    id: "5741",
    cleanRecordsConfig: {
      genuineSpikes: {
        "1738540800": true,
        "1738627200": true,
        "1738713600": true,
        "1746489600": true
      },
    },
  },
  "1delta": {
    id: "5740",
  },
  "defiapp": {
    id: "5819",
  },
  "erc-burner": {
    id: "5859"
  },
  "oogabooga": {
    id: "5880"
  },
  "mosaic": {
    id: "5890"
  },
  "houdiniswap": {
    id: "3041"
  },
  "cro-ag": {
    id: "6026"
  },
  "kame-aggregator": {
    id: "6064"
  },
  "holdstation-agg": {
    id: "6178"
  },
  "bungee-dex": {
    id: "6187"
  },
  "lifi": {
    id: "6233"
  },
  "mimboku-aggregator": {
    id: "6252"
  },
  "vetrade": {
    id: "6253"
  },
  // "dzap": {  // wash trading?, re-enabled with new listing 6435
  //   id: "6259"
  // },
  "haiku": {
    id: "6266"
  },
  "bluefin7k-aggregator": {
    id: "6322"
  },
  "hyperbloom": {
    id: "6329"
  },
  "gluex-protocol": {
    id: "6334"
  },
  "liquidswap": {
    id: "6374"
  },
  "obsidian": {
    id: "6393"
  },
  "apstation": {
    id: "6416"
  },
  "dzap": {
    id: "6435"
  },
  "rango": {
    id: "6382"
  },
  "metamask": {
    id: "3031"
  },
  "opensea": {
    id: "2258"
  },
  "orbiter-finance": {
    id: "6520"
  },
  "hyperflow": {
    id: "6538"
  },
  "nordstern-finance": {
    id: "6552"
  },
  "superboring": {
    id: "6606"
  },
  "dedust": {
    id: "2617"
},
} as AdaptorsConfig;
