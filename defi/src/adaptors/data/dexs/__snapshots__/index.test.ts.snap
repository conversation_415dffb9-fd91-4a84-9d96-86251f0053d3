// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Protocol adaptor list is complete DEXS 1`] = `
Array [
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.0x.org/developer-resources/audits",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2116",
      "protocolsData": Object {
        "0x RFQ": Object {
          "displayName": "0x - RFQ",
          "enabled": true,
          "id": "2116",
        },
      },
    },
    "description": "Open-source, decentralized exchange infrastructure that enables the exchange of tokenized assets on multiple blockchains.",
    "disabled": false,
    "displayName": "0x - RFQ",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:0xgov.eth",
    ],
    "id": "2116",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/0x.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/0x",
    "module": "0x",
    "name": "0x",
    "protocolType": undefined,
    "symbol": "ZRX",
    "twitter": "0xProject",
    "url": "https://www.0x.org/",
    "versionKey": "0x RFQ",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Starknet",
    "chains": Array [
      "starknet",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2345",
    },
    "description": "An AMM that advances with Ethereum, deployed on StarkNet Mainnet",
    "disabled": false,
    "displayName": "10KSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2345",
    "language": "Cairo",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/10kswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/10kswap",
    "module": "10kswap",
    "name": "10KSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "10KSwap",
    "url": "https://10kswap.com/",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://**********-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FdfX5W2ttoTF0UsavgZhT%2Fuploads%2FNiALqEGB39ep1N0BtMU2%2Fomniscia-report_1667586376.pdf?alt=media&token=71a2e19a-cbda-4cff-8625-761b2fcbea46",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "22611",
    "config": Object {
      "enabled": true,
      "id": "2283",
    },
    "description": "The 3xcalibur Protocol is a permissionless, liquidationless, oracleless liquidity marketplace, powered by Tri-AMM architecture to facilitate stableswaps, variable swaps and borrowing/lending.",
    "disabled": false,
    "displayName": "3xcalibur",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "3xcalibur",
    "id": "2283",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/3xcalibur.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/3xcalibur",
    "module": "3xcalibur",
    "name": "3xcalibur",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "XCAL",
    "twitter": "3xcalibur69",
    "url": "https://3xcalibur.com",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://docs.pando.im/docs/security/audit-reports",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Mixin",
    "chains": Array [
      "mixin",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "951",
    },
    "description": "Pando is a set of open financial protocols which includes 3 major protocols: 1.Pando Lake/4swap: a decentralized protocol for automated liquidity provision built with the Mixin Trusted Group. 2.Pando Leaf: a decentralized financial network to minting stablecoins. 3.Pando Rings: a decentralized protocol where you can lend or borrow cryptocurrencies",
    "disabled": false,
    "displayName": "4Swap",
    "enabled": true,
    "gecko_id": null,
    "id": "951",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pando.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/4swap",
    "module": "4swap",
    "name": "4Swap",
    "parentProtocol": "Pando",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "pando_im",
    "url": "https://pando.im",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2090",
    },
    "description": "Aequinox Dex is a community-driven decentralized exchange that supports multi-token and weighted pools on the Binance Smart Chain.",
    "disabled": false,
    "displayName": "Aequinox",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "aequinox",
    "id": "2090",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/aequinox.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/aequinox",
    "module": "aequinox",
    "name": "Aequinox",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "AEQ",
    "twitter": "Aequinox_Dex",
    "url": "https://www.aequinox.exchange/#/pools",
  },
  Object {
    "address": "solana:E5ndSkaB17Dm7CsD22dvcjfrYSDLCxFcMd6z8ddCk5wp",
    "audit_links": Array [
      "https://dex.aldrin.com/877f900320eec44c13409814fe473fb7.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "10935",
    "config": Object {
      "enabled": true,
      "id": "739",
    },
    "description": "Aldrin’s mission is to simplify DeFi and create powerful tools for all traders to succeed.",
    "disabled": false,
    "displayName": "Aldrin",
    "enabled": true,
    "gecko_id": "aldrin",
    "id": "739",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/aldrin.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/aldrin",
    "module": "aldrin",
    "name": "Aldrin",
    "openSource": false,
    "protocolType": undefined,
    "symbol": "RIN",
    "twitter": "Aldrin_Exchange",
    "url": "https://aldrin.com",
  },
  Object {
    "address": "stacks:SP3K8BC0PPEVCV7NZ6QSRWPQ2JE9E5B6N3PA0KBR9",
    "audit_links": Array [
      "https://www.alexgo.io/#Security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Stacks",
    "chains": Array [
      "stacks",
    ],
    "cmcId": "22074",
    "config": Object {
      "enabled": true,
      "id": "1466",
    },
    "description": "Next Gen DeFi on Bitcoin via Stacks. Building Financial Infrastructure on Bitcoin through Stacks",
    "disabled": false,
    "displayName": "ALEX",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "alexgo",
    "id": "1466",
    "language": "Clarity",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/alex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/alex",
    "module": "alex",
    "name": "ALEX",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ALEX",
    "twitter": "@ALEXLabBTC",
    "url": "https://alexlab.co/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/runtimeverification/publications/blob/main/reports/smart-contracts/Algofi.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Algorand",
    "chains": Array [
      "algorand",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2091",
    },
    "description": "Decentralized swap protocol built on Algorand",
    "disabled": false,
    "displayName": "Algofi Swap",
    "enabled": true,
    "gecko_id": null,
    "id": "2091",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/algofi.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/algofi",
    "module": "algofi",
    "name": "Algofi Swap",
    "oracles": Array [],
    "parentProtocol": "Algofi",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "algofiorg",
    "url": "https://www.algofi.org/",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "6082",
    "config": Object {
      "enabled": true,
      "id": "2603",
    },
    "description": "The most faster, cheaper and secure DEX on Arbitrum. Swap, earn and farm tokens with few clicks and automate your earnings",
    "disabled": false,
    "displayName": "AlienFi",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "alienfi",
    "id": "2603",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/alienfi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/alienfi",
    "module": "alienfi",
    "name": "AlienFi",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ALIEN",
    "twitter": "alienficoin",
    "url": "https://www.alien.fi",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://ampleswap.com/audit-by-etherauthority.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2383",
    },
    "description": "The best AMM DEX on Binance Smart Chain (BSC, providing friendly trading and better project support",
    "disabled": false,
    "displayName": "AmpleSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2383",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ampleswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ampleswap",
    "module": "ampleswap",
    "name": "AmpleSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "AMPLE",
    "twitter": "ampleswap",
    "url": "https://ampleswap.com",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://apeswap.gitbook.io/apeswap-finance/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "polygon",
      "ethereum",
      "telos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "398",
    },
    "description": "ApeSwap is a Decentralized Autonomous Organization (DAO) that offers a full suite of tools to explore and engage with decentralized finance opportunities. Using the products within our DeFi Hub, users and partners can tap into this new wave of financial innovation in a secure, transparent, and globally accessible way.",
    "disabled": false,
    "displayName": "ApeSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "398",
    "logo": "https://icons.llama.fi/apeswap-amm.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/apeswap",
    "module": "apeswap",
    "name": "ApeSwap AMM",
    "parentProtocol": "ApeSwap",
    "protocolType": undefined,
    "symbol": "BANANA",
    "twitter": "ape_swap",
    "url": "https://apeswap.finance",
  },
  Object {
    "address": "core:******************************************",
    "audit_links": Array [
      "https://**********-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2F6vdtb4jHLhV87BIUy2nb%2Fuploads%2FkBTuxRssUr6JyX5XJA7t%2FArcherSwap%20Protocol%20-%20Smart%20Contracts%20Security%20Audit%20Report.pdf?alt=media&token=75cae46a-3500-430e-8adc-1c2da1eaf780",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "CORE",
    "chains": Array [
      "core",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2648",
    },
    "description": "ArcherSwap is a crypto world for users to trade, earn, and game. It is the premier choice for projects on Core Chain with features including AMM, NFT, and GameFi.",
    "disabled": false,
    "displayName": "ArcherSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2648",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/archerswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/archerswap",
    "module": "archerswap",
    "name": "ArcherSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "BOW",
    "twitter": "archerswapcore",
    "url": "https://archerswap.finance",
  },
  Object {
    "address": "telos:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Telos",
    "chains": Array [
      "telos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2317",
    },
    "description": "Archly is a permissionless, low slippage, low cost protocol that incentives fees on the Telos EVM.",
    "disabled": false,
    "displayName": "Archly Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "2317",
    "listedAt": 1669728918,
    "logo": "https://icons.llama.fi/archly-finance.png",
    "methodology": Object {
      "Fees": "The trading fees are 0.05%, and can be adjusted from 0.01% up to 0.1%.",
      "HoldersRevenue": "veArc voters receive all protocol fees.",
      "ProtocolRevenue": "Treasury does not earn any revenue from trading fees.",
      "Revenue": "All trading fees are paid to veArc voters.",
      "SupplySideRevenue": "LPs do not earn any revenue from trading fees, only Arc emission decided by veArc voters.",
      "UserFees": "Currently users pay a trading fee of 0.05%.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/archly-finance",
    "module": "archly-finance",
    "name": "Archly Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ARC",
    "twitter": "ArchlyFinance",
    "url": "https://archly.fi",
  },
  Object {
    "address": "aurora:******************************************",
    "audit_links": Array [
      "https://docsend.com/view/42a2ixu3ey6zu3nx",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Aurora",
    "chains": Array [
      "aurora",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2176",
    },
    "description": "Powered by iZUMi Finance, Arctic is leading the next chapter of Aurora development as the core ecosystem-DEX, bringing DeFi innovation to Aurora in the forms of the veNFT and DL-AMM (Discretized Liquidity-AMM).",
    "disabled": false,
    "displayName": "Arctic",
    "enabled": true,
    "forkedFrom": Array [
      "iziSwap",
    ],
    "gecko_id": null,
    "id": "2176",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/arctic.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/arctic",
    "module": "arctic",
    "name": "Arctic",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ARC",
    "twitter": "Arctic_trade",
    "url": "https://arctic.trade/trade/swap",
  },
  Object {
    "address": "elrond:ASH-a642d1",
    "audit_links": Array [
      "https://docs.ashswap.io/resources/audit-report",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Elrond",
    "chains": Array [
      "elrond",
    ],
    "cmcId": "22321",
    "config": Object {
      "enabled": true,
      "id": "2551",
    },
    "description": "The very first decentralized exchange (DEX) following the stable-swap model on the MultiversX blockchain",
    "disabled": false,
    "displayName": "Ashswap",
    "enabled": true,
    "gecko_id": "ashswap",
    "id": "2551",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ashswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ashswap",
    "module": "ashswap",
    "name": "Ashswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ASH",
    "twitter": "ash_swap",
    "url": "https://app.ashswap.io/swap/",
  },
  Object {
    "address": "terra:terra1xj49zyqrwpv5k928jwfpfy2ha668nwdgkwlrg3",
    "audit_links": Array [
      "https://github.com/astroport-fi/astro-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Terra",
    "chains": Array [
      "terra",
    ],
    "cmcId": "16304",
    "config": Object {
      "disabled": true,
      "enabled": true,
      "id": "1052",
    },
    "description": "The meta AMM of Terra",
    "disabled": true,
    "displayName": "Astroport",
    "enabled": true,
    "gecko_id": "astroport",
    "id": "1052",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/astroport.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/astroport",
    "module": "astroport",
    "name": "Astroport",
    "openSource": true,
    "protocolType": undefined,
    "symbol": "ASTRO",
    "twitter": "astroport_fi",
    "url": "https://astroport.fi",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "1098",
    "config": Object {
      "enabled": true,
      "id": "1859",
    },
    "description": "AuraSwap is an AMM decentralized exchange and Yield Farming on Polygon with near-zero gas fees. At the heart of AURA is the driving force of the ecosystem.",
    "disabled": false,
    "displayName": "AuraSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "auraswap",
    "id": "1859",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/auraswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/auraswap",
    "module": "auraswap",
    "name": "AuraSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "AURA",
    "twitter": "AuraSwapDEX",
    "url": "https://auraswap.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.autoshark.finance/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10303",
    "config": Object {
      "enabled": true,
      "id": "1074",
    },
    "description": "AutoShark Finance is the 1st Hybrid AMM and Yield Optimizer, offering unparalleled access to farming opportunities through the use of superior yield strategies, auto-compounding vaults, and NFT-powered farming.",
    "disabled": false,
    "displayName": "AutoShark",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "autoshark",
    "id": "1074",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/autoshark.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/autoshark",
    "module": "autoshark",
    "name": "AutoShark",
    "protocolType": undefined,
    "symbol": "JAWS",
    "twitter": "AutoSharkFin",
    "url": "https://autoshark.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Aptos",
    "chains": Array [
      "aptos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2213",
    },
    "description": "AUX is the universal exchange. Our mission is to provide the best decentralized trading experience possible. Live on Aptos.",
    "disabled": false,
    "displayName": "AUX Exchange",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2213",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/aux-exchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/aux-exchange",
    "module": "aux-exchange",
    "name": "AUX Exchange",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "AuxExchange",
    "url": "https://aux.exchange/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "14396",
    "config": Object {
      "enabled": true,
      "id": "845",
    },
    "description": "Axial is creating the new center for liquidity on Avalanche. It is an implementation of the Stableswap mechanism and decentralized governance. As the centerpiece for liquidity of value-pegged assets, bridged and native assets will have a home and route throughout the Avalanche ecosystem. Axial was launched in November of 2021, and is one of many projects originating from the Snowball DAO.",
    "disabled": false,
    "displayName": "Axial",
    "enabled": true,
    "forkedFrom": Array [
      "Saddle Finance",
    ],
    "gecko_id": "axial-token",
    "id": "845",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/axial.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/axial",
    "module": "axial",
    "name": "Axial",
    "protocolType": undefined,
    "symbol": "AXIAL",
    "twitter": "AxialDeFi",
    "url": "https://www.axial.exchange",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/baby-doge-coin",
      "https://dessertswap.finance/audits/Baby%20Doge%20Coin%20(BabyDoge)%20BEP-20%20Audit%208332977.pdf",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10407",
    "config": Object {
      "enabled": true,
      "id": "2169",
    },
    "description": "Meme of BSC hold, pet, love, & help save dogs! BSC Farms & Swap",
    "disabled": false,
    "displayName": "BabyDogeSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "baby-doge-coin",
    "governanceID": Array [
      "snapshot:babydogevote.eth",
    ],
    "id": "2169",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/babydogeswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/babydogeswap",
    "module": "babydogeswap",
    "name": "BabyDogeSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "BabyDoge",
    "twitter": "BabyDogeCoin",
    "url": "https://babydogeswap.com",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/babyswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10334",
    "config": Object {
      "enabled": true,
      "id": "597",
    },
    "description": "BabySwap is an AMM+NFT decentralized exchange for new projects on Binance Smart Chain",
    "disabled": false,
    "displayName": "BabySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "babyswap",
    "id": "597",
    "logo": "https://icons.llama.fi/babyswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/babyswap",
    "module": "babyswap",
    "name": "BabySwap",
    "protocolType": undefined,
    "symbol": "BABY",
    "twitter": "babyswap_bsc",
    "url": "https://home.babyswap.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.balancer.fi/core-concepts/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "116",
      "protocolsData": Object {
        "v1": Object {
          "displayName": "Balancer V1",
          "enabled": true,
          "id": "116",
        },
        "v2": Object {
          "displayName": "Balancer V2",
          "enabled": true,
          "id": "2611",
        },
      },
    },
    "description": "Balancer is a protocol for programmable liquidity.


",
    "disabled": false,
    "displayName": "Balancer V1",
    "enabled": true,
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:balancer.eth",
    ],
    "id": "116",
    "logo": "https://icons.llama.fi/balancer-v1.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/balancer",
    "module": "balancer",
    "name": "Balancer V1",
    "parentProtocol": "Balancer",
    "protocolType": undefined,
    "symbol": "BAL",
    "treasury": "balancer.js",
    "twitter": "BalancerLabs",
    "url": "https://balancer.finance/",
    "versionKey": "v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.balancer.fi/core-concepts/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "polygon",
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "116",
      "protocolsData": Object {
        "v1": Object {
          "displayName": "Balancer V1",
          "enabled": true,
          "id": "116",
        },
        "v2": Object {
          "displayName": "Balancer V2",
          "enabled": true,
          "id": "2611",
        },
      },
    },
    "description": "Balancer is a protocol for programmable liquidity.",
    "disabled": false,
    "displayName": "Balancer V2",
    "enabled": true,
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:balancer.eth",
    ],
    "id": "116",
    "logo": "https://icons.llama.fi/balancer-v2.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/balancer",
    "module": "balancer",
    "name": "Balancer V2",
    "parentProtocol": "Balancer",
    "protocolType": undefined,
    "symbol": "BAL",
    "twitter": "BalancerLabs",
    "url": "https://balancer.finance/",
    "versionKey": "v2",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.bancor.network/about-bancor-network/security-and-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "162",
      "protocolsData": Object {
        "v2.1": Object {
          "enabled": true,
          "id": "162",
        },
        "v3": Object {
          "enabled": true,
          "id": "1995",
        },
      },
    },
    "description": "Bancor is an on-chain liquidity protocol that enables automated, decentralized exchange on Ethereum and across blockchains.",
    "disabled": false,
    "displayName": "Bancor V3",
    "enabled": true,
    "gecko_id": null,
    "id": "162",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/bancor.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/bancor",
    "module": "bancor",
    "name": "Bancor V3",
    "parentProtocol": "Bancor",
    "protocolType": undefined,
    "symbol": "BNT",
    "twitter": "Bancor",
    "url": "https://app.bancor.network/",
    "versionKey": "v3",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://bancor-network.gitbook.io/v2.1/ethereum-contracts/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "162",
      "protocolsData": Object {
        "v2.1": Object {
          "enabled": true,
          "id": "162",
        },
        "v3": Object {
          "enabled": true,
          "id": "1995",
        },
      },
    },
    "description": "Bancor is an on-chain liquidity protocol that enables automated, decentralized exchange on Ethereum and across blockchains.",
    "disabled": false,
    "displayName": "Bancor V2.1",
    "enabled": true,
    "gecko_id": null,
    "id": "162",
    "logo": "https://icons.llama.fi/bancor.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/bancor",
    "module": "bancor",
    "name": "Bancor V2.1",
    "parentProtocol": "Bancor",
    "protocolType": undefined,
    "symbol": "BNT",
    "twitter": "Bancor",
    "url": "https://app.bancor.network/",
    "versionKey": "v2.1",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://app.inspex.co/library/baryon-network",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1950",
    },
    "description": "Baryon Network is a BNB Chain-based Premier Suite of DeFi Products containing three fundamental building blocks: BaryonSwap (AMM), BaryonFarm, and BaryonStake.",
    "disabled": false,
    "displayName": "Baryon Network",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1950",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/baryon-network.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/baryon",
    "module": "baryon",
    "name": "Baryon Network",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "BaryonNetwork",
    "url": "https://www.baryon.network",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://docs.beamswap.io/contracts/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": "17035",
    "config": Object {
      "enabled": true,
      "id": "1289",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "1289",
        },
        "stable-amm": Object {
          "enabled": true,
          "id": "2596",
        },
      },
    },
    "description": "Defi Hub on Moonbeam",
    "disabled": false,
    "displayName": "BeamSwap Classic",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "beamswap",
    "id": "1289",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/beamswap-classic.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/beamswap",
    "module": "beamswap",
    "name": "BeamSwap Classic",
    "oracles": Array [
      "DIA",
    ],
    "protocolType": undefined,
    "symbol": "GLINT",
    "twitter": "Beamswapio",
    "url": "https://beamswap.io",
    "versionKey": "classic",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://docs.beamswap.io/contracts/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1289",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "1289",
        },
        "stable-amm": Object {
          "enabled": true,
          "id": "2596",
        },
      },
    },
    "description": "Defi Hub on Moonbeam",
    "disabled": false,
    "displayName": "BeamSwap Stable AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1289",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/beamswap-stable-amm.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/beamswap",
    "module": "beamswap",
    "name": "BeamSwap Stable AMM",
    "oracles": Array [
      "DIA",
    ],
    "parentProtocol": "BeamSwap",
    "protocolType": undefined,
    "symbol": "GLINT",
    "twitter": "Beamswapio",
    "url": "https://beamswap.io",
    "versionKey": "stable-amm",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "optimism",
    ],
    "cmcId": "13244",
    "config": Object {
      "enabled": true,
      "id": "654",
    },
    "description": "We leverage best in breed DeFi protocols to offer novel decentralized investment strategies. Built on Balancer V2, Beethoven X is the first next-generation AMM protocol on Fantom.",
    "disabled": false,
    "displayName": "Beethoven X",
    "enabled": true,
    "forkedFrom": Array [
      "Balancer",
    ],
    "gecko_id": "beethoven-x",
    "governanceID": Array [
      "snapshot:beets.eth",
    ],
    "id": "654",
    "logo": "https://icons.llama.fi/beethoven-x.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/beethoven-x",
    "module": "beethoven-x",
    "name": "Beethoven X",
    "protocolType": undefined,
    "symbol": "BEETS",
    "treasury": "beethovenx.js",
    "twitter": "beethoven_x",
    "url": "https://www.beethovenx.io",
  },
  Object {
    "address": "smartbch:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/bentoken-finance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "smartBCH",
    "chains": Array [
      "smartbch",
    ],
    "cmcId": "1638",
    "config": Object {
      "enabled": true,
      "id": "749",
    },
    "description": "BenSwap. The #1 AMM and yield farm for dog lovers on BSC and smartBCH",
    "disabled": false,
    "displayName": "BenSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "green-ben",
    "id": "749",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/benswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/benswap",
    "module": "benswap",
    "name": "BenSwap",
    "protocolType": undefined,
    "symbol": "EBEN",
    "twitter": "BenTokenFinanc1",
    "url": "https://benswap.cash",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Bitcoin",
    "chains": Array [
      "bitcoin",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2588",
    },
    "description": "Buy and sell bitcoin for fiat (or other cryptocurrencies) privately and securely using Bisq's peer-to-peer network and open-source desktop software.",
    "disabled": false,
    "displayName": "Bisq",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2588",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/bisq.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/bisq",
    "module": "bisq",
    "name": "Bisq",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "bisq_network",
    "url": "https://bisq.network/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/biswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10746",
    "config": Object {
      "enabled": true,
      "id": "373",
    },
    "description": "Biswap is a trusted DEX platform on the BNB Chain network with a Multi-type Referral Program and low trade fee starting from 0.1%. Biswap is the ecosystem that offers the best service and creates new standards in the DeFi industry",
    "disabled": false,
    "displayName": "BiSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "biswap",
    "governanceID": Array [
      "snapshot:biswap-org.eth",
    ],
    "id": "373",
    "logo": "https://icons.llama.fi/biswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/biswap",
    "module": "biswap",
    "name": "BiSwap",
    "protocolType": undefined,
    "symbol": "BSW",
    "twitter": "Biswap_Dex",
    "url": "https://biswap.org/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2158",
    },
    "description": "Green Planet is an incentivized, non-custodial lending protocol for earning interest on deposits and borrowing assets.",
    "disabled": false,
    "displayName": "Blue Planet",
    "enabled": true,
    "gecko_id": null,
    "id": "2158",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/planet-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/blue-planet",
    "module": "blue-planet",
    "name": "Blue Planet",
    "parentProtocol": "Planet",
    "protocolType": undefined,
    "symbol": "AQUA",
    "twitter": "planet_finance",
    "url": "https://app.planet.finance/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://static.bogged.finance/audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "avax",
      "fantom",
      "polygon",
      "cronos",
    ],
    "cmcId": "8723",
    "config": Object {
      "enabled": true,
      "id": "617",
    },
    "description": "Bogged. Finance is a DeFi tool suite for the Binance Smart Chain powered by the BOG token",
    "disabled": false,
    "displayName": "Bogged Finance",
    "enabled": true,
    "gecko_id": "bogged-finance",
    "id": "617",
    "logo": "https://icons.llama.fi/bogged-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/bogged-finance",
    "module": "bogged-finance",
    "name": "Bogged Finance",
    "protocolType": undefined,
    "symbol": "BOG",
    "twitter": "boggedfinance",
    "url": "https://www.bogged.finance",
  },
  Object {
    "address": "heco:******************************************",
    "audit_links": Array [
      "https://mos-wallet-public.oss-cn-hangzhou.aliyuncs.com/mos/BXH/bxh_audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Heco",
    "chains": Array [
      "bsc",
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "404",
    },
    "description": "BXH (Bitcoin Dex on HECO) is an innovative Dex trading platform developed based on HECO Chain.",
    "disabled": false,
    "displayName": "BXH",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "bxh",
    "id": "404",
    "logo": "https://icons.llama.fi/bxh.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/bxh",
    "module": "bxh",
    "name": "BXH",
    "protocolType": undefined,
    "symbol": "BXH",
    "twitter": "BXH_Blockchain",
    "url": "https://bxh.com",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://paladinsec.co/projects/camelot/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2307",
    },
    "description": "Camelot is an ecosystem-focused and community-driven DEX built on Arbitrum. It has been built as a highly efficient and customizable protocol, allowing both builders and users to leverage our custom infrastructure for deep, sustainable, and adaptable liquidity. Camelot moves beyond the traditional design of DEXs to focus on offering a tailored approach that prioritises composability",
    "disabled": false,
    "displayName": "Camelot",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2307",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/camelot.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/camelot",
    "module": "camelot",
    "name": "Camelot",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "GRAIL",
    "twitter": "CamelotDEX",
    "url": "https://camelot.exchange/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Canto",
    "chains": Array [
      "canto",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1985",
    },
    "description": "Dex on canto blockchain",
    "disabled": false,
    "displayName": "Canto Dex",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1985",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/canto-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/canto-dex",
    "module": "canto-dex",
    "name": "Canto Dex",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "CantoPublic",
    "url": "https://app.slingshot.finance/swap/CANTO",
  },
  Object {
    "address": "cube:******************************************",
    "audit_links": Array [
      "https://docs.capricorn.finance/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cube",
    "chains": Array [
      "cube",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2128",
    },
    "description": "Capricorn is an automated market maker (AMM) based decentralized exchange (DEX) for CUBE Chain.",
    "disabled": false,
    "displayName": "Capricorn Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "capricorn",
    "id": "2128",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/capricorn-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/capricorn-finance",
    "module": "capricorn-finance",
    "name": "Capricorn Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CORN",
    "twitter": "CapricornFi",
    "url": "https://www.capricorn.finance/",
  },
  Object {
    "address": "energyweb:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "EnergyWeb",
    "chains": Array [
      "energyweb",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "670",
    },
    "description": "CarbonSwap is a family of interconnected green, sustainability-focused DeFi & NFT products, including initially an automated market-making (AMM) decentralized exchange (DEX) and a bi-directional ERC-to-ERC bridge (Omnibridge) between the Ethereum Mainnet and Energy Web Chain.",
    "disabled": false,
    "displayName": "CarbonSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "670",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/carbonswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/carbonswap",
    "module": "carbonswap",
    "name": "CarbonSwap",
    "protocolType": undefined,
    "symbol": "SUSU",
    "twitter": "carbonswap_fi",
    "url": "https://carbonswap.exchange/#/swap",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Aptos",
    "chains": Array [
      "aptos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2289",
    },
    "description": "Cetus is a pioneer DEX and concentrated liquidity protocol focusing on Move-based ecosystems like Aptos and Sui. It works as a crucial part of the ecosystem infrastructure to satisfy the comprehensive needs of traders, LPs, upper applications and an increasing DeFi population.",
    "disabled": false,
    "displayName": "Cetus",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2289",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/cetus.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/cetus",
    "module": "cetus",
    "name": "Cetus",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "CetusProtocol",
    "url": "https://www.cetus.zone",
  },
  Object {
    "address": "fusion:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/chaingefinance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fusion",
    "chains": Array [
      "fusion",
    ],
    "cmcId": "9071",
    "config": Object {
      "enabled": true,
      "id": "704",
    },
    "description": "Chainge Finance stands as the most liquid cross-chain aggregated DEX with over 70bn in aggregated liquidity. The mobile app boasts over 20 integrated DEXs and 29 chains. From a cross-chain wallet and instant cross-chain roaming to decentralized earning solutions and smart escrow contracts, Chainge is an DeFi app powered and secured by the innovative Fusion DCRM tech",
    "disabled": false,
    "displayName": "Chainge Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "chainge-finance",
    "id": "704",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/chainge-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/chainge-finance",
    "module": "chainge-finance",
    "name": "Chainge Finance",
    "protocolType": undefined,
    "symbol": "CHNG",
    "twitter": "FinanceChainge",
    "url": "https://www.chainge.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1643",
    },
    "description": "The Internet Scale DeFi for the People. Trade, earn, and win crypto on the most popular decentralized platform for all people with internet access.",
    "disabled": false,
    "displayName": "ChampagneSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1643",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/champagneswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/champagneswap",
    "module": "champagneswap",
    "name": "ChampagneSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CHAM",
    "twitter": "champagnedefi",
    "url": "https://champagneswap.com",
  },
  Object {
    "address": "okex:******************************************",
    "audit_links": Array [
      "https://docs.cherryswap.net/noneag",
      "https://docs.cherryswap.net/knownsec",
      "https://docs.cherryswap.net/certik",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "OKExChain",
    "chains": Array [
      "okexchain",
    ],
    "cmcId": "9437",
    "config": Object {
      "enabled": true,
      "id": "543",
    },
    "description": "CherrySwap is the automatic market-making protocol based on OKExChain(OEC). It adopts the mechanism of Automatic Market Maker (AMM) and aims to achieve self-driven liquidity creation with diversified functions such as liquidity mining, IFO, NFT, lottery, and DAO, so as to provide participants with the maximum value bonus.",
    "disabled": false,
    "displayName": "CherrySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "cherryswap",
    "id": "543",
    "logo": "https://icons.llama.fi/cherryswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/cherryswap",
    "module": "cherryswap",
    "name": "CherrySwap",
    "protocolType": undefined,
    "symbol": "CHE",
    "twitter": "CherryswapNet",
    "url": "https://www.cherryswap.net",
  },
  Object {
    "address": "klaytn:******************************************",
    "audit_links": Array [
      "https://github.com/claimswap/claimswap-audit",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Klaytn",
    "chains": Array [
      "klaytn",
    ],
    "cmcId": "18371",
    "config": Object {
      "enabled": true,
      "id": "1455",
    },
    "description": "Swap, earn, and claim on the decentralized, community driven platform",
    "disabled": false,
    "displayName": "ClaimSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "claimswap",
    "id": "1455",
    "language": "Solidity",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/claimswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/claimswap",
    "module": "claimswap",
    "name": "ClaimSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CLA",
    "twitter": "claimswap",
    "url": "https://claimswap.org/",
  },
  Object {
    "address": "-",
    "audit_links": Array [
      "https://docs.clipper.exchange/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "optimism",
      "polygon",
      "moonbeam",
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "622",
    },
    "description": "Clipper is the decentralized exchange (DEX) built to give the self-made crypto trader the best possible prices on small trades (< $10k)",
    "disabled": false,
    "displayName": "Clipper",
    "enabled": true,
    "gecko_id": null,
    "id": "622",
    "logo": "https://icons.llama.fi/clipper.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/clipper",
    "module": "clipper",
    "name": "Clipper",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "Clipper_DEX",
    "url": "https://clipper.exchange",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "8294",
    "config": Object {
      "enabled": true,
      "id": "261",
    },
    "description": "DeFi powered space game with yield generating NFT ",
    "disabled": false,
    "displayName": "Cometh",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "must",
    "id": "261",
    "logo": "https://icons.llama.fi/cometh.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/cometh",
    "module": "cometh",
    "name": "Cometh",
    "protocolType": undefined,
    "symbol": "MUST",
    "twitter": "MUSTCometh",
    "url": "https://www.cometh.io/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "471",
    },
    "description": "Complus Network is an (AMM) decentralized crypto exchange (DEX) and yield farming protocol on Avalanche.",
    "disabled": false,
    "displayName": "Complus Network",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "complus-network",
    "id": "471",
    "logo": "https://icons.llama.fi/complus-network.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/complus-network",
    "module": "complus-network",
    "name": "Complus Network",
    "protocolType": undefined,
    "symbol": "COM",
    "twitter": "complusnetwork",
    "url": "https://complus.exchange",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1970",
    },
    "description": "Cone is a decentralized exchange that allows swaps at a low cost in swap fees and has a governance structure based on the ve(3,3) system",
    "disabled": false,
    "displayName": "Cone",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "1970",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/cone.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/cone",
    "module": "cone",
    "name": "Cone",
    "protocolType": undefined,
    "symbol": "CONE",
    "twitter": "Coneswap",
    "url": "https://cone.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1412",
    },
    "description": "Crema Finance is a powerful concentrated liquidity protocol built on Solana that provides superior performance for both traders and liquidity providers. It changes the Solana DeFi game by introducing a series of innovations to improve the overall capital efficiency and trading depth.",
    "disabled": false,
    "displayName": "Crema Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1412",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/crema-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/crema-finance",
    "module": "crema-finance",
    "name": "Crema Finance",
    "openSource": false,
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "Crema_Finance",
    "url": "https://www.crema.finance",
  },
  Object {
    "address": "cronos:******************************************",
    "audit_links": Array [
      "https://hacken.io/audits/#crodex",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cronos",
    "chains": Array [
      "cronos",
    ],
    "cmcId": "15069",
    "config": Object {
      "enabled": true,
      "id": "828",
    },
    "description": "Crodex is a decentralized exchange (DEX), providing liquidity and enabling peer-to-peer transactions on Cronos.",
    "disabled": false,
    "displayName": "Crodex",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "crodex",
    "id": "828",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/crodex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/crodex",
    "module": "crodex",
    "name": "Crodex",
    "protocolType": undefined,
    "symbol": "CRX",
    "twitter": "crodexapp",
    "url": "https://swap.crodex.app",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1750",
    },
    "description": "The hottest new AMM on BSC! Earn CST through yield farming, then stake it in Csyrup Pools to earn more tokens. Swap, stake, and earn",
    "disabled": false,
    "displayName": "CryptoSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1750",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/cryptoswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/cryptoswap",
    "module": "cryptoswap",
    "name": "CryptoSwap",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "CST",
    "twitter": "CryptoDevelopm3",
    "url": "https://cryptoswapdex.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://curve.fi/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "polygon",
      "fantom",
      "arbitrum",
      "avax",
      "optimism",
      "xdai",
    ],
    "cmcId": "6538",
    "config": Object {
      "enabled": true,
      "id": "3",
    },
    "description": "Curve is a decentralized exchange liquidity pool on Ethereum designed for extremely efficient stablecoin trading",
    "disabled": false,
    "displayName": "Curve",
    "enabled": true,
    "gecko_id": "curve-dao-token",
    "governanceID": Array [
      "snapshot:curve.eth",
    ],
    "id": "3",
    "language": "Vyper",
    "logo": "https://icons.llama.fi/curve.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/curve",
    "module": "curve",
    "name": "Curve",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CRV",
    "twitter": "CurveFinance",
    "url": "https://curve.fi",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2167",
    },
    "description": "AMM DEX on BSC",
    "disabled": false,
    "displayName": "DAO Swap",
    "enabled": true,
    "gecko_id": null,
    "id": "2167",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dao-maker.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dao-swap",
    "module": "dao-swap",
    "name": "DAO Swap",
    "parentProtocol": "DAO Maker",
    "protocolType": undefined,
    "symbol": "DAO",
    "twitter": "TheDaoMaker",
    "url": "https://swap.daomaker.com/",
  },
  Object {
    "address": "cronos:******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-DarkNess-Dollar-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cronos",
    "chains": Array [
      "cronos",
    ],
    "cmcId": "18753",
    "config": Object {
      "enabled": true,
      "id": "1555",
    },
    "description": "DarkNess Finance is the first multi-fractional-algorithmic stablecoin with stablecoins DEX on Cronos Chain",
    "disabled": false,
    "displayName": "Darkness",
    "enabled": true,
    "forkedFrom": Array [
      "Balancer",
    ],
    "gecko_id": "darkness-share",
    "id": "1555",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/darkness.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/darkness",
    "module": "darkness",
    "name": "Darkness",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "NESS",
    "twitter": "DarkCryptoFi",
    "url": "https://www.darkness.finance",
  },
  Object {
    "address": "harmony:******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/DefiKingdoms/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Harmony",
    "chains": Array [
      "harmony",
    ],
    "cmcId": "12319",
    "config": Object {
      "enabled": true,
      "id": "556",
    },
    "description": "DeFi Kingdoms is a game, a DEX, a liquidity pool opportunity, a market of rare utility driven NFTs, and it all plays out seamlessly in the incredibly nostalgic form of fantasy pixel art.",
    "disabled": false,
    "displayName": "Defi Kingdoms",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "defi-kingdoms",
    "governanceID": Array [
      "snapshot:dfkvote.eth",
    ],
    "id": "556",
    "logo": "https://icons.llama.fi/defi-kingdoms.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/defi-kingdoms",
    "module": "defi-kingdoms",
    "name": "Defi Kingdoms",
    "protocolType": undefined,
    "symbol": "JEWEL",
    "twitter": "DefiKingdoms",
    "url": "https://www.defikingdoms.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://crypto.com/defi/swap-protocol-audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1776",
    "config": Object {
      "enabled": true,
      "id": "221",
    },
    "description": "Crypto.com DeFi Swap is a fork of Uniswap V2 designed to be the best place to swap and farm DeFi coins.",
    "disabled": false,
    "displayName": "Defi Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "crypto-com-chain",
    "id": "221",
    "logo": "https://icons.llama.fi/defi-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/defi-swap",
    "module": "defi-swap",
    "name": "Defi Swap",
    "protocolType": undefined,
    "symbol": "CRO",
    "twitter": "cryptocom",
    "url": "https://crypto.com/defi/swap",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://support.defibox.cc/hc/en-us/articles/************-PeckShield-Smart-Contract-Security-Audit-Report",
      "https://support.defibox.cc/hc/en-us/articles/************",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "EOS",
    "chains": Array [
      "eos",
      "wax",
      "bsc",
    ],
    "cmcId": "6960",
    "config": Object {
      "enabled": true,
      "id": "507",
    },
    "description": "Defibox is a one-stop DeFi application platform. The project was launched on EOS chain (https://defibox.io) on July 21, 2020 and Swap protocol was launched on BSC chain(https://bscdapp.defibox.io/) on July 21, 2021. Currently, Defibox has launched three protocols, namely Swap protocol, USN stable coin protocol and decentralized lending protocol.",
    "disabled": false,
    "displayName": "DefiBox",
    "enabled": true,
    "gecko_id": "defibox",
    "id": "507",
    "logo": "https://icons.llama.fi/defibox.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/defibox",
    "module": "defibox",
    "name": "DefiBox",
    "protocolType": undefined,
    "symbol": "BOX",
    "twitter": "DefiboxOfficial",
    "url": "https://defibox.io",
  },
  Object {
    "address": "-",
    "audit_links": Array [
      "https://defichain.com/security/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "DefiChain",
    "chains": Array [
      "defichain",
    ],
    "cmcId": "5804",
    "config": Object {
      "enabled": true,
      "id": "1166",
    },
    "description": "Swap between DFI and wrapped tokens in a completely decentralized, exchange.",
    "disabled": false,
    "displayName": "DefiChain DEX",
    "enabled": true,
    "gecko_id": "defichain",
    "id": "1166",
    "logo": "https://icons.llama.fi/defichain.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/defichain-dex",
    "module": "defichain-dex",
    "name": "DefiChain DEX",
    "protocolType": undefined,
    "symbol": "DFI",
    "twitter": "defichain",
    "url": "https://defichain.com/dex",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/pessimistic-io/audits/blob/main/DeFi%20Plaza%20Security%20Analysis%20by%20Pessimistic.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "14636",
    "config": Object {
      "enabled": true,
      "id": "728",
    },
    "description": "DefiPlaza is the low-cost distributed exchange on Ethereum",
    "disabled": false,
    "displayName": "DefiPlaza",
    "enabled": true,
    "gecko_id": "defiplaza",
    "id": "728",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/defiplaza.svg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/defiplaza",
    "module": "defiplaza",
    "name": "DefiPlaza",
    "protocolType": undefined,
    "symbol": "DFP2",
    "twitter": "DefiPlaza",
    "url": "https://defiplaza.net/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Carbon",
    "chains": Array [
      "carbon",
    ],
    "cmcId": "1240",
    "config": Object {
      "enabled": true,
      "id": "2001",
    },
    "description": "The first fully decentralized DEX that supports any type of financial market.",
    "disabled": false,
    "displayName": "Demex",
    "enabled": true,
    "gecko_id": null,
    "id": "2001",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/demex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/demex",
    "module": "demex",
    "name": "Demex",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "demexchange",
    "url": "https://app.dem.exchange/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2589",
    },
    "description": "Dexalot is a revolutionary decentralized exchange aiming at bringing the traditional centralized exchange look and feel through a decentralized on-chain application.",
    "disabled": false,
    "displayName": "Dexalot",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2589",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dexalot.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dexalot",
    "module": "dexalot",
    "name": "Dexalot",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ALOT",
    "twitter": "dexalotcom",
    "url": "https://dexalot.com/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/dfx-finance/protocol/blob/main/audits/2021-05-03-Trail_of_Bits.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "polygon",
    ],
    "cmcId": "8666",
    "config": Object {
      "enabled": true,
      "id": "366",
    },
    "description": "DFX is a decentralized foreign exchange protocol optimized for trading fiat-backed foreign stablecoins",
    "disabled": false,
    "displayName": "DFX Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Shell Protocol",
    ],
    "gecko_id": "dfx-finance",
    "id": "366",
    "logo": "https://icons.llama.fi/dfx-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dfx-finance",
    "module": "dfx-finance",
    "name": "DFX Finance",
    "protocolType": undefined,
    "symbol": "DFX",
    "twitter": "DFXFinance",
    "url": "https://app.dfx.finance",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "9511",
    "config": Object {
      "enabled": true,
      "id": "318",
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "Dfyn Network",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "dfyn-network",
    "id": "318",
    "logo": "https://icons.llama.fi/dfyn-network.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dfyn",
    "module": "dfyn",
    "name": "Dfyn Network",
    "oracles": Array [
      "Chainlink",
      "DIA",
    ],
    "protocolType": undefined,
    "symbol": "DFYN",
    "twitter": "_Dfyn",
    "url": "https://www.dfyn.network/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/dinosaureggs",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "12549",
    "config": Object {
      "enabled": true,
      "id": "695",
    },
    "description": "The dinosaur world is a metaverse built on the Binance Smart Chain (BSC) which includes a trading platform and NFT exchange market, followed by a social networking platform in the future.",
    "disabled": false,
    "displayName": "Dinosaur Eggs",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "dinosaureggs",
    "id": "695",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dinosaur-eggs.svg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dinosaur-eggs",
    "module": "dinosaur-eggs",
    "name": "Dinosaur Eggs",
    "protocolType": undefined,
    "symbol": "DSG",
    "twitter": "dsgmetaverse",
    "url": "https://dsgmetaverse.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/DODOEX/docs/blob/master/docs/audit.md",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "aurora",
      "bsc",
      "ethereum",
      "polygon",
    ],
    "cmcId": "7224",
    "config": Object {
      "enabled": true,
      "id": "146",
    },
    "description": "Trade crypto assets with market-leading liquidity",
    "disabled": false,
    "displayName": "DODO",
    "enabled": true,
    "gecko_id": "dodo",
    "governanceID": Array [
      "snapshot:dodobird.eth",
    ],
    "id": "146",
    "logo": "https://icons.llama.fi/dodo.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dodo",
    "module": "dodo",
    "name": "DODO",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "DODO",
    "treasury": "dodo.js",
    "twitter": "BreederDodo",
    "url": "https://dodoex.io/",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "20538",
    "config": Object {
      "enabled": true,
      "id": "1756",
    },
    "description": "A reimagined, community-oriented ve(3,3) dex on Polygon",
    "disabled": false,
    "displayName": "Dystopia",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "dystopia",
    "id": "1756",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dystopia.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/dystopia",
    "module": "dystopia",
    "name": "Dystopia",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "DYST",
    "twitter": "dystopiaswap",
    "url": "https://www.dystopia.exchange",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.ede.finance/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "arbitrum",
    ],
    "cmcId": "22810",
    "config": Object {
      "enabled": true,
      "id": "2356",
    },
    "description": "El Dorado Exchange(EDE) is a decentralized spot and perpetual social trading exchange which prioritizes user security and stable investor returns. In EDE, all the interactions will happen on-chain. Trading is supported by 3 unique multi-asset pools",
    "disabled": false,
    "displayName": "El Dorado Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "el-dorado-exchange",
    "governanceID": Array [
      "snapshot:edefinance.eth",
    ],
    "id": "2356",
    "listedAt": 1670855637,
    "logo": "https://icons.llama.fi/el-dorado-exchange.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/el-dorado-exchange",
    "module": "el-dorado-exchange",
    "name": "El Dorado Exchange",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "EDE",
    "twitter": "ede_finance",
    "url": "https://ede.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://blog.hashex.org/elk-finance-smart-contracts-audit-report-a18deaa5890b",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "arbitrum",
      "avax",
      "bsc",
      "fantom",
      "polygon",
      "xdai",
      "elastos",
      "okexchain",
      "kcc",
      "ethereum",
      "optimism",
      "fuse",
      "iotex",
      "telos",
    ],
    "cmcId": "10095",
    "config": Object {
      "enabled": true,
      "id": "420",
    },
    "description": "Elk Finance is building Web3 infrastructure for cross-chain value and data transfer via ElkNet - our decentralized bridge. Our motto is Any chain, anytime, anywhere.",
    "disabled": false,
    "displayName": "Elk",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "elk-finance",
    "id": "420",
    "logo": "https://icons.llama.fi/elk.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/elk",
    "module": "elk",
    "name": "Elk",
    "protocolType": undefined,
    "symbol": "ELK",
    "twitter": "elk_finance",
    "url": "https://elk.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/ellipsis-finance/ellipsis-audits/blob/master/010421_Hacken_Ellipsis_SC_Audit_Report.pdf",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "8938",
    "config": Object {
      "enabled": true,
      "id": "238",
    },
    "description": "Secure low-slippage stable swapping on BSC.",
    "disabled": false,
    "displayName": "Ellipsis Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Curve",
    ],
    "gecko_id": "ellipsis",
    "id": "238",
    "logo": "https://icons.llama.fi/ellipsis-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ellipsis",
    "module": "ellipsis",
    "name": "Ellipsis Finance",
    "protocolType": undefined,
    "symbol": "EPS",
    "twitter": "Ellipsisfi",
    "url": "https://ellipsis.finance/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/emdx-dex/perpetual-protocol/blob/emdx/main/audit/2021-12%20EMDX%20Protocol%20Audit.final.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2299",
    },
    "description": "EMDX is a decentralized derivative protocol with an in-house spot market, expanding risk hedging tools for traditional assets into the DeFi environment and allowing token listing “on demand” with “liquidity as a service” from the start.",
    "disabled": false,
    "displayName": "EMDX",
    "enabled": false,
    "gecko_id": null,
    "id": "2299",
    "listedAt": 1669035629,
    "logo": "https://icons.llama.fi/emdx.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/emdx",
    "module": "emdx",
    "name": "EMDX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "emdx_io",
    "url": "https://emdx.io/",
  },
  Object {
    "address": "empire:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "xdai",
      "polygon",
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "812",
    },
    "description": "#EmpireDEX is a Multi-Chain DEX Protocol",
    "disabled": false,
    "displayName": "EmpireDEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "812",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/empire-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/empiredex",
    "module": "empiredex",
    "name": "EmpireDEX",
    "protocolType": undefined,
    "symbol": "EMPIRE",
    "twitter": "Empire_DEX",
    "url": "https://ecc.capital/swap",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Energi",
    "chains": Array [
      "energi",
    ],
    "cmcId": "3218",
    "config": Object {
      "enabled": true,
      "id": "242",
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Energi.",
    "disabled": false,
    "displayName": "Energiswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "energi",
    "id": "242",
    "logo": "https://icons.llama.fi/energiswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/energiswap",
    "module": "energiswap",
    "name": "Energiswap",
    "protocolType": undefined,
    "symbol": "NRG",
    "twitter": "energi",
    "url": "https://www.energiswap.exchange",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": "Equalizer is forked from Velodrome, which had it's improvements over Solidly audited multiple times.",
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2332",
    },
    "description": "Equalizer's twin-AMM design unites StableSwap pools with Standard 'kxy' liquidity pools. All the trading fees go to Vote-Escrowers of emission token $EQUAL which has to be Locked to earn triple 'Bribes' from candidate pools via Trade Fee, Internal Bribes & External Bribes.",
    "disabled": false,
    "displayName": "Equalizer Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "equalizer-dex",
    "id": "2332",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/equalizer-exchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/equalizer-exchange",
    "module": "equalizer-exchange",
    "name": "Equalizer Exchange",
    "oracles": Array [
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "EQUAL",
    "twitter": "Equalizer0x",
    "url": "https://equalizer.exchange",
  },
  Object {
    "address": "kava:******************************************",
    "audit_links": Array [],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2586",
    },
    "description": "Equilibre is an AMM (Automatic Market Maker) based on Velodrome designed to provide large liquidity & low swapping fees",
    "disabled": false,
    "displayName": "Equilibre",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "2586",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/equilibre.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/equilibre",
    "module": "equilibre",
    "name": "Equilibre",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VARA",
    "twitter": "EquilibreAMM",
    "url": "https://equilibrefinance.com/swap",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Mixin",
    "chains": Array [
      "mixin",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1179",
    },
    "description": "ExinSwap is the automated market making trading platform of Exin.",
    "disabled": false,
    "displayName": "ExinSwap",
    "enabled": true,
    "gecko_id": null,
    "id": "1179",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/exinswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/exinswap",
    "module": "exinswap",
    "name": "ExinSwap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": null,
    "url": "https://app.exinswap.com",
  },
  Object {
    "address": "findora:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Findora",
    "chains": Array [
      "findora",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1671",
    },
    "description": "Fairyswap, the first DEX V2 on Findora (FRA) delivering great trading experience and earning opportunities.",
    "disabled": false,
    "displayName": "FairySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1671",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/fairyswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/fairyswap",
    "module": "fairyswap",
    "name": "FairySwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "FAIRY",
    "twitter": "fairy_swap",
    "url": "https://fairyswap.finance",
  },
  Object {
    "address": "cronos:******************************************",
    "audit_links": Array [
      "https://docs.ferroprotocol.com/extras/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cronos",
    "chains": Array [
      "cronos",
    ],
    "cmcId": "20716",
    "config": Object {
      "enabled": true,
      "id": "1882",
    },
    "description": "a stableswap AMM protocol on Cronos",
    "disabled": false,
    "displayName": "Ferro",
    "enabled": true,
    "forkedFrom": Array [
      "Saddle Finance",
    ],
    "gecko_id": "ferro",
    "id": "1882",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ferro.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ferro",
    "module": "ferro",
    "name": "Ferro",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "FER",
    "twitter": "FerroProtocol",
    "url": "https://ferroprotocol.com",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/flamingo-finance/flamingo-audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "NEO",
    "chains": Array [
      "neo",
    ],
    "cmcId": "7150",
    "config": Object {
      "enabled": true,
      "id": "304",
    },
    "description": "An Interoperable Full-Stack DeFi Protocol on Neo",
    "disabled": false,
    "displayName": "Flamingo Finance",
    "enabled": true,
    "gecko_id": "flamingo-finance",
    "id": "304",
    "logo": "https://icons.llama.fi/flamingo-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/flamingo-finance",
    "module": "flamingo-finance",
    "name": "Flamingo Finance",
    "protocolType": undefined,
    "symbol": "FLM",
    "twitter": "FlamingoFinance",
    "url": "https://flamingo.finance/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "aurora",
      "avax",
      "boba",
      "bsc",
      "ethereum",
      "fantom",
      "harmony",
      "moonbeam",
      "moonriver",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2121",
    },
    "description": "Fraxswap is the first constant product automated market maker with an embedded time-weighted average market maker (TWAMM) for conducting large trades over long periods of time trustlessly. It is fully permissionless and the core AMM is based on Uniswap V2.",
    "disabled": false,
    "displayName": "Frax Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2121",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/frax.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/frax-swap",
    "module": "frax-swap",
    "name": "Frax Swap",
    "oracles": Array [],
    "parentProtocol": "Frax Finance",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "fraxfinance",
    "url": "https://app.frax.finance/swap/main",
  },
  Object {
    "address": "functionx:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "FunctionX",
    "chains": Array [
      "functionx",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2138",
    },
    "description": "A scalable, open and decentralized ecosystem, enabling all to build entirely on and for the blockchain.",
    "disabled": false,
    "displayName": "FX Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2138",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/fx-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/fx-swap",
    "module": "fx-swap",
    "name": "FX Swap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "WFX",
    "twitter": "FUNCTIONX_IO",
    "url": "https://fx-swap.io/#/swap",
  },
  Object {
    "address": "boba:******************************************",
    "audit_links": Array [
      "https://github.com/gin-finance/gin-finance-audits/blob/main/PeckShield-Audit-Report-GinFinance-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Boba",
    "chains": Array [
      "boba",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1795",
    },
    "description": "Gin Finance is a DEX solution on BOBA Network. It is an open-source protocol for providing liquidity and trading ERC20 tokens on BOBA Network.",
    "disabled": false,
    "displayName": "Gin Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "gin-finance",
    "id": "1795",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/gin-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/gin-finance",
    "module": "gin-finance",
    "name": "Gin Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "GIN",
    "twitter": "ginfinance_",
    "url": "https://www.gin.finance",
  },
  Object {
    "address": "esc:******************************************",
    "audit_links": Array [
      "https://docs.glidefinance.io/security/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Elastos",
    "chains": Array [
      "elastos",
    ],
    "cmcId": "19398",
    "config": Object {
      "enabled": true,
      "id": "806",
    },
    "description": "Glide Finance is a Decentralized Exchange / Automated Market Maker, Yield Farming, and Staking platform running on the Elastos Smart Chain (ESC) that aims to accelerate adoption of the Elastos ecosystem by acting as a source of liquidity for users and the projects built on it.",
    "disabled": false,
    "displayName": "Glide Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "glide-finance",
    "id": "806",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/glide-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/glide-finance",
    "module": "glide-finance",
    "name": "Glide Finance",
    "protocolType": undefined,
    "symbol": "GLIDE",
    "twitter": "GlideFinance",
    "url": "https://glidefinance.io",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/xvi10/gambit-contracts/tree/master/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "arbitrum",
      "avax",
    ],
    "cmcId": "11857",
    "config": Object {
      "enabled": true,
      "id": "337",
      "protocolsData": Object {
        "swap": Object {
          "category": "Dexs",
          "displayName": "GMX - SWAP",
          "enabled": true,
          "id": "337",
        },
      },
    },
    "description": "GMX is a decentralized spot and perpetual exchange that supports low swap fees and zero price impact trades. Trading is supported by a unique multi-asset pool that earns liquidity providers fees from market making, swap fees, leverage trading (spreads, funding fees & liquidations) and asset rebalancing.",
    "disabled": false,
    "displayName": "GMX - SWAP",
    "enabled": true,
    "gecko_id": "gmx",
    "governanceID": Array [
      "snapshot:gmx.eth",
    ],
    "id": "337",
    "logo": "https://icons.llama.fi/gmx.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/gmx",
    "module": "gmx",
    "name": "GMX",
    "protocolType": undefined,
    "referralUrl": "https://gmx.io/#/?ref=defillama",
    "symbol": "GMX",
    "twitter": "GMX_IO",
    "url": "https://gmx.io/",
    "versionKey": "swap",
  },
  Object {
    "address": "solana:GFX1ZjR2P15tmrSwow6FjyDYcEkoFb4p4gJCpLBjaxHD",
    "audit_links": Array [
      "https://safefiles.defiyield.info/safe/files/audit/pdf/GooseFX_Swap_Program_Security_Audit_Report_Halborn_Final.pdf",
      "https://github.com/GooseFX1/gfx-swap/blob/master/audit/goosefx_ssl-audit-public.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "solana",
    ],
    "cmcId": "12898",
    "config": Object {
      "enabled": true,
      "id": "2175",
    },
    "description": " A full suite DeFi platform to trade crypto, derivatives, NFTs and single sided liquidity pools with yield farming.",
    "disabled": false,
    "displayName": "GooseFX",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "goosefx",
    "id": "2175",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/goosefx.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/goosefx",
    "module": "goosefx",
    "name": "GooseFX",
    "oracles": Array [
      "Pyth",
    ],
    "protocolType": undefined,
    "symbol": "GOFX",
    "twitter": "GooseFX1",
    "url": "https://app.goosefx.io",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/chainsulting/Smart-Contract-Security-Audits/blob/master/Gravis%20Finance/02_Smart%20Contract%20Audit_GravisFinance_Farm.pdf?network=56&gravisLanguage=en",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "polygon",
      "bsc",
    ],
    "cmcId": "19788",
    "config": Object {
      "enabled": true,
      "id": "2195",
    },
    "description": "A complete tool set for basic income: DeFi - AMM DEX",
    "disabled": false,
    "displayName": "Gravis Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "gravis-finance",
    "id": "2195",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/gravis-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/gravis",
    "module": "gravis",
    "name": "Gravis Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "GRVS",
    "twitter": "GravisFi",
    "url": "https://gswap.exchange",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://github.com/Tibereum/obelisk-audits/blob/main/Gravity.pdf",
      "https://github.com/JorgeRodriguezsec/CTDsec/blob/main/Audits/Cybersecurity_Audit_CTDSEC_Gravity_v4.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "10324",
    "config": Object {
      "enabled": true,
      "id": "351",
    },
    "description": "Gravity Finance is a DeFi platform, consisting of a Decentralised Exchange, Yield Farms, Auto-Compounding Vaults, an IDO Launchpad and Automated Investment Strategies called Silos. Gravity Admin Fees are paid to Governance Token Holders as ETH and BTC.",
    "disabled": false,
    "displayName": "Gravity Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "gravity-finance",
    "id": "351",
    "logo": "https://icons.llama.fi/gravity-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/gravity-finance",
    "module": "gravity-finance",
    "name": "Gravity Finance",
    "protocolType": undefined,
    "symbol": "GFI",
    "twitter": "Gravity_Finance",
    "url": "https://gravityfinance.io/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://hakuswap.com/audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "17219",
    "config": Object {
      "enabled": true,
      "id": "1253",
    },
    "description": "HakuSwap is a crypto world for users to trade, earn, and game. It is the premier choice for projects on Avalanche with features including AMM, NFT, and GameFi.",
    "disabled": false,
    "displayName": "HakuSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "hakuswap",
    "id": "1253",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/hakuswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/hakuswap",
    "module": "hakuswap",
    "name": "HakuSwap",
    "protocolType": undefined,
    "symbol": "HAKU",
    "twitter": "HakuSwap",
    "url": "https://hakuswap.com",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "avax",
      "bsc",
      "arbitrum",
      "optimism",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1447",
    },
    "description": "The most powerful DeFi trading experience. Tight spreads. Zero slippage. MEV-resistant",
    "disabled": false,
    "displayName": "Hashflow",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:hashflowdao.eth",
    ],
    "id": "1447",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/hashflow.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/hashflow",
    "module": "hashflow",
    "name": "Hashflow",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "HFT",
    "twitter": "hashflow",
    "url": "https://www.hashflow.com",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/LimeChain/HeliSwap-contracts/blob/main/audits/Halborn-august-2022.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Hedera",
    "chains": Array [
      "hedera",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2244",
    },
    "description": "The first DEX on the Hedera network supporting swaps between HTS, ERC20s and HBAR. Open-sourced, completely trustless and permissionless. Built on the Hedera Smart Contract Service with the aim to become the defacto AMM being a public good within the network.",
    "disabled": false,
    "displayName": "HeliSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2244",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/heliswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/heliswap",
    "module": "heliswap",
    "name": "HeliSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "HeliSwap_DEX",
    "url": "https://www.heliswap.io",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Injective",
    "chains": Array [
      "injective",
    ],
    "cmcId": "1551",
    "config": Object {
      "enabled": true,
      "id": "2259",
    },
    "description": "The premier decentralized crypto exchange. Trade unlimited cross-chain spot and futures markets.",
    "disabled": false,
    "displayName": "Helix",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2259",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/helix.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/helix",
    "module": "helix",
    "name": "Helix",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "HelixApp_",
    "url": "https://helixapp.com",
  },
  Object {
    "address": "metis:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Metis",
    "chains": Array [
      "metis",
    ],
    "cmcId": "21272",
    "config": Object {
      "enabled": true,
      "id": "1384",
    },
    "description": "Hermes is the first DEX that allows low cost, near 0 slippage trades on uncorrelated or tightly correlated assets. The protocol incentivizes fees instead of liquidity.",
    "disabled": false,
    "displayName": "Hermes Protocol",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "hermes-protocol",
    "id": "1384",
    "language": "Solidity",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/hermes-v2.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/hermes-protocol",
    "module": "hermes-protocol",
    "name": "Hermes Protocol",
    "protocolType": undefined,
    "symbol": "HERMES",
    "twitter": "HermesOmnichain",
    "url": "https://hermes.maiadao.io/",
  },
  Object {
    "address": "null",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Map",
    "chains": Array [
      "map",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2485",
    },
    "description": "Uniswap fork on map blockchain",
    "disabled": false,
    "displayName": "HiveSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2485",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/hiveswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/hiveswap",
    "module": "hiveswap",
    "name": "HiveSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": null,
    "url": "https://swap.hiveswap.io",
  },
  Object {
    "address": "xdai:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "xDai",
    "chains": Array [
      "polygon",
      "xdai",
    ],
    "cmcId": "7972",
    "config": Object {
      "enabled": true,
      "id": "271",
    },
    "description": "Honeyswap is a decentralized exchange built on the xDai Chain, this enables users to experience fast and secure transactions with incredibly low fees. Multiple tokens are available with which you can swap and add liquidity. It is a fork of the well known Uniswap-V2 protocol adapted to fit for the xDai Chain and the 1hive ecosystem.",
    "disabled": false,
    "displayName": "Honeyswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "honey",
    "id": "271",
    "logo": "https://icons.llama.fi/honeyswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/honeyswap",
    "module": "honeyswap",
    "name": "Honeyswap",
    "protocolType": undefined,
    "symbol": "HNY",
    "twitter": "Honeyswap",
    "url": "https://honeyswap.org/",
  },
  Object {
    "address": "moonriver:******************************************",
    "audit_links": Array [
      "https://github.com/HuckleberryDex/huckleberry-contracts/blob/main/Smart%20contract%20security%20audit%20report-Huckleberry.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonriver",
    "chains": Array [
      "moonriver",
      "clv",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "630",
    },
    "description": "Huckleberry is a brand-new kind of community driven AMM crosschain DEX ",
    "disabled": false,
    "displayName": "Huckleberry AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "630",
    "logo": "https://icons.llama.fi/huckleberry-amm.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/huckleberry",
    "module": "huckleberry",
    "name": "Huckleberry AMM",
    "parentProtocol": "Huckleberry",
    "protocolType": undefined,
    "symbol": "FINN",
    "twitter": "HuckleberryDEX",
    "url": "https://www.huckleberry.finance/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://f.hubspotusercontent40.net/hubfs/21135646/Reach - Security Review for HumbleSwap Smart Contract v1.0.pdf",
      "https://drive.google.com/file/d/1H9wEHbkzzR-vazSF3kThUfF2zuynf097/view",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Algorand",
    "chains": Array [
      "algorand",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1629",
    },
    "description": "Humble Defi is your one stop shop Defi suite. Our first product, Humble Swap is an AMM style exchange that allows you to swap, pool and earn on your assets.",
    "disabled": false,
    "displayName": "Humble Defi",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1629",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/humble-defi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/humble-defi",
    "module": "humble-defi",
    "name": "Humble Defi",
    "oracles": Array [
      "Coingecko",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "HumbleDefi",
    "url": "https://www.humble.sh",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.hyperjump.fi/essentials/audits",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "317",
    },
    "description": "Play To Earn Farm To Earn Cross-Chain DEX on FTM, BSC & METIS",
    "disabled": false,
    "displayName": "HyperJump",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "317",
    "logo": "https://icons.llama.fi/hyperjump.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/hyperjump",
    "module": "hyperjump",
    "name": "HyperJump",
    "protocolType": undefined,
    "symbol": "HYPR",
    "twitter": "Hyperjump_fi",
    "url": "https://www.hyperjump.app",
  },
  Object {
    "address": "bitgert:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Bitgert",
    "chains": Array [
      "bitgert",
      "core",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1990",
    },
    "description": "The first Swap on the Bitgert (Brise) Blockchain",
    "disabled": false,
    "displayName": "IcecreamSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "icecream",
    "id": "1990",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/icecreamswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/icecreamswap",
    "module": "icecreamswap",
    "name": "IcecreamSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ICE",
    "twitter": "icecream_swap",
    "url": "https://icecreamswap.com/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/oak-security/audit-reports/tree/master/IncrementFi",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Flow",
    "chains": Array [
      "flow",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1907",
    },
    "description": "A permisionless CPAMM-based decentralized exchange on Flow.",
    "disabled": false,
    "displayName": "Increment Swap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1907",
    "language": "Cadence",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/increment-swap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/increment-swap",
    "module": "increment-swap",
    "name": "Increment Swap",
    "oracles": Array [],
    "parentProtocol": "incrementFinance",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "incrementfi",
    "url": "https://app.increment.fi/swap",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.integral.link/library/audit-reports",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "ethereum",
    ],
    "cmcId": "10404",
    "config": Object {
      "enabled": true,
      "id": "291",
    },
    "description": "Integral is a new DeFi primitive that efficiently executes large orders. Its latest product, SIZE, is a OTC-style decentralized exchange. With built-in TWAP execution, SIZE enables DAOs and whales to trade with 0 price impact at any order size.",
    "disabled": false,
    "displayName": "Integral",
    "enabled": true,
    "gecko_id": "integral",
    "id": "291",
    "logo": "https://icons.llama.fi/integral.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/integral",
    "module": "integral",
    "name": "Integral",
    "protocolType": undefined,
    "symbol": "ITGR",
    "twitter": "IntegralHQ",
    "url": "https://integral.link/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docsend.com/view/ura947u6ck3urpqb",
      "https://docsend.com/view/2bif3vfinpv657mh",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1883",
    },
    "description": "A next-generation DEX on BNB Chain to maximize capital efficiency with the innovative Discretized-Liquidity-AMM model",
    "disabled": false,
    "displayName": "iziSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1883",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/iziswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/iziswap",
    "module": "iziswap",
    "name": "iziSwap",
    "oracles": Array [],
    "parentProtocol": "iZUMI Finance",
    "protocolType": undefined,
    "symbol": "IZI",
    "twitter": "izumi_Finance",
    "url": "https://izumi.finance/trade/swap",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Starknet",
    "chains": Array [
      "starknet",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2344",
    },
    "description": "A community-led fully permissionless and composable AMM on Starknet by StarkWareLtd",
    "disabled": false,
    "displayName": "JediSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2344",
    "language": "Cairo",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/jediswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/jediswap",
    "module": "jediswap",
    "name": "JediSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "JediSwap",
    "url": "https://jediswap.xyz",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://jetswap.finance/audit-by-etherauthority.pdf",
      "https://jetswap.finance/audit-by-hash0x.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10810",
    "config": Object {
      "enabled": true,
      "id": "659",
    },
    "description": "Jetfuel ecosystem is an all-in-one Defi that's meant to be a one-stop-shop for users, it accounts with an AMM & Yield Farming on Jetswap, Lending/borrowing markets on Fortress, a Yield optimizer on Jetfuel.",
    "disabled": false,
    "displayName": "JetSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "jetswap-token",
    "id": "659",
    "logo": "https://icons.llama.fi/jetswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/jetswap",
    "module": "jetswap",
    "name": "JetSwap",
    "protocolType": undefined,
    "symbol": "WINGS",
    "twitter": "Jetfuelfinance",
    "url": "https://jetswap.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2320",
    },
    "description": "JOJO is a decentralized perpetual contract exchange based on an off-chain matching system.",
    "disabled": false,
    "displayName": "JOJO",
    "enabled": false,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2320",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/jojo.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/jojo",
    "module": "jojo",
    "name": "JOJO",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "jojo_exchange",
    "url": "https://app.jojo.exchange/trade",
  },
  Object {
    "address": "okex:******************************************",
    "audit_links": Array [
      "https://www.slowmist.com/en/security-audit-certificate.html?id=928799684ad96ef4ed4b0c0fb12a5fae085456f874b19dc4300195b32a5a1431",
      "https://www.slowmist.com/en/security-audit-certificate.html?id=e18f32468d1c52dc0682fbf1c1137df933fabb09526b124c84cd391fce7149bb",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "OKExChain",
    "chains": Array [
      "okexchain",
    ],
    "cmcId": "11146",
    "config": Object {
      "enabled": true,
      "id": "678",
    },
    "description": "Jswap Finance is the community-driven multi-chain compatible Dex and assets management protocol.",
    "disabled": false,
    "displayName": "JSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "jswap-finance",
    "id": "678",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/jswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/jswap",
    "module": "jswap",
    "name": "JSwap",
    "protocolType": undefined,
    "symbol": "JF",
    "twitter": "Jswap_Finance",
    "url": "https://app.jswap.finance/#/swap",
  },
  Object {
    "address": "juno:-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Juno",
    "chains": Array [
      "juno",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2052",
    },
    "description": "JunoSwap is an interchain decentralized exchange focussed on CW-20 (CosmWasm) asset adoption. Built on the public permission-less Juno Network",
    "disabled": false,
    "displayName": "Junoswap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "junoswap-raw-dao",
    "id": "2052",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/junoswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/junoswap",
    "module": "junoswap",
    "name": "Junoswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "RAW",
    "twitter": "JunoNetwork",
    "url": "https://www.junoswap.com/",
  },
  Object {
    "address": "kardia:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Kardia",
    "chains": Array [
      "kardia",
    ],
    "cmcId": "21222",
    "config": Object {
      "enabled": true,
      "id": "712",
    },
    "description": "KAIDEX is the first of its kind in the decentralized exchange market, possessing the capabilities of a completely decentralized cross-chain token trading platform. KAIDEX is built upon the Dual Node technology, a novel patent-pending technology that would truly revolutionize multi-chain swapping.",
    "disabled": false,
    "displayName": "Kaidex",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "kaidex",
    "id": "712",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/kaidex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kaidex",
    "module": "kaidex",
    "name": "Kaidex",
    "protocolType": undefined,
    "symbol": "KDX",
    "twitter": "KardiaChain",
    "url": "https://kaidex.io",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Karura",
    "chains": Array [
      "KARURA",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "451",
    },
    "description": "Karura Swap is a trustless, automated market maker (AMM)-styled decentralized exchange on the Karura network.",
    "disabled": false,
    "displayName": "Karura Swap",
    "enabled": true,
    "gecko_id": null,
    "id": "451",
    "logo": "https://icons.llama.fi/karura-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/karura-swap",
    "module": "karura-swap",
    "name": "Karura Swap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "KaruraNetwork",
    "url": "https://acala.network/karura",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ronin",
    "chains": Array [
      "ronin",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "797",
    },
    "description": "Katana a DEX For Ronin",
    "disabled": false,
    "displayName": "Katana DEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "797",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/katana.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/katana",
    "module": "katana",
    "name": "Katana DEX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "AxieInfinity",
    "url": "https://katana.roninchain.com/#/swap",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": "11562",
    "config": Object {
      "enabled": true,
      "id": "618",
    },
    "description": "Kava Swap is a cross-chain Autonomous Market Making (AMM) application designed to provide an efficient and safe way to swap natively between the world's largest cryptocurrency assets",
    "disabled": false,
    "displayName": "Kava Swap",
    "enabled": true,
    "gecko_id": "kava-swap",
    "id": "618",
    "logo": "https://icons.llama.fi/kava-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kava-swap",
    "module": "kava-swap",
    "name": "Kava Swap",
    "protocolType": undefined,
    "symbol": "SWP",
    "twitter": "Kava_Swap",
    "url": "https://app.kava.io/swap/pools",
  },
  Object {
    "address": "-",
    "audit_links": Array [
      "https://docs.klayswap.com/risk-and-security#greater-than-read-the-smart-contract-audit-reports-here",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Klaytn",
    "chains": Array [
      "klaytn",
    ],
    "cmcId": "8296",
    "config": Object {
      "enabled": true,
      "id": "508",
    },
    "description": "KLAYswap is an AMM-based Instant Swap Protocol",
    "disabled": false,
    "displayName": "KlaySwap",
    "enabled": true,
    "gecko_id": "klayswap-protocol",
    "id": "508",
    "logo": "https://icons.llama.fi/klayswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/klayswap",
    "module": "klayswap",
    "name": "KlaySwap",
    "protocolType": undefined,
    "symbol": "KSP",
    "twitter": "KLAYswap",
    "url": "https://klayswap.com/dashboard",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/balancer-labs/balancer-v2-monorepo/tree/master/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Klaytn",
    "chains": Array [
      "klaytn",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2049",
    },
    "description": "KLEX is a protocol for programmable liquidity.",
    "disabled": false,
    "displayName": "KLEX Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Balancer",
    ],
    "gecko_id": null,
    "id": "2049",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/klex-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/klex-finance",
    "module": "klex-finance",
    "name": "KLEX Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "klexfinance",
    "url": "https://klex.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/TechRate/Smart-Contract-Audits/blob/main/November/KnightSwap.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "fantom",
    ],
    "cmcId": "15841",
    "config": Object {
      "enabled": true,
      "id": "942",
    },
    "description": "Trade, Earn, & Raid To Stack Your Riches While Securely Storing Them Within Our Castle Vaults",
    "disabled": false,
    "displayName": "KnightSwap Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "knightswap",
    "id": "942",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/knightswap-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/knightswap-finance",
    "module": "knightswap-finance",
    "name": "KnightSwap Finance",
    "protocolType": undefined,
    "symbol": "KNIGHT",
    "twitter": "KnightEcosystem",
    "url": "https://knightswap.financial",
  },
  Object {
    "address": "klaytn:******************************************",
    "audit_links": Array [
      "https://drive.google.com/file/d/1I0HnU8Rs88HHqBC6Y3aUcEKTPZfVTAZD/view",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Klaytn",
    "chains": Array [
      "klaytn",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1790",
    },
    "description": "Kokonut Swap is a 2nd generation DEX protocol, with the lowest slippage and fees made possible through self-implemented Stable Swaps and Crypto Pools.",
    "disabled": false,
    "displayName": "Kokonut Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Curve",
    ],
    "gecko_id": null,
    "id": "1790",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/kokonut-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kokonut-swap",
    "module": "kokonut-swap",
    "name": "Kokonut Swap",
    "oracles": Array [
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "KOKOS",
    "twitter": null,
    "url": "https://kokonutswap.finance",
  },
  Object {
    "address": "boba:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Boba",
    "chains": Array [
      "boba",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1766",
    },
    "description": "Kōyō is the first next-generation AMM protocol in the Boba ecosystem. It is inspired by Balancer. At its core, it's a decentralised exchange (DEX) that minimizes unnecessary losses from swaps between assets of equal value.",
    "disabled": false,
    "displayName": "Koyo Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Balancer",
    ],
    "gecko_id": "koyo",
    "id": "1766",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/koyo-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/koyo",
    "module": "koyo",
    "name": "Koyo Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "KYO",
    "treasury": "koyo.js",
    "twitter": "KoyoFinance",
    "url": "https://koyo.finance",
  },
  Object {
    "address": "kava:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2326",
    },
    "description": "KPerp.Exchange is a decentralized exchange created to offer a broad selection of trading options and extremely high levels of liquidity on numerous cryptocurrencies.",
    "disabled": false,
    "displayName": "KPerp Exchange",
    "enabled": false,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": null,
    "id": "2326",
    "listedAt": 1669910621,
    "logo": "https://icons.llama.fi/kperp-exchange.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kperp-exchange",
    "module": "kperp-exchange",
    "name": "KPerp Exchange",
    "oracles": Array [
      "TWAP",
      "Witnet",
    ],
    "protocolType": undefined,
    "symbol": "KPE",
    "twitter": "KPerpExchange",
    "url": "https://kperp.exchange/",
  },
  Object {
    "address": "kucoin:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Kucoin",
    "chains": Array [
      "kcc",
    ],
    "cmcId": "1420",
    "config": Object {
      "enabled": true,
      "id": "480",
    },
    "description": "KuSwap is the first decentralized trading platform on the KCC network to offer the lowest platform transaction fees (0.1 percent), with fees refunded in KUS, our native currency.",
    "disabled": false,
    "displayName": "KuSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "kuswap",
    "id": "480",
    "logo": "https://icons.llama.fi/kuswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kuswap",
    "module": "kuswap",
    "name": "KuSwap",
    "oracles": Array [
      "Pyth",
    ],
    "protocolType": undefined,
    "symbol": "KUS",
    "twitter": "kuswapfinance",
    "url": "https://kuswap.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://chainsecurity.com/security-audit/kyber-network-dynamic-market-maker-dmm/",
    ],
    "audit_note": null,
    "audits": null,
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "aurora",
      "optimism",
      "arbitrum",
      "fantom",
      "avax",
      "bsc",
      "polygon",
      "ethereum",
      "cronos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "127",
      "protocolsData": Object {
        "classic": Object {
          "displayName": "KyberSwap - Classic",
          "enabled": true,
          "id": "127",
        },
        "elastic": Object {
          "displayName": "KyberSwap - Elastic",
          "enabled": true,
          "id": "2615",
        },
      },
    },
    "description": "KyberSwap is both a decentralized exchange (DEX) aggregator and a liquidity source with capital-efficient liquidity pools that earns fees for liquidity providers",
    "disabled": false,
    "displayName": "KyberSwap - Classic",
    "enabled": true,
    "gecko_id": null,
    "id": "127",
    "logo": "https://icons.llama.fi/kyberswap-classic.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kyberswap",
    "module": "kyberswap",
    "name": "KyberSwap Classic",
    "oracles": Array [
      "Chainlink",
      "Band",
    ],
    "parentProtocol": "KyberSwap",
    "protocolType": undefined,
    "symbol": "KNC",
    "twitter": "KyberNetwork",
    "url": "https://kyberswap.com/#/swap",
    "versionKey": "classic",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://chainsecurity.com/security-audit/kyber-network-dynamic-market-maker-dmm/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "optimism",
      "arbitrum",
      "fantom",
      "avax",
      "bsc",
      "polygon",
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "127",
      "protocolsData": Object {
        "classic": Object {
          "displayName": "KyberSwap - Classic",
          "enabled": true,
          "id": "127",
        },
        "elastic": Object {
          "displayName": "KyberSwap - Elastic",
          "enabled": true,
          "id": "2615",
        },
      },
    },
    "description": "KyberSwap is both a decentralized exchange (DEX) aggregator and a liquidity source with capital-efficient liquidity pools that earns fees for liquidity providers",
    "disabled": false,
    "displayName": "KyberSwap - Elastic",
    "enabled": true,
    "gecko_id": null,
    "id": "127",
    "logo": "https://icons.llama.fi/kyberswap-elastic.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kyberswap",
    "module": "kyberswap",
    "name": "KyberSwap Elastic",
    "oracles": Array [
      "Chainlink",
      "Band",
    ],
    "parentProtocol": "KyberSwap",
    "protocolType": undefined,
    "symbol": "KNC",
    "twitter": "KyberNetwork",
    "url": "https://kyberswap.com/#/swap",
    "versionKey": "elastic",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://hacken.io/audits/#leonicorn_swap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "13778",
    "config": Object {
      "enabled": true,
      "id": "923",
    },
    "description": "Advanced Decentralized Ecosystem With a Reward Sharing Economy",
    "disabled": false,
    "displayName": "LeonicornSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "leon-token",
    "id": "923",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/leonicornswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/leonicornswap",
    "module": "leonicornswap",
    "name": "LeonicornSwap",
    "protocolType": undefined,
    "symbol": "LEON",
    "twitter": "swapleonicorn",
    "url": "https://dex.leonicornswap.com/home",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://obeliskauditing.com/audits/level-finance",
      "https://obeliskauditing.com/audits/level-finance-core",
      "https://obeliskauditing.com/audits/level-finance-trading",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2395",
    },
    "description": "Level Finance - Decentralized Perpetual Exchange.",
    "disabled": false,
    "displayName": "Level Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:level-finance.eth",
    ],
    "id": "2395",
    "listedAt": 1672230595,
    "logo": "https://icons.llama.fi/level-finance.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/level-finance",
    "module": "level-finance",
    "name": "Level Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "LVL",
    "twitter": "Level__Finance",
    "url": "https://app.level.finance",
  },
  Object {
    "address": "xdai:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "xDai",
    "chains": Array [
      "xdai",
    ],
    "cmcId": "1353",
    "config": Object {
      "enabled": true,
      "id": "299",
    },
    "description": "AMM DEX on xDai powered by the LightningDAO",
    "disabled": false,
    "displayName": "Levinswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "levin",
    "id": "299",
    "logo": "https://icons.llama.fi/levinswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/levinswap",
    "module": "levinswap",
    "name": "Levinswap",
    "protocolType": undefined,
    "symbol": "LEVIN",
    "twitter": "levinswap",
    "url": "https://levinswap.org/",
  },
  Object {
    "address": "tombchain:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Tombchain",
    "chains": Array [
      "tombchain",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2040",
    },
    "description": "AMM DEX on Tombchain",
    "disabled": false,
    "displayName": "Lif3 Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2040",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/lif3-swap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/lif3-swap",
    "module": "lif3-swap",
    "name": "Lif3 Swap",
    "oracles": Array [],
    "parentProtocol": "Lif3.com",
    "protocolType": undefined,
    "symbol": "LIF3",
    "twitter": "Official_LIF3",
    "url": "https://lif3.com/swap",
  },
  Object {
    "address": "solana:LFNTYraetVioAPnGJht4yNg2aUZFXR776cMeN9VMjXp",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "19842",
    "config": Object {
      "enabled": true,
      "id": "2154",
    },
    "description": "The first proactive market maker on Solana designed to improve capital efficiency and reduce impermanent loss.",
    "disabled": false,
    "displayName": "Lifinity",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "lifinity",
    "id": "2154",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/lifinity.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/lifinity",
    "module": "lifinity",
    "name": "Lifinity",
    "oracles": Array [
      "Pyth",
    ],
    "protocolType": undefined,
    "symbol": "LFNTY",
    "twitter": "Lifinity_io",
    "url": "https://lifinity.io/pools",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://docs.liquidswap.com/#halborn",
      "https://docs.liquidswap.com/#ottersec",
      "https://docs.liquidswap.com/#zellic",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Aptos",
    "chains": Array [
      "aptos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2210",
    },
    "description": "Liquidswap is the first AMM on Aptos, the safest and most scalable L1 blockchain. The DEX runs on Aptos mainnet.",
    "disabled": false,
    "displayName": "LiquidSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2210",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/liquidswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/liquidswap",
    "module": "liquidswap",
    "name": "LiquidSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "PontemNetwork",
    "url": "https://liquidswap.com/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/tomochain/luaswap-core/tree/master/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "tomochain",
      "ethereum",
    ],
    "cmcId": "7216",
    "config": Object {
      "enabled": true,
      "id": "707",
    },
    "description": "LuaSwap is a swap protocol inspired by the previous AMM-based swap protocols such as Uniswap and SushiSwap.",
    "disabled": false,
    "displayName": "LuaSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "lua-token",
    "id": "707",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/luaswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/luaswap",
    "module": "luaswap",
    "name": "LuaSwap",
    "protocolType": undefined,
    "symbol": "LUA",
    "twitter": "LuaSwap",
    "url": "https://luaswap.org/#/",
  },
  Object {
    "address": "stellar:GAB7STHVD5BDH3EEYXPI3OM7PCS4V443PYB5FNT6CFGJVPDLMKDM24WK",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Stellar",
    "chains": Array [
      "stellar",
    ],
    "cmcId": "11678",
    "config": Object {
      "enabled": true,
      "id": "882",
    },
    "description": "Lumenswap is a decentralized exchange built on the Stellar network that allows you to swap and trade assets using a friendly, minimal interface.",
    "disabled": false,
    "displayName": "LumenSwap",
    "enabled": true,
    "gecko_id": "lumenswap",
    "id": "882",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/lumenswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/lumenswap",
    "module": "lumenswap",
    "name": "LumenSwap",
    "protocolType": undefined,
    "symbol": "LSP",
    "twitter": "lumenswap",
    "url": "https://lumenswap.io",
  },
  Object {
    "address": "heco:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/makiswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Heco",
    "chains": Array [
      "heco",
    ],
    "cmcId": "10232",
    "config": Object {
      "enabled": true,
      "id": "378",
    },
    "description": "Makiswap is an automated market maker (AMM). You can think of an AMM as a primitive robotic market maker that is always willing to quote prices between two assets according to a simple pricing algorithm. Makiswap can be farmed that can be found on the 'rewards' section on this page.",
    "disabled": false,
    "displayName": "MakiSwap",
    "enabled": true,
    "gecko_id": "makiswap",
    "id": "378",
    "logo": "https://icons.llama.fi/makiswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/makiswap",
    "module": "makiswap",
    "name": "MakiSwap",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "MAKI",
    "twitter": "MakiSwap",
    "url": "https://makiswap.com",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/maverickprotocol/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2644",
    },
    "description": "The DeFi infrastructure built to bring higher capital efficiency + greater capital control to the liquidity market, powered by Maverick AMM.",
    "disabled": false,
    "displayName": "Maverick Protocol",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2644",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/maverick-protocol.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/maverick",
    "module": "maverick",
    "name": "Maverick Protocol",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mavprotocol",
    "url": "https://www.mav.xyz",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/mux-protocol",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "avax",
      "bsc",
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "232",
    },
    "description": "MUX (Previously MCDEX),The first Multi-Chain native leveraged trading protocol,
allowing zero price impact trading, up to 100x leverage, no counterparty risks for traders and an optimized on-chain trading experience.",
    "disabled": false,
    "displayName": "MCDEX",
    "enabled": false,
    "gecko_id": null,
    "id": "232",
    "logo": "https://icons.llama.fi/mcdex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mcdex",
    "module": "mcdex",
    "name": "MCDEX",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "MUX Protocol",
    "protocolType": undefined,
    "symbol": "MCB",
    "twitter": "muxprotocol",
    "url": "https://mux.network/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://mdex.com/slowmist_bsc.pdf",
      "https://mdex.com/fairyproof_bsc.pdf",
      "https://mdex.com/certik_bsc.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Heco",
    "chains": Array [
      "bsc",
      "heco",
    ],
    "cmcId": "8335",
    "config": Object {
      "enabled": true,
      "id": "334",
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "MDEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "mdex",
    "id": "334",
    "logo": "https://icons.llama.fi/mdex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mdex",
    "module": "mdex",
    "name": "MDEX",
    "protocolType": undefined,
    "symbol": "MDX",
    "twitter": "Mdextech",
    "url": "https://mdex.com/",
  },
  Object {
    "address": "ton:EQB-cCOQDwerEnUD4-6xoYD0eL6_yC1zogAvA5GuXTF0iDIf",
    "audit_links": Array [
      "https://docs.megaton.fi/v/en/more/contract-and-audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "TON",
    "chains": Array [
      "ton",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2540",
    },
    "description": "Megatonfi is an AMM-based Instant Swap Protocol",
    "disabled": false,
    "displayName": "Megaton Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2540",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/megaton-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/megaton-finance",
    "module": "megaton-finance",
    "name": "Megaton Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MEGA",
    "twitter": "Megaton_Fi",
    "url": "https://megaton.fi",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "20169",
    "config": Object {
      "enabled": true,
      "id": "1726",
    },
    "description": "Meshswap is an AMM based decentralized exchange protocol to actively utilize the assets on Polygon.",
    "disabled": false,
    "displayName": "Meshswap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "meshswap-protocol",
    "id": "1726",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/meshswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/meshswap",
    "module": "meshswap",
    "name": "Meshswap",
    "oracles": Array [
      "Internal",
    ],
    "protocolType": undefined,
    "symbol": "MESH",
    "twitter": "Meshswap_Fi",
    "url": "https://meshswap.fi",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "heco",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2253",
    },
    "description": "MetaTdex is a decentralized order matching trading platform",
    "disabled": false,
    "displayName": "MetaTdex",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2253",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/metatdex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/metatdex",
    "module": "metatdex",
    "name": "MetaTdex",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "TT",
    "twitter": "MetaTdex",
    "url": "https://www.metatdex.com",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://github.com/metavaultorg/trade-contracts/blob/main/Metavault.Trade_Full_Smart_Contract_Security_Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "19973",
    "config": Object {
      "enabled": true,
      "id": "1801",
    },
    "description": "Metavault.Trade is a new kind of Decentralised Exchange, designed to provide a large range of trading features and very deep liquidity on many large cap crypto assets. Traders can use it in two ways: Spot trading, with swaps and limit orders. Perpetual futures trading with up to 50x leverage on short and long positions. Metavault.Trade aims to become the go-to solution for traders who want to stay in control of their funds at all times without sharing their personal data.",
    "disabled": false,
    "displayName": "Metavault.Trade",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "metavault-trade",
    "id": "1801",
    "listedAt": 1654203519,
    "logo": "https://icons.llama.fi/metavault.trade.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/metavault.trade",
    "module": "metavault.trade",
    "name": "Metavault.Trade",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Metavault",
    "protocolType": undefined,
    "symbol": "MVX",
    "twitter": "MetavaultTRADE",
    "url": "https://metavault.trade",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2452",
    },
    "description": "Metropolis is a Liquidity Book AMM on Fantom focused on offering a premier trading experience with zero slippage and low fees.",
    "disabled": false,
    "displayName": "Metropolis",
    "enabled": true,
    "forkedFrom": Array [
      "TraderJoe",
    ],
    "gecko_id": null,
    "id": "2452",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/metropolis.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/metropolis",
    "module": "metropolis",
    "name": "Metropolis",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "MetropolisDEX",
    "url": "https://metropolis.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "IoTeX",
    "chains": Array [
      "iotex",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1241",
    },
    "description": "mimo is a decentralized liquidity protocol that will fuel the next wave of decentralized finance (DeFi) on IoTeX. mimo’s vision is to empower next-gen DeFi products that utilize our state-of-the-art automated liquidity protocol and the IoTeX's lightning-fast speed, low gas fees, and cross-chain capabilities.",
    "disabled": false,
    "displayName": "Mimo",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1241",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mimo.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mimo",
    "module": "mimo",
    "name": "Mimo",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mimoprotocol",
    "url": "https://mimo.exchange",
  },
  Object {
    "address": "ethpow:******************************************",
    "audit_links": Array [
      "https://github.com/interfinetwork/project-delivery-data/blob/main/Minerswap/MinerSwap_AuditReport_InterFi.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "EthereumPoW",
    "chains": Array [
      "ethpow",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2233",
    },
    "description": "MinerSwap is the first hybrid StableSwap / Uniswap V2 DEX on EthereumPoW. Swap, farm, and stake tokens to earn MSP and earn additional yields.",
    "disabled": false,
    "displayName": "MinerSwap",
    "enabled": false,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "2233",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/minerswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/minerswap",
    "module": "minerswap",
    "name": "MinerSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MSP",
    "twitter": "minerswapfi",
    "url": "https://minerswap.fi",
  },
  Object {
    "address": "cardano:asset1d9v7aptfvpx7we2la8f25kwprkj2ma5rp6uwzv",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Cardano",
    "chains": Array [
      "cardano",
    ],
    "cmcId": "12787",
    "config": Object {
      "enabled": true,
      "id": "1494",
    },
    "description": "Minswap aims to be the best liquidity provider on the market by integrating the best asset pool models from across the DEX ecosystem into one protocol.",
    "disabled": false,
    "displayName": "Minswap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "minswap",
    "id": "1494",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/minswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/minswap",
    "module": "minswap",
    "name": "Minswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MIN",
    "twitter": "MinswapDEX",
    "url": "https://minswap.org",
  },
  Object {
    "address": "smartbch:******************************************",
    "audit_links": Array [
      "https://docs.mistswap.fi/MistSwap-0xguard-audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "smartBCH",
    "chains": Array [
      "smartbch",
    ],
    "cmcId": "1626",
    "config": Object {
      "enabled": true,
      "id": "748",
    },
    "description": "Trade, launch, stake, farm, invest, automate, build on the premier DeFi platform of smartBCH",
    "disabled": false,
    "displayName": "MistSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "mistswap",
    "id": "748",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mistswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mistswap",
    "module": "mistswap",
    "name": "MistSwap",
    "protocolType": undefined,
    "symbol": "MIST",
    "twitter": "mistswapdex",
    "url": "https://mistswap.fi",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2015",
    },
    "description": "MM Finance swap on Polygon network",
    "disabled": false,
    "displayName": "MM Stableswap Polygon",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2015",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mm-stableswap-polygon.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mm-stableswap-polygon",
    "module": "mm-stableswap-polygon",
    "name": "MM Stableswap Polygon",
    "oracles": Array [],
    "parentProtocol": "MM Finance",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "MMFcrypto",
    "url": "https://polymm.finance",
  },
  Object {
    "address": "celo:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Celo",
    "chains": Array [
      "celo",
    ],
    "cmcId": "14099",
    "config": Object {
      "enabled": true,
      "id": "588",
    },
    "description": "A cross-chain stableswap protocol built on Celo.",
    "disabled": false,
    "displayName": "Mobius Money",
    "enabled": true,
    "forkedFrom": Array [
      "Saddle Finance",
    ],
    "gecko_id": "mobius-money",
    "id": "588",
    "logo": "https://icons.llama.fi/mobius-money.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mobius-money",
    "module": "mobius-money",
    "name": "Mobius Money",
    "protocolType": undefined,
    "symbol": "MOBI",
    "twitter": "MobiusMoney",
    "url": "https://www.mobius.money",
  },
  Object {
    "address": "kcc:******************************************",
    "audit_links": Array [
      "https://github.com/MojitoFinance/mojito-swap-farm/blob/main/doc/PeckShield-Audit-Report-Mojito-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Kucoin",
    "chains": Array [
      "kcc",
    ],
    "cmcId": "1521",
    "config": Object {
      "enabled": true,
      "id": "1181",
    },
    "description": "Mojito Finance is a decentralized exchange running on KCC, with lots of features that let you earn and win tokens. It is fast, cheap and easily accessible as it is not difficult to use. It has currently opened Trade ,Bar (Farm) and Wine Pools. Other functions are on the way. And it will continue to launch interesting functions such as Cocktail Tickets and NFT in the future.",
    "disabled": false,
    "displayName": "MojitoSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "mojitoswap",
    "id": "1181",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mojitoswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mojitoswap",
    "module": "mojitoswap",
    "name": "MojitoSwap",
    "protocolType": undefined,
    "symbol": "MJT",
    "twitter": "MojitoSwap",
    "url": "https://www.mojitoswap.finance",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Conflux",
    "chains": Array [
      "conflux",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1942",
    },
    "description": "MoonSwap: High speed,0 GAS AMM DEX Based on Ethereum and Conflux.",
    "disabled": false,
    "displayName": "Moon Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "moonswap",
    "id": "1942",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/moon-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/moon-swap",
    "module": "moon-swap",
    "name": "Moon Swap",
    "protocolType": undefined,
    "symbol": "MOON",
    "twitter": "imMoonSwap",
    "url": "https://moonswap.fi",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://dapp.org.uk/reports/mooniswap.html",
      "https://mooniswap.exchange/docs/mooniswap-audit-report-2.pdf",
      "https://mooniswap.exchange/docs/mooniswap-audit-report-3.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1102",
    "config": Object {
      "enabled": true,
      "id": "1053",
    },
    "description": "Next gen. AMM protocol by 1inch! Redistributes earnings to liquidity providers, capitalizes on user slippages and protects traders from front-running attacks.",
    "disabled": false,
    "displayName": "MooniSwap",
    "enabled": true,
    "gecko_id": null,
    "id": "1053",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mooniswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mooniswap",
    "module": "mooniswap",
    "name": "MooniSwap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mooniswap",
    "url": "https://mooniswap.exchange/#/swap",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://morpheusswap.gitbook.io/morpheus-swap/security/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "11896",
    "config": Object {
      "enabled": true,
      "id": "581",
    },
    "description": "Morpheus Swap is a decentralized exchange that's powered by Fantom Opera. By staking our core token, PILLS, users are entitled to a percentage of all protocol revenue, paid out in other tokens.",
    "disabled": false,
    "displayName": "Morpheus Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "morpheus-token",
    "id": "581",
    "logo": "https://icons.llama.fi/morpheus-swap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/morpheus-swap",
    "module": "morpheus-swap",
    "name": "Morpheus Swap",
    "protocolType": undefined,
    "symbol": "PILLS",
    "twitter": "MorpheusSwap",
    "url": "https://morpheusswap.finance",
  },
  Object {
    "address": "cardano:8a1cfae21368b8bebbbed9800fec304e95cce39a2a57dc35e2e3ebaa.MILK",
    "audit_links": Array [
      "https://github.com/mlabs-haskell/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "smartBCH",
    "chains": Array [
      "milkomeda",
      "cardano",
    ],
    "cmcId": "17275",
    "config": Object {
      "enabled": true,
      "id": "747",
    },
    "description": "A live and operating Orderbook and AMM DEX on Cardano L1 and Milkomeda - based on the simplistic, research-driven order book protocol tailored for Cardano UTXOs.",
    "disabled": false,
    "displayName": "MuesliSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "muesliswap-milk",
    "id": "747",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/muesliswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/muesliswap",
    "module": "muesliswap",
    "name": "MuesliSwap",
    "protocolType": undefined,
    "symbol": "MILK",
    "twitter": "MuesliSwapTeam",
    "url": "https://muesliswap.com",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "optimism",
    ],
    "cmcId": "23038",
    "config": Object {
      "enabled": true,
      "id": "2361",
    },
    "description": "Bring unparalleled Swap and Perpetual trading experience to FantomFDN users",
    "disabled": false,
    "displayName": "Mummy Finance",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "mummy-finance",
    "governanceID": Array [
      "snapshot:mmyvote.eth",
    ],
    "id": "2361",
    "listedAt": 1671034969,
    "logo": "https://icons.llama.fi/mummy-finance.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/mummy-finance",
    "module": "mummy-finance",
    "name": "Mummy Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MMY",
    "twitter": "mummyftm",
    "url": "https://www.mummy.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Starknet",
    "chains": Array [
      "starknet",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2367",
    },
    "description": "an AMM on top of StarkNet",
    "disabled": false,
    "displayName": "mySwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2367",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/myswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/myswap",
    "module": "myswap",
    "name": "mySwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mySwapxyz",
    "url": "https://www.myswap.xyz",
  },
  Object {
    "address": "metis:******************************************",
    "audit_links": Array [
      "https://docs.netswap.io/security/security-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Metis",
    "chains": Array [
      "metis",
    ],
    "cmcId": "16615",
    "config": Object {
      "enabled": true,
      "id": "1140",
    },
    "description": "Netswap is a decentralized exchange (DEX) which runs on Metis Andromeda (Layer2), uses the same AMM model as Uniswap and has a governance token NETT",
    "disabled": false,
    "displayName": "NetSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "netswap",
    "id": "1140",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/netswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/netswap",
    "module": "netswap",
    "name": "NetSwap",
    "protocolType": undefined,
    "symbol": "NETT",
    "twitter": "netswapofficial",
    "url": "https://netswap.io/#/home",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/nomiswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "834",
    "config": Object {
      "enabled": true,
      "id": "1823",
    },
    "description": "Nomiswap is a decentralized exchange platform with a binary referral system and the lowest platform transaction fees.",
    "disabled": false,
    "displayName": "Nomiswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "nominex",
    "id": "1823",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/nomiswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/nomiswap",
    "module": "nomiswap",
    "name": "Nomiswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "NMX",
    "twitter": "Nomiswap",
    "url": "https://nomiswap.io",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/okex/OKCSwap/tree/main/audits/oklink",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "OKExChain",
    "chains": Array [
      "okexchain",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2099",
    },
    "description": "OKC Swap is the official AMM dex based on OKX Chain.",
    "disabled": false,
    "displayName": "OKCSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2099",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/okcswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/okcswap",
    "module": "okcswap",
    "name": "OKCSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": null,
    "url": "https://www.okx.com/okc/swap",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2534",
    },
    "description": "A DEX for capital-efficiency, best-price, and zero slippage.",
    "disabled": false,
    "displayName": "OnePunchSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2534",
    "logo": "https://icons.llama.fi/onepunchswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/onepunch",
    "module": "onepunch",
    "name": "OnePunchSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "OnePunchSwap",
    "url": "https://www.onepunch.finance",
  },
  Object {
    "address": "boba:******************************************",
    "audit_links": Array [
      "https://github.com/OolongSwap/oolongswap-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Boba",
    "chains": Array [
      "boba",
    ],
    "cmcId": "15072",
    "config": Object {
      "enabled": true,
      "id": "794",
    },
    "description": "As the liquidity hub of Boba Network, OolongSwap is a DEX that is...well, not satisfied with being just an ordinary, everyday DEX. We don't want to be just another AMM, we want the best of both worlds: safe, smooth while also being fun, revolutionary, and most importantly, irreplaceable!",
    "disabled": false,
    "displayName": "OolongSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "oolongswap",
    "id": "794",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/oolongswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/oolongswap",
    "module": "oolongswap",
    "name": "OolongSwap",
    "protocolType": undefined,
    "symbol": "OLO",
    "twitter": "oolongswap",
    "url": "https://oolongswap.com/#/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2322",
    },
    "description": "OpenBook is a community-led fork of the Serum V3 program. The CLOB formerly known as Serum V3.",
    "disabled": false,
    "displayName": "OpenBook",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2322",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/openbook.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/openbook",
    "module": "openbook",
    "name": "OpenBook",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "openbookdex",
    "url": "https://openserum.io",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/OpenLeverageDev/openleverage-contracts/blob/main/audits/REP-OpenLeverage-Protocol-2021-06-24.pdf",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-OpenLeverage-1.0.1.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "bsc",
      "kcc",
      "ethereum",
    ],
    "cmcId": "1441",
    "config": Object {
      "enabled": true,
      "id": "1208",
    },
    "description": "OpenLeverage is a permissionless margin trading protocol that enables traders or other applications to be long or short on any trading pair on DEXs efficiently and securely.",
    "disabled": false,
    "displayName": "OpenLeverage",
    "enabled": true,
    "gecko_id": "openleverage",
    "governanceID": Array [
      "snapshot:xole.eth",
    ],
    "id": "1208",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/openleverage.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/openleverage",
    "module": "openleverage",
    "name": "OpenLeverage",
    "protocolType": undefined,
    "symbol": "OLE",
    "twitter": "OpenLeverage",
    "url": "https://openleverage.finance",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_links": Array [
      "https://github.com/opxfinance/opx-contracts/blob/caf9ef6d2695b45fc8182d2094c334951f7c525d/audits/Quantstamp_Audit_Report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
    ],
    "cmcId": "22673",
    "config": Object {
      "enabled": true,
      "id": "2256",
    },
    "description": "OPX (Decentralized Spot & Perpetual Exchange) - the next DEX generation in  Optimizing Investment Efficiency & Leverage Trading",
    "disabled": false,
    "displayName": "OPX Finance",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "opx-finance",
    "id": "2256",
    "listedAt": 1667818926,
    "logo": "https://icons.llama.fi/opx-finance.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/opx-finance",
    "module": "opx-finance",
    "name": "OPX Finance",
    "oracles": Array [
      "SEDA",
    ],
    "protocolType": undefined,
    "symbol": "OPX",
    "twitter": "opxfinance",
    "url": "https://www.opx.finance/#/trade",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Orai",
    "chains": Array [
      "orai",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2564",
    },
    "description": "Developed by Oraichain, OraiDEX is a CosmWasm smart contract-based decentralized exchange with multi-chain interoperability & optimal speed.",
    "disabled": false,
    "displayName": "OraiDEX",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "oraidex",
    "id": "2564",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/oraidex.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/oraidex",
    "module": "oraidex",
    "name": "OraiDEX",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ORAIX",
    "twitter": "oraidex",
    "url": "https://oraidex.io",
  },
  Object {
    "address": "solana:orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "11165",
    "config": Object {
      "enabled": true,
      "id": "283",
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "Orca",
    "enabled": true,
    "gecko_id": "orca",
    "id": "283",
    "logo": "https://icons.llama.fi/orca.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/orca",
    "module": "orca",
    "name": "Orca",
    "openSource": false,
    "protocolType": undefined,
    "symbol": "ORCA",
    "twitter": "orca_so",
    "url": "https://www.orca.so",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://www.certik.com/projects/orderly-network",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Near",
    "chains": Array [
      "near",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2264",
    },
    "description": "Orderly Network is a permissionless and modular protocol that brings high throughput, low latency, low fees, tight spreads and composability for DeFi builders.",
    "disabled": false,
    "displayName": "Orderly Network",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2264",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/orderly-network.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/orderly-network",
    "module": "orderly-network",
    "name": "Orderly Network",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "OrderlyNetwork",
    "url": "https://orderly.network",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Osmosis",
    "chains": Array [
      "cosmos",
    ],
    "cmcId": "12220",
    "config": Object {
      "enabled": true,
      "id": "383",
    },
    "description": "Osmosis DEX is the advanced automated market maker (AMM) protocol at the core of the Osmosis blockchain.",
    "disabled": false,
    "displayName": "Osmosis DEX",
    "enabled": true,
    "gecko_id": "osmosis",
    "id": "383",
    "logo": "https://icons.llama.fi/osmosis-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/osmosis",
    "module": "osmosis",
    "name": "Osmosis DEX",
    "protocolType": undefined,
    "symbol": "OSMO",
    "twitter": "osmosiszone",
    "url": "https://osmosis.zone/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Obyte",
    "chains": Array [
      "obyte",
    ],
    "cmcId": "1492",
    "config": Object {
      "enabled": true,
      "id": "1778",
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Obyte.",
    "disabled": false,
    "displayName": "Oswap AMM",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "byteball",
    "id": "1778",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/oswap-amm.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/oswap",
    "module": "oswap",
    "name": "Oswap AMM",
    "oracles": Array [],
    "parentProtocol": "Oswap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ObyteOrg",
    "url": "https://oswap.io",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/runtimeverification/publications/blob/main/reports/smart-contracts/Pact_Fi.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Algorand",
    "chains": Array [
      "algorand",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1468",
    },
    "description": "Pact is a decentralised Automated Market Maker (AMM) built on the Algorand protocol, offering deep liquidity and low transaction fees.",
    "disabled": false,
    "displayName": "Pact",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1468",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pact.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pact",
    "module": "pact",
    "name": "Pact",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "pact_fi",
    "url": "https://app.pact.fi",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://dapps.padswap.exchange/pad_audit_report.pdf",
      "https://toad.network/toad_audit_report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "moonriver",
      "bsc",
      "moonbeam",
    ],
    "cmcId": "9983",
    "config": Object {
      "enabled": true,
      "id": "644",
    },
    "description": "Cross Chain AMM, Advanced Liquidity Solutions, DeFi product solves, Yield farming.",
    "disabled": false,
    "displayName": "PadSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "toad-network",
    "id": "644",
    "logo": "https://icons.llama.fi/padswap.svg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/padswap",
    "module": "padswap",
    "name": "PadSwap",
    "protocolType": undefined,
    "symbol": "TOAD",
    "twitter": "ToadNetwork",
    "url": "https://padswap.exchange",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/PaintSwap/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "13229",
    "config": Object {
      "enabled": true,
      "id": "421",
    },
    "description": "PaintSwap Finance has the premier open NFT Marketplace on Fantom. It has a unique and sustainable yield farming AMM platform where 50% of farming rewards are locked for 3 months in the Art Gallery. The first project to create a video game built on the Fantom network, as well as many other exciting features in the works.",
    "disabled": false,
    "displayName": "Paint Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "paint-swap",
    "id": "421",
    "logo": "https://icons.llama.fi/paint-swap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/paint-swap",
    "module": "paint-swap",
    "name": "Paint Swap",
    "protocolType": undefined,
    "symbol": "BRUSH",
    "twitter": "paint_swap",
    "url": "https://paintswap.finance",
  },
  Object {
    "address": "astar:******************************************",
    "audit_links": Array [
      "https://paladinsec.co/projects/pandora-swap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Astar",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "1687",
    "config": Object {
      "enabled": true,
      "id": "1698",
    },
    "description": "PandoraSwap has the most complete and holistic ecosystem on Astar Chain with its AMM/DEX, Yield Optimizer & NFT.",
    "disabled": false,
    "displayName": "Pandora Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1698",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pandora-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pandora",
    "module": "pandora",
    "name": "Pandora Swap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "PANDORA",
    "twitter": "pandora_swap",
    "url": "https://pandoraswapxyz.org/",
  },
  Object {
    "address": "klaytn:******************************************",
    "audit_links": Array [
      "https://github.com/pangea-protocol/pangea-core/blob/main/audits/%5BHAECHI%20AUDIT%5D%20Smart%20Contract%20Audit%20Reports%20for%20Pangea.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Klaytn",
    "chains": Array [
      "klaytn",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1987",
    },
    "description": "The First Concentrated Liquidity DEX on Klaytn.",
    "disabled": false,
    "displayName": "Pangea Swap",
    "enabled": true,
    "forkedFrom": Array [
      "SushiSwap",
    ],
    "gecko_id": null,
    "id": "1987",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pangea-swap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pangea-swap",
    "module": "pangea-swap",
    "name": "Pangea Swap",
    "oracles": Array [
      "Witnet",
    ],
    "protocolType": undefined,
    "symbol": "STONE",
    "twitter": "Pangea_Swap",
    "url": "https://pangeaswap.com",
  },
  Object {
    "address": "avax:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "8422",
    "config": Object {
      "enabled": true,
      "id": "246",
    },
    "description": "Pangolin is a community-driven DEX that runs on multiple blockchains. ",
    "disabled": false,
    "displayName": "Pangolin",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "pangolin",
    "governanceID": Array [
      "snapshot:pangolindex.eth",
    ],
    "id": "246",
    "logo": "https://icons.llama.fi/pangolin.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pangolin",
    "module": "pangolin",
    "name": "Pangolin",
    "protocolType": undefined,
    "symbol": "PNG",
    "twitter": "pangolindex",
    "url": "https://pangolin.exchange",
  },
  Object {
    "address": "eos:MLNK-eos-swap.pcash",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "EOS",
    "chains": Array [
      "eos",
    ],
    "cmcId": "20603",
    "config": Object {
      "enabled": true,
      "id": "1452",
    },
    "description": "PayCash – among other things – is a universal EOS blockchain-based decentralized p2p crypto-to-fiat gateway (exchange) facilitating easy entry and exit to and from the cryptocurrency universe.",
    "disabled": false,
    "displayName": "PayCash",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "malinka",
    "id": "1452",
    "language": "C++",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/paycash.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/paycash",
    "module": "paycash",
    "name": "PayCash",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MLNK",
    "twitter": "PayCashTweet",
    "url": "https://paycashswap.com",
  },
  Object {
    "address": "syscoin:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Syscoin",
    "chains": Array [
      "syscoin",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1432",
    },
    "description": "Swap, earn, and build with the leading decentralized crypto trading protocol on Syscoin.",
    "disabled": false,
    "displayName": "PegaSys",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "pegasys",
    "id": "1432",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pegasys.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pegasys",
    "module": "pegasys",
    "name": "PegaSys",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "PSYS",
    "twitter": "PegasysDEX",
    "url": "https://pegasys.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1575",
    },
    "description": "A Liquidity-as-a-Service Platform, built on Solana.",
    "disabled": false,
    "displayName": "Penguin",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1575",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/penguin.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/penguin",
    "module": "penguin",
    "name": "Penguin",
    "oracles": Array [
      "Coingecko",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "png_fi",
    "url": "https://png.fi",
  },
  Object {
    "address": "cronos:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Cronos",
    "chains": Array [
      "cronos",
    ],
    "cmcId": "17640",
    "config": Object {
      "enabled": true,
      "id": "847",
    },
    "description": "AMM styled decentralized exchange (DEX) on Cronos ",
    "disabled": false,
    "displayName": "PhotonSwap Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "photonswap",
    "id": "847",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/photonswap-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/photonswap-finance",
    "module": "photonswap-finance",
    "name": "PhotonSwap Finance",
    "protocolType": undefined,
    "symbol": "PHOTON",
    "twitter": "photonswap_fi",
    "url": "https://photonswap.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/TechRate/Smart-Contract-Audits/blob/main/PinkSwap%20Token%20Full%20Smart%20Contract%20Security%20Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "1384",
    "config": Object {
      "enabled": true,
      "id": "367",
    },
    "description": "Sale, swap, earn, lock all on one decentralized, community driven platform. Earn PinkS by staking LP & tokens. Welcome to PinkArmy Family!",
    "disabled": false,
    "displayName": "PinkSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "pinkswap-token",
    "id": "367",
    "logo": "https://icons.llama.fi/pinkswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pinkswap",
    "module": "pinkswap",
    "name": "PinkSwap",
    "protocolType": undefined,
    "symbol": "PINKS",
    "twitter": "pinkmoonfinance",
    "url": "https://www.pinkswap.finance/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://omniscia.io/platypus-finance-core-implementation/",
      "https://hacken.io/audits/#platypus_finance",
      "https://omniscia.io/platypus-finance-governance-staking/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "16231",
    "config": Object {
      "enabled": true,
      "id": "944",
    },
    "description": "Platypus combined stableswap and stablecoin, masterfully utilizing its underlying assets to bring next-level capital efficiency.",
    "disabled": false,
    "displayName": "Platypus Finance",
    "enabled": true,
    "gecko_id": "platypus-finance",
    "id": "944",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/platypus.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/platypus",
    "module": "platypus",
    "name": "Platypus Finance",
    "protocolType": undefined,
    "symbol": "PTP",
    "twitter": "Platypusdefi",
    "url": "https://platypus.finance",
  },
  Object {
    "address": "tezos:KT1JVjgXPMMSaa6FkzeJcgb8q9cUaLmwaJUX",
    "audit_links": Array [
      "https://github.com/Plenty-network/security-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Tezos",
    "chains": Array [
      "tezos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "490",
    },
    "description": "Plenty is a decentralized exchange on Tezos that offers a seamless trading experience. Users can trade, earn, govern and build on the platform. Plenty features both stable and volatile liquidity pools, enables near-zero slippage trades and easy bridging from Ethereum and Polygon.",
    "disabled": false,
    "displayName": "Plenty",
    "enabled": true,
    "gecko_id": null,
    "id": "490",
    "logo": "https://icons.llama.fi/plenty.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/plenty",
    "module": "plenty",
    "name": "Plenty",
    "protocolType": undefined,
    "symbol": "PLY",
    "twitter": "plenty_network",
    "url": "https://www.plenty.network",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://docs.polycat.finance/security#audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "10134",
    "config": Object {
      "enabled": true,
      "id": "499",
    },
    "description": "​Polycat is a value-oriented, sustainable and decentralized hybrid yield optimizer (yield farm and yield aggregator) running on the Polygon blockchain.",
    "disabled": false,
    "displayName": "Polycat",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "polycat-finance",
    "id": "499",
    "logo": "https://icons.llama.fi/polycat.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/polycat",
    "module": "polycat",
    "name": "Polycat",
    "parentProtocol": "Polycat Finance",
    "protocolType": undefined,
    "symbol": "FISH",
    "twitter": "PolycatFinance",
    "url": "https://polycat.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Rangers",
    "chains": Array [
      "rpg",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2657",
    },
    "description": "The first DEX for traders and gamers on Rangers Protocol",
    "disabled": false,
    "displayName": "PonytaSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2657",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ponytaswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ponytaswap",
    "module": "ponytaswap",
    "name": "PonytaSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ponytaswap",
    "url": "https://www.ponytaswap.finance/swap",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/protofi",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "17594",
    "config": Object {
      "enabled": true,
      "id": "1306",
    },
    "description": "The First AMM on Fantom in which you are the owner of the Protocol",
    "disabled": false,
    "displayName": "ProtoFi",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "protofi",
    "id": "1306",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/protofi.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/protofi",
    "module": "protofi",
    "name": "ProtoFi",
    "protocolType": undefined,
    "symbol": "PROTO",
    "twitter": "ProtoFiProtocol",
    "url": "https://fantombank.protofi.app/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/PYESWAPV2",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2109",
    },
    "description": "The New Swap 2.0 Protocol",
    "disabled": false,
    "displayName": "PYEswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2109",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/pyeswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/pyeswap",
    "module": "pyeswap",
    "name": "PYEswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "PYE",
    "twitter": "PyeEcosystem",
    "url": "https://pyeswap.com/",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://contractsecurity.io/quick-audit-report/",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "19966",
    "config": Object {
      "enabled": true,
      "id": "306",
      "protocolsData": Object {
        "v2": Object {
          "displayName": "Quickswap V2",
          "enabled": true,
          "id": "306",
        },
        "v3": Object {
          "enabled": true,
          "id": "2239",
        },
      },
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "Quickswap V2",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "quickswap",
    "id": "306",
    "logo": "https://icons.llama.fi/quickswap-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/quickswap",
    "module": "quickswap",
    "name": "Quickswap Dex",
    "parentProtocol": "Quickswap",
    "protocolType": undefined,
    "symbol": "QUICK",
    "twitter": "QuickswapDEX",
    "url": "https://quickswap.exchange/",
    "versionKey": "v2",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://contractsecurity.io/quick-audit-report/",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
      "dogechain",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "306",
      "protocolsData": Object {
        "v2": Object {
          "displayName": "Quickswap V2",
          "enabled": true,
          "id": "306",
        },
        "v3": Object {
          "enabled": true,
          "id": "2239",
        },
      },
    },
    "description": "QuickSwap is a decentralized exchange that runs on Polygon Network to offer cheaper and faster transactions. Its automated market maker integrates upgradeable smart contracts on Ethereum and renders intermediaries obsolete. The exchange is based on open-source software and prioritizes decentralization, censorship resistance and security. It benefits from ultra-low gas prices compared to Ethereum, as well as fast and simple trading execution",
    "disabled": false,
    "displayName": "Quickswap V3",
    "enabled": true,
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": null,
    "id": "306",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/quickswap-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/quickswap",
    "module": "quickswap",
    "name": "Quickswap V3",
    "oracles": Array [
      "Witnet",
    ],
    "parentProtocol": "Quickswap",
    "protocolType": undefined,
    "symbol": "QUICK",
    "twitter": "QuickswapDEX",
    "url": "https://quickswap.exchange/",
    "versionKey": "v3",
  },
  Object {
    "address": "tezos:KT193D4vozYnhGJQVtw7CoxxqphqUEEwK6Vb",
    "audit_links": Array [
      "https://leastauthority.com/static/publications/LeastAuthority_Tezos_Foundation_QuipuSwap_Smart_Contracts_Final_Audit_Report.pdf",
      "https://github.com/runtimeverification/publications/blob/main/reports/smart-contracts/quipuswap-stableswap.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Tezos",
    "chains": Array [
      "tezos",
    ],
    "cmcId": "13316",
    "config": Object {
      "enabled": true,
      "id": "513",
    },
    "description": "QuipuSwap is an open-source protocol that provides an interface for the seamless decentralized exchange of Tezos-based Tokens, stable swap with dividends, and farming features. Liquidity providers may earn fees from trading, baking rewards, or receive rewards from staking tokens on farms. QuipuSwap smart contracts were developed by the MadFish Team and audited by Least Authority and  Runtime Verification companies.",
    "disabled": false,
    "displayName": "QuipuSwap",
    "enabled": true,
    "gecko_id": "quipuswap-governance-token",
    "id": "513",
    "logo": "https://icons.llama.fi/quipuswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/quipuswap",
    "module": "quipuswap",
    "name": "QuipuSwap",
    "protocolType": undefined,
    "symbol": "QUIPU",
    "twitter": "QuipuSwap",
    "url": "https://quipuswap.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/radioshack",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "polygon",
      "bsc",
      "ethereum",
      "avax",
      "fantom",
    ],
    "cmcId": "19006",
    "config": Object {
      "enabled": true,
      "id": "1616",
    },
    "description": "Uniswap fork",
    "disabled": false,
    "displayName": "RadioShack",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "radioshack",
    "id": "1616",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/radioshack.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/radioshack",
    "module": "radioshack",
    "name": "RadioShack",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "RADIO",
    "twitter": "RadioShack",
    "url": "https://www.radioshack.org/",
  },
  Object {
    "address": "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "8526",
    "config": Object {
      "enabled": true,
      "id": "214",
    },
    "description": "An on-chain order book AMM powering the evolution of DeFi.",
    "disabled": false,
    "displayName": "Raydium",
    "enabled": true,
    "gecko_id": "raydium",
    "id": "214",
    "logo": "https://icons.llama.fi/raydium.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/raydium",
    "module": "raydium",
    "name": "Raydium",
    "openSource": false,
    "protocolType": undefined,
    "symbol": "RAY",
    "twitter": "RaydiumProtocol",
    "url": "https://raydium.io",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Near",
    "chains": Array [
      "near",
    ],
    "cmcId": "11809",
    "config": Object {
      "enabled": true,
      "id": "541",
    },
    "description": "Ref Finance is a core projects in the DeFi ecosystem on Near Protocol. Its main objective is to bring together the core components of DeFi, namely, decentralized exchange (DEX), lending protocol, synthetic asset issuer, and more",
    "disabled": false,
    "displayName": "Ref Finance",
    "enabled": true,
    "gecko_id": "ref-finance",
    "id": "541",
    "logo": "https://icons.llama.fi/ref-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ref-finance",
    "module": "ref-finance",
    "name": "Ref Finance",
    "protocolType": undefined,
    "symbol": "REF",
    "twitter": "finance_ref",
    "url": "https://app.ref.finance",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://docs.rubicon.finance/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "799",
    },
    "description": "Democratizing the open order book and bringing decentralized exchange to the masses. Built on Ethereum L2.",
    "disabled": false,
    "displayName": "Rubicon",
    "enabled": true,
    "gecko_id": null,
    "id": "799",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/rubicon.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/rubicon",
    "module": "rubicon",
    "name": "Rubicon",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "rubicondefi",
    "url": "https://www.rubicon.finance",
  },
  Object {
    "address": "solana:Saber2gLauYim4Mvftnrasomsv6NvAuncvMEZwcLpD1",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "11181",
    "config": Object {
      "enabled": true,
      "id": "419",
    },
    "description": "Saber is the first automated market maker optimized for trading pegged assets on Solana. Our protocol enables Solana users and applications to efficiently trade between stable pairs of assets, as well as earn yields by providing liquidity to the platform.",
    "disabled": false,
    "displayName": "Saber",
    "enabled": true,
    "gecko_id": "saber",
    "id": "419",
    "logo": "https://icons.llama.fi/saber.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/saber",
    "module": "saber",
    "name": "Saber",
    "protocolType": undefined,
    "symbol": "SBR",
    "twitter": "Saber_HQ",
    "url": "https://saber.so",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/saddle-finance/saddle-audits/blob/master/10-29-2020_Certik.pdf",
      "https://blog.openzeppelin.com/saddle-contracts-audit",
      "https://github.com/saddle-finance/saddle-audits/blob/master/12-09-2020_Quantstamp.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "ethereum",
    ],
    "cmcId": "14791",
    "config": Object {
      "enabled": true,
      "id": "202",
    },
    "description": "Saddle is an automated market maker for pegged value crypto assets.",
    "disabled": false,
    "displayName": "Saddle Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "saddle-finance",
    "governanceID": Array [
      "snapshot:saddlefinance.eth",
    ],
    "id": "202",
    "logo": "https://icons.llama.fi/saddle-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/saddle-finance",
    "module": "saddle-finance",
    "name": "Saddle Finance",
    "protocolType": undefined,
    "symbol": "SDL",
    "twitter": "saddlefinance",
    "url": "https://saddle.finance/",
  },
  Object {
    "address": "solana:SSwapUtytfBdBn1b9NUGG6foMVPtcWgpRU32HToDUZr",
    "audit_links": Array [
      "https://app.inspex.co/library/saros-finance#?scope=saros-finance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1262",
    },
    "description": "Saros Finance is the ultimate DeFi platform native to Solana, with trading, staking, and yield farming services. Built by Coin98 Labs, Saros Finance aims to adopt millions of users to DeFi.",
    "disabled": false,
    "displayName": "Saros",
    "enabled": true,
    "gecko_id": null,
    "id": "1262",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/saros.svg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/saros",
    "module": "saros",
    "name": "Saros",
    "openSource": false,
    "protocolType": undefined,
    "symbol": "SAROS",
    "twitter": "Saros_Finance",
    "url": "https://saros.finance",
  },
  Object {
    "address": "hedera:0.0.731861",
    "audit_links": Array [
      "https://docs.saucerswap.finance/resources/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Hedera",
    "chains": Array [
      "hedera",
    ],
    "cmcId": "21914",
    "config": Object {
      "enabled": true,
      "id": "1979",
    },
    "description": "SaucerSwap is an automated market maker protocol that leverages the Hedera Smart Contract Service to include Solidity smart contract integration with the Hedera Token Service.",
    "disabled": false,
    "displayName": "SaucerSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "saucerswap",
    "id": "1979",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/saucerswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/saucerswap",
    "module": "saucerswap",
    "name": "SaucerSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SAUCE",
    "twitter": "SaucerSwapLabs",
    "url": "https://www.saucerswap.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": null,
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "6187",
    "config": Object {
      "enabled": true,
      "id": "145",
      "includedVolume": Array [
        "raydium",
      ],
    },
    "description": "Serum is a decentralized exchange (DEX) and ecosystem that brings unprecedented speed and low transaction costs to decentralized finance.",
    "disabled": false,
    "displayName": "Serum",
    "enabled": true,
    "gecko_id": "serum",
    "id": "145",
    "includedVolume": Array [
      "raydium",
    ],
    "logo": "https://icons.llama.fi/serum.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/serum",
    "module": "serum",
    "name": "Serum",
    "protocolType": undefined,
    "symbol": "SRM",
    "twitter": "ProjectSerum",
    "url": "https://projectserum.com/",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "1",
    "category": "Dexs",
    "chain": "SXnetwork",
    "chains": Array [
      "sx",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1828",
    },
    "description": "SharkSwap is a decentralized trading platform on the SX network.",
    "disabled": false,
    "displayName": "SharkSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "shark",
    "id": "1828",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sharkswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sharkswap",
    "module": "sharkswap",
    "name": "SharkSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SHARK",
    "twitter": "SharkSwap_xyz",
    "url": "https://sharkswap.xyz",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": null,
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "7499",
    "config": Object {
      "enabled": true,
      "id": "133",
    },
    "description": "DeFi hub for users & builders. Breakthrough accounting architecture and AMM engine.",
    "disabled": false,
    "displayName": "Shell Protocol",
    "enabled": true,
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:shellprotocol.eth",
    ],
    "id": "133",
    "logo": "https://icons.llama.fi/shell-protocol.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/shell-protocol",
    "module": "shell-protocol",
    "name": "Shell Protocol",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ShellProtocol",
    "url": "https://www.shellprotocol.io/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/shib",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "11865",
    "config": Object {
      "enabled": true,
      "id": "397",
    },
    "description": "SHIB, LEASH, and BONE, come together to create ShibaSwap, the next evolution in DeFi platforms. ShibaSwap gives users the ability to DIG (provide liquidity), BURY (stake), and SWAP tokens to gain WOOF Returns through our sophisticated and innovative passive income reward system.",
    "disabled": false,
    "displayName": "ShibaSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "bone-shibaswap",
    "id": "397",
    "logo": "https://icons.llama.fi/shibaswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/shibaswap",
    "module": "shibaswap",
    "name": "ShibaSwap",
    "protocolType": undefined,
    "symbol": "BONE",
    "twitter": "ShibaSwapDEX",
    "url": "https://shibaswap.com",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "1366",
    "config": Object {
      "enabled": true,
      "id": "883",
    },
    "description": "SmartDEX is a decentralized exchange developed by the Autonio Team. It is designed to be the world’s first 'smart DEX', giving users the ability to use high-frequency market makers and automated trading algorithms on a decentralized exchange.",
    "disabled": false,
    "displayName": "SmartDEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "883",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/smartdex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/smartdex",
    "module": "smartdex",
    "name": "SmartDEX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "AI_smartdex",
    "url": "https://www.smartdex.app",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1632",
    },
    "description": "AMM on bsc",
    "disabled": false,
    "displayName": "SMBSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1632",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/smbswap.jpeg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/smbswap",
    "module": "smbswap",
    "name": "SMBSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SELF",
    "twitter": "smbswap",
    "url": "https://smbswap.finance/",
  },
  Object {
    "address": "moonriver:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Moonriver",
    "chains": Array [
      "moonriver",
    ],
    "cmcId": "13041",
    "config": Object {
      "enabled": true,
      "id": "551",
    },
    "description": "Solarbeam is a decentralized exchange, providing liquidity and enabling peer-to-peer transactions on the Moonriver Network.",
    "disabled": false,
    "displayName": "Solarbeam",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "solarbeam",
    "id": "551",
    "logo": "https://icons.llama.fi/solarbeam.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/solarbeam",
    "module": "solarbeam",
    "name": "Solarbeam",
    "protocolType": undefined,
    "symbol": "SOLAR",
    "twitter": "Solarbeamio",
    "url": "https://app.solarbeam.io/exchange/swap",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/solarbeam",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1269",
    },
    "description": "Solarflare is a decentralized exchange, providing liquidity and enabling peer-to-peer transactions on the Moonbeam Network.",
    "disabled": false,
    "displayName": "Solarflare",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "solarflare",
    "id": "1269",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solarflare.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/solarflare",
    "module": "solarflare",
    "name": "Solarflare",
    "protocolType": undefined,
    "symbol": "FLARE",
    "twitter": "Solarbeamio",
    "url": "https://solarflare.io",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2528",
    },
    "description": "SolidLizard is an Arbitrum One decentralized exchange that allows swaps at a low cost in swap fees and has a governance structure based on the ve(3,3) system",
    "disabled": false,
    "displayName": "SolidLizard",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "solidlizard",
    "id": "2528",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solidlizard.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/solidlizard",
    "module": "solidlizard",
    "name": "SolidLizard",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SLIZ",
    "twitter": "solidlizardfi",
    "url": "https://solidlizard.finance/",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://github.com/andrecronje/solidly/blob/master/audits/e456a816-3802-4384-894c-825a4177245a.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "18399",
    "config": Object {
      "enabled": true,
      "id": "1407",
    },
    "description": "Solidly allows low cost, near 0 slippage trades on uncorrelated or tightly correlated assets. The protocol incentivizes fees instead of liquidity. Liquidity providers (LPs) are given incentives in the form of token",
    "disabled": false,
    "displayName": "Solidly",
    "enabled": true,
    "gecko_id": "solidly",
    "id": "1407",
    "language": "Solidity",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solidly.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/solidly",
    "module": "solidly",
    "name": "Solidly",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SOLID",
    "twitter": "solidlyexchange",
    "url": "https://solidly.exchange/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/SolidlyLabs/Solidly-Audits/blob/main/audit_solidly.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2400",
    },
    "description": "Self-optimizing DEX combining the best of Curve, Uniswap and ve(3,3)",
    "disabled": false,
    "displayName": "Solidly V2",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "solidlydex",
    "id": "2400",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solidly-v2.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/solidlydex",
    "module": "solidlydex",
    "name": "Solidly V2",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SOLID",
    "twitter": "SolidlyDEX",
    "url": "https://solidly.com",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "13342",
    "config": Object {
      "enabled": true,
      "id": "544",
    },
    "description": "Soul is an algorithmic, cross-chain AMM and P2P lending protocol (on Fantom) built for traders, investors, developers, and visionaries seeking to unlock a universe of open financial applications.",
    "disabled": false,
    "displayName": "SoulSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "soul-swap",
    "id": "544",
    "logo": "https://icons.llama.fi/soulswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/soulswap",
    "module": "soulswap",
    "name": "SoulSwap",
    "protocolType": undefined,
    "symbol": "SOUL",
    "twitter": "SoulSwapFinance",
    "url": "https://soul.sh",
  },
  Object {
    "address": "callisto:******************************************",
    "audit_links": Array [
      "https://soy-finance.gitbook.io/soy-finance/safety-on-yields/soy-finance-security-audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Callisto",
    "chains": Array [
      "callisto",
    ],
    "cmcId": "19879",
    "config": Object {
      "enabled": true,
      "id": "1008",
    },
    "description": "SOY Finance will deploy a set of services on the Callisto Network blockchain, including decentralized swap, yield farming, as well as a stable coin.",
    "disabled": false,
    "displayName": "Soy Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "soy-finance",
    "id": "1008",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/soy-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/soy-finance",
    "module": "soy-finance",
    "name": "Soy Finance",
    "protocolType": undefined,
    "symbol": "SOY",
    "twitter": "Soy_Finance",
    "url": "https://soy.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1755",
    },
    "description": "Spartacus Exchange is a decentralized exchange that is designed for everyone with low fees for both correlated and volatile assets.",
    "disabled": false,
    "displayName": "Spartacus Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "1755",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/spartacus.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/spartacus-exchange",
    "module": "spartacus-exchange",
    "name": "Spartacus Exchange",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "treasury": "spartacus.js",
    "twitter": "Spartacus_Fi",
    "url": "https://app.spartacus.exchange",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/spartan-protocol/resources/blob/master/certik-audit.pdf",
      "https://code423n4.com/reports/2021-07-spartan/",
      "https://www.immunefi.com/bounty/spartanprotocol",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "6992",
    "config": Object {
      "enabled": true,
      "id": "1246",
    },
    "description": "Spartan Protocol is an AMM built on top of Binance Smart Chain utilizing liquidity sensitive fees. Focusing on incentivised liquidity pools and Synths, Spartan Protocol allows users to earn revenue by providing liquidity to pools and forging + staking Synthetic Assets.",
    "disabled": false,
    "displayName": "Spartan",
    "enabled": true,
    "gecko_id": "spartan-protocol-token",
    "id": "1246",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/spartan.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/spartan",
    "module": "spartan",
    "name": "Spartan",
    "protocolType": undefined,
    "symbol": "SPARTA",
    "twitter": "SpartanProtocol",
    "url": "https://spartanprotocol.org",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "cronos",
    ],
    "cmcId": "14461",
    "config": Object {
      "enabled": true,
      "id": "1992",
    },
    "description": "An AIO(All-In-One) solution for trading, farming, staking and holding",
    "disabled": false,
    "displayName": "Sphynx",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "sphynx-labs",
    "id": "1992",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sphynx.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sphynx",
    "module": "sphynx",
    "name": "Sphynx",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SPHYNX",
    "twitter": "SphynxLabs",
    "url": "https://linktr.ee/sphynxlabs",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://github.com/Layer3Org/spiritswap-core/blob/main/SpiritSwap-Core%20Security%20Audit%20Report.pdf",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "311",
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "SpiritSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "311",
    "logo": "https://icons.llama.fi/spiritswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/spiritswap",
    "module": "spiritswap",
    "name": "SpiritSwap AMM",
    "parentProtocol": "SpiritSwap",
    "protocolType": undefined,
    "symbol": "SPIRIT",
    "twitter": "Spirit_Swap",
    "url": "https://app.spiritswap.finance/#/",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/spookyswap",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "9608",
    "config": Object {
      "enabled": true,
      "id": "302",
    },
    "description": "Automated Market Maker.",
    "disabled": false,
    "displayName": "SpookySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "spookyswap",
    "governanceID": Array [
      "snapshot:spookyswap.eth",
    ],
    "id": "302",
    "logo": "https://icons.llama.fi/spookyswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/spookyswap",
    "module": "spookyswap",
    "name": "SpookySwap",
    "protocolType": undefined,
    "symbol": "BOO",
    "treasury": "spookyswap.js",
    "twitter": "SpookySwap",
    "url": "https://spooky.fi/#/",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Stellar",
    "chains": Array [
      "stellar",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "972",
    },
    "description": "We’re fast. We’re free. And we’re the first decentralized crypto platform with global fiat gateways. You can trade bitcoin for Euros for Chinese Yuan on StellarX. That’s not possible anywhere else.",
    "disabled": false,
    "displayName": "StellarX",
    "enabled": true,
    "gecko_id": null,
    "id": "972",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/stellarx.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/stellarx",
    "module": "stellarx",
    "name": "StellarX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "stellarxhq",
    "url": "https://www.stellarx.com",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/stellaswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": "17358",
    "config": Object {
      "enabled": true,
      "id": "1274",
    },
    "description": "Stellaswap is leading DEX on Moonbeam.",
    "disabled": false,
    "displayName": "StellaSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": "stellaswap",
    "id": "1274",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/stellaswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/stellaswap",
    "module": "stellaswap",
    "name": "StellaSwap",
    "protocolType": undefined,
    "symbol": "STELLA",
    "twitter": "StellaSwap",
    "url": "https://stellaswap.com",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Step",
    "chains": Array [
      "step",
    ],
    "cmcId": "21725",
    "config": Object {
      "enabled": true,
      "id": "2312",
    },
    "description": "Dex on Step Network ",
    "disabled": false,
    "displayName": "Step Exchange",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "stepex",
    "id": "2312",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/step-exchange.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/step-exchange",
    "module": "step-exchange",
    "name": "Step Exchange",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "StepApp_",
    "url": "https://app.step.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2556",
    },
    "description": "Swap, farm, stake and win crypto with ZSwap! The future home of crypto on AVAX.",
    "disabled": false,
    "displayName": "Subzero ZSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2556",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/subzero-zswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/subzero-zswap",
    "module": "subzero-zswap",
    "name": "Subzero ZSwap",
    "oracles": Array [],
    "parentProtocol": "Subzero",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "zswapdex",
    "url": "https://zswap.plus",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cardano",
    "chains": Array [
      "cardano",
    ],
    "cmcId": "11986",
    "config": Object {
      "enabled": true,
      "id": "1302",
    },
    "description": "The first native AMM-based decentralized exchange and liquidity provision protocol on Cardano.",
    "disabled": false,
    "displayName": "SundaeSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "sundaeswap",
    "id": "1302",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sundaeswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sundaeswap",
    "module": "sundaeswap",
    "name": "SundaeSwap",
    "protocolType": undefined,
    "symbol": "SUNDAE",
    "twitter": "SundaeSwap",
    "url": "https://www.sundaeswap.finance/",
  },
  Object {
    "address": "tron:TSSMHYeV2uE9qYH95DqyoCuNCzEL1NvU3S",
    "audit_links": Array [
      "https://justswap.io/docs/audit-report_cn.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Tron",
    "chains": Array [
      "tron",
    ],
    "cmcId": "1116",
    "config": Object {
      "enabled": true,
      "id": "690",
    },
    "description": "SUNSwap is a TRON-based decentralized trading protocol for automated liquidity provision and an open financial market accessible to all.",
    "disabled": false,
    "displayName": "SUNSwap",
    "enabled": true,
    "gecko_id": null,
    "id": "690",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sunswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sunswap",
    "module": "sunswap",
    "name": "SUNSwap",
    "parentProtocol": "SUN.io",
    "protocolType": undefined,
    "symbol": "SUN",
    "twitter": "DeFi_JUST",
    "url": "https://sunswap.com/#/home",
  },
  Object {
    "address": "kava:******************************************",
    "audit_links": Array [
      "https://surfswap.gitbook.io/surfswap/contracts/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1868",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "1868",
        },
        "stable-amm": Object {
          "enabled": true,
          "id": "2598",
        },
      },
    },
    "description": "Community DEX on Kava. One-stop shop for the crypto community, enabling peer-to-peer transactions.",
    "disabled": false,
    "displayName": "Surfswap Classic",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1868",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/surfswap-classic.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/surfswap",
    "module": "surfswap",
    "name": "Surfswap Classic",
    "oracles": Array [
      "TWAP",
    ],
    "parentProtocol": "Surfswap",
    "protocolType": undefined,
    "symbol": "TIDE",
    "twitter": "SurfswapDEX",
    "url": "https://surfdex.io",
    "versionKey": "classic",
  },
  Object {
    "address": "kava:******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Surfswap-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1868",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "1868",
        },
        "stable-amm": Object {
          "enabled": true,
          "id": "2598",
        },
      },
    },
    "description": "Community DEX on Kava. One-stop shop for the crypto community, enabling peer-to-peer transactions.",
    "disabled": false,
    "displayName": "Surfswap Stable AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1868",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/surfswap-stable-amm.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/surfswap",
    "module": "surfswap",
    "name": "Surfswap Stable AMM",
    "oracles": Array [
      "TWAP",
    ],
    "parentProtocol": "Surfswap",
    "protocolType": undefined,
    "symbol": "TIDE",
    "twitter": "SurfswapDEX",
    "url": "https://surfdex.io",
    "versionKey": "stable-amm",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-SushiSwap-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
      "fantom",
      "arbitrum",
      "celo",
      "avax",
      "harmony",
      "moonriver",
      "xdai",
      "moonbeam",
      "boba",
      "fuse",
    ],
    "cmcId": "6758",
    "config": Object {
      "enabled": true,
      "id": "119",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "119",
        },
        "trident": Object {
          "enabled": true,
          "id": "2152",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum.
",
    "disabled": false,
    "displayName": "SushiSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "sushi",
    "id": "119",
    "logo": "https://icons.llama.fi/sushiswap.png",
    "methodology": Object {
      "Fees": "SushiSwap charges a flat 0.3% fee",
      "HoldersRevenue": "SUSHI token stakers are entitled to share a 0.05% fee from each trade",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "A 0.05% of each trade goes to token holders",
      "SupplySideRevenue": "Liquidity providers get 0.25% of all trades in their pools",
      "UserFees": "Users pay a 0.3% fee on each trade",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sushiswap",
    "module": "sushiswap",
    "name": "SushiSwap",
    "oracles": Array [],
    "parentProtocol": "Sushi",
    "protocolType": undefined,
    "symbol": "SUSHI",
    "treasury": "sushiswap.js",
    "twitter": "SushiSwap",
    "url": "https://sushi.com/",
    "versionKey": "classic",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
      "optimism",
      "kava",
      "metis",
      "bittorrent",
      "arbitrum",
      "bsc",
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "119",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "119",
        },
        "trident": Object {
          "enabled": true,
          "id": "2152",
        },
      },
    },
    "description": "TRIDENT 🔱 is a newly developed AMM and routing system from SushiSwap (Sushi).",
    "disabled": false,
    "displayName": "Sushi Trident",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "119",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sushi-trident.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sushiswap",
    "module": "sushiswap",
    "name": "Sushi Trident",
    "oracles": Array [],
    "parentProtocol": "Sushi",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "SushiSwap",
    "url": "https://www.sushi.com/swap",
    "versionKey": "trident",
  },
  Object {
    "address": "conflux:******************************************",
    "audit_links": Array [
      "https://drive.google.com/file/d/1zj08NuUuMAO-bikOBvPOsOnPcTC5rdHx/view?usp=sharing",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Conflux",
    "chains": Array [
      "conflux",
    ],
    "cmcId": "19544",
    "config": Object {
      "enabled": true,
      "id": "1660",
    },
    "description": "Swappi is an automated market maker (AMM) based decentralized exchange (DEX) deployed on Conflux Network.",
    "disabled": false,
    "displayName": "Swappi",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "swappi",
    "id": "1660",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/swappi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/swappi",
    "module": "swappi",
    "name": "Swappi",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "PPI",
    "twitter": "SwappiDEX",
    "url": "https://app.swappi.io",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://gateway.ipfs.io/ipfs/QmNspbn2dQgQMQ9uXkMc7Fjf12RUVVJTzB27ywGeLUXXdn",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "xdai",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "292",
    },
    "description": "A governance-enabled automated-market maker with adjustable fees, made by DXdao.",
    "disabled": false,
    "displayName": "Swapr",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "swapr",
    "governanceID": Array [
      "snapshot:swpr.eth",
    ],
    "id": "292",
    "logo": "https://icons.llama.fi/swapr.svg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/swapr",
    "module": "swapr",
    "name": "Swapr",
    "protocolType": undefined,
    "symbol": "SWPR",
    "twitter": "SwaprEth",
    "url": "https://swapr.eth.link/#/swap",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/swapsicle",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "20438",
    "config": Object {
      "enabled": false,
      "id": "1824",
    },
    "description": "Swapsicle is a decentralised exchange built on the avalanche blockchain offering swaps, farming and staking opportunities.",
    "disabled": false,
    "displayName": "Swapsicle",
    "enabled": false,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "swapsicle-pops",
    "id": "1824",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/swapsicle.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/swapsicle",
    "module": "swapsicle",
    "name": "Swapsicle",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "POPS",
    "twitter": "SwapsicIeDEX",
    "url": "https://swapsicle.io",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://www.synfutures.com/peckshield-audit-report-synfutures-v1.1.pdf",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Derivatives",
    "chain": "Ethereum",
    "chains": Array [
      "polygon",
      "arbitrum",
      "ethereum",
      "bsc",
    ],
    "cmcId": "1504",
    "config": Object {
      "enabled": false,
      "id": "2328",
    },
    "description": "SynFutures is a next-generation derivatives exchange focused on creating an open and trustless derivatives market by enabling futures trading on anything, anytime, anywhere. By cultivating a free market and maximizing the variety of tradable assets, SynFutures is lowering the barrier to entry in the derivatives market, creating a more equitable derivatives market.",
    "disabled": false,
    "displayName": "SynFutures",
    "enabled": false,
    "forkedFrom": Array [],
    "gecko_id": "synfutures",
    "id": "2328",
    "listedAt": 1669979746,
    "logo": "https://icons.llama.fi/synfutures.svg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/synfutures",
    "module": "synfutures",
    "name": "SynFutures",
    "oracles": Array [
      "Chainlink",
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "SynFuturesDefi",
    "url": "https://www.synfutures.com/",
  },
  Object {
    "address": "solana:4dmKkXNHdgYsXqBHCuMikNQWwVomZURhYvkkX5c4pQ7y",
    "audit_links": Array [
      "https://github.com/Synthetify/synthetify-landing/blob/master/public/blog/audit/audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "9447",
    "config": Object {
      "enabled": true,
      "id": "731",
    },
    "description": "Synthetify is a decentralized synthetic assets protocol build on the Solana blockchain. The protocol allows for the creation and exchange of synthetic assets that closely track the price of specific assets. Synthetic tokens are based on the SPL-Token standard that gives them the ability to be easily integrated with other DeFi applications like AMM.",
    "disabled": false,
    "displayName": "Synthetify",
    "enabled": true,
    "gecko_id": "synthetify-token",
    "id": "731",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/synthetify.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/synthetify",
    "module": "synthetify",
    "name": "Synthetify",
    "oracles": Array [
      "Pyth",
    ],
    "protocolType": undefined,
    "symbol": "SNY",
    "twitter": "synthetify",
    "url": "https://www.synthetify.io",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/Syrup-Finance/syrup-contracts/tree/main/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "23111",
    "config": Object {
      "enabled": true,
      "id": "2401",
    },
    "description": "Fully Decentralized Spot & Perpetual Exchange. Go long, go short or swap crypto in self-custody, with up to 100x more buying power",
    "disabled": false,
    "displayName": "Syrup Finance",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "syrup-finance",
    "id": "2401",
    "listedAt": 1672682063,
    "logo": "https://icons.llama.fi/syrup-finance.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/syrup-finance",
    "module": "syrup-finance",
    "name": "Syrup Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SRX",
    "twitter": "syrupfinance",
    "url": "https://syrup.finance/",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2178",
    },
    "description": "$TEMPLE can be purchased and sold from our own AMM under the Trade page",
    "disabled": false,
    "displayName": "TempleDAO Trade",
    "enabled": true,
    "gecko_id": null,
    "id": "2178",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/temple-dao.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/templedao-trade",
    "module": "templedao-trade",
    "name": "TempleDAO Trade",
    "parentProtocol": "Temple DAO",
    "protocolType": undefined,
    "symbol": "TEMPLE",
    "twitter": "templedao",
    "url": "https://templedao.link/dapp/trade",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Terra",
    "chains": Array [
      "terra",
    ],
    "cmcId": null,
    "config": Object {
      "disabled": true,
      "enabled": true,
      "id": "491",
    },
    "description": "Terraswap is a Uniswap-inspired automated market-maker (AMM) protocol implemented with smart contracts on the Terra blockchain. This enables a decentralized on-chain exchange for the various assets involved in Terra ecosystem.",
    "disabled": true,
    "displayName": "Terraswap",
    "enabled": true,
    "gecko_id": null,
    "id": "491",
    "logo": "https://icons.llama.fi/terraswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/terraswap",
    "module": "terraswap",
    "name": "Terraswap",
    "openSource": true,
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "terraswap_io",
    "url": "https://terraswap.io",
  },
  Object {
    "address": "metis:******************************************",
    "audit_links": Array [
      "https://defisecurity.io/dist/files/tethys_finance_security_audit_report_1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Metis",
    "chains": Array [
      "metis",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1139",
    },
    "description": "We believe that in the future, L2 solutions will help Ethereum with scaling. Our mission is to empower the Metis Andromeda network with a fast, secure, reliable, and advanced native decentralized exchange app to handle all kinds of trading needs.",
    "disabled": false,
    "displayName": "Tethys AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
      "Impermax Finance",
    ],
    "gecko_id": null,
    "id": "1139",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/tethys-finance.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/tethys-finance",
    "module": "tethys-finance",
    "name": "Tethys AMM",
    "parentProtocol": "Tethys Finance",
    "protocolType": undefined,
    "symbol": "TETHYS",
    "twitter": "tethysfinance",
    "url": "https://tethys.finance",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://docs.tetu.io/tetu-io/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Yield",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "506",
    },
    "description": "Tetu is a decentralized organization committed to providing a next generation yield aggregator to DeFi investors. The Tetu core team has deep industry knowledge building back-end international banking systems and development with leading global payment processing infrastructure.",
    "disabled": false,
    "displayName": "Tetu Earn",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "506",
    "logo": "https://icons.llama.fi/tetu.svg",
    "methodology": Object {
      "Fees": "Yield",
      "ProtocolRevenue": "Gets management + performance fees",
      "Revenue": "Management + performance fees",
      "SupplySideRevenue": "Generated yield excluding protocol fees",
      "UserFees": "Pays management + performance fees",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/tetu",
    "module": "tetu",
    "name": "Tetu Earn",
    "oracles": Array [
      "Internal",
    ],
    "parentProtocol": "Tetu",
    "protocolType": undefined,
    "symbol": "TETU",
    "twitter": "tetu_io",
    "url": "http://tetu.io",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2417",
    },
    "description": "Thena is a native liquidity layer & AMM on BNB Chain",
    "disabled": false,
    "displayName": "Thena",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "2417",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/thena.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/thena",
    "module": "thena",
    "name": "Thena",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "THE",
    "twitter": "ThenaFi_",
    "url": "https://thena.fi/",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Thorchain",
    "chains": Array [
      "thorchain",
    ],
    "cmcId": "12942",
    "config": Object {
      "enabled": true,
      "id": "2287",
    },
    "description": "Earn native yield with THORChain Savers Vaults with no additional asset exposure or impermanent loss.",
    "disabled": false,
    "displayName": "THORSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "thorswap",
    "id": "2287",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/thorswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/thorswap",
    "module": "thorswap",
    "name": "THORSwap",
    "protocolType": undefined,
    "symbol": "THOR",
    "twitter": "THORSwap",
    "url": "https://app.thorswap.finance/liquidity",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Cross Chain",
    "chain": "Ethereum",
    "chains": Array [
      "thorchain",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2533",
    },
    "description": "True cross-chain DEX",
    "disabled": false,
    "displayName": "THORWallet DEX",
    "enabled": false,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2533",
    "logo": "https://icons.llama.fi/thorwallet-dex.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/thorwallet",
    "module": "thorwallet",
    "name": "THORWallet DEX",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "THORWalletDEX",
    "url": "https://www.thorwallet.org",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://docs.tinyman.org/contracts",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Algorand",
    "chains": Array [
      "algorand",
    ],
    "cmcId": "1569",
    "config": Object {
      "enabled": true,
      "id": "680",
    },
    "description": "Tinyman is a next-generation decentralized AMM on the Algorand blockchain.",
    "disabled": false,
    "displayName": "Tinyman",
    "enabled": true,
    "gecko_id": null,
    "id": "680",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/tinyman.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/tinyman",
    "module": "tinyman",
    "name": "Tinyman",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "tinymanorg",
    "url": "https://tinyman.org",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/titano",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "14746",
    "config": Object {
      "enabled": true,
      "id": "2102",
    },
    "description": "Titano Dex",
    "disabled": false,
    "displayName": "Titano Swych",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "titano",
    "id": "2102",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/titano-swych.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/titano-swych",
    "module": "titano-swych",
    "name": "Titano Swych",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "TITANO",
    "twitter": "TitanoFinance",
    "url": "https://app.titano.finance/swap",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2129",
    },
    "description": "AMM DEX of the Tomb Finance ecosystem on Fantom",
    "disabled": false,
    "displayName": "Tomb Swap",
    "enabled": true,
    "gecko_id": null,
    "id": "2129",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/tomb-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/tomb-swap",
    "module": "tomb-swap",
    "name": "Tomb Swap",
    "parentProtocol": "Tomb Finance",
    "protocolType": undefined,
    "symbol": "TOMB",
    "twitter": "tombfinance",
    "url": "https://tomb.finance/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://docs.traderjoexyz.com/main/security-and-contracts/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "468",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "468",
        },
        "v2": Object {
          "enabled": true,
          "id": "2393",
        },
      },
    },
    "description": "Trader Joe is your one-stop decentralized trading platform on the Avalanche network.",
    "disabled": false,
    "displayName": "Trader Joe DEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "468",
    "logo": "https://icons.llama.fi/trader-joe.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/traderjoe",
    "module": "traderjoe",
    "name": "Trader Joe DEX",
    "parentProtocol": "Trader Joe",
    "protocolType": undefined,
    "symbol": "JOE",
    "treasury": "traderjoe.js",
    "twitter": "traderjoe_xyz",
    "url": "https://www.traderjoexyz.com",
    "versionKey": "v1",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://github.com/abdk-consulting/audits/blob/main/traderjoe/ABDK_TraderJoe_TraderJoe_v_2_0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "468",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "468",
        },
        "v2": Object {
          "enabled": true,
          "id": "2393",
        },
      },
    },
    "description": "Joe V2 is a decentralized exchange based on Liquidity Book, a novel AMM protocol. Zero 0% slippage for swaps between ticks and Dynamic fees to improve liquidity provider profitability.",
    "disabled": false,
    "displayName": "Joe V2",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "468",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/joe-v2.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/traderjoe",
    "module": "traderjoe",
    "name": "Joe V2",
    "oracles": Array [],
    "parentProtocol": "Trader Joe",
    "protocolType": undefined,
    "symbol": "JOE",
    "twitter": "traderjoe_xyz",
    "url": "https://traderjoexyz.com/home",
    "versionKey": "v2",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "ThunderCore",
    "chains": Array [
      "thundercore",
    ],
    "cmcId": "1450",
    "config": Object {
      "enabled": true,
      "id": "705",
    },
    "description": "TTSwap is a DEX (decentralized exchange) on the ThunderCore blockchain based on the model: Automated Market-Making (AMM) inspired by Uniswap.",
    "disabled": false,
    "displayName": "TTswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "705",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ttswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ttswap",
    "module": "ttswap",
    "name": "TTswap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ThunderProtocol",
    "url": "https://ttswap.space/#/swap",
  },
  Object {
    "address": "celo:******************************************",
    "audit_links": Array [
      "https://docs.ubeswap.org/code-and-contracts/security#audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Celo",
    "chains": Array [
      "celo",
    ],
    "cmcId": "1339",
    "config": Object {
      "enabled": true,
      "id": "488",
    },
    "description": "Ubeswap is the leading DEX on Celo network!",
    "disabled": false,
    "displayName": "Ubeswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "ubeswap",
    "id": "488",
    "logo": "https://icons.llama.fi/ubeswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ubeswap",
    "module": "ubeswap",
    "name": "Ubeswap",
    "protocolType": undefined,
    "symbol": "UBE",
    "twitter": "ubeswap",
    "url": "https://ubeswap.org",
  },
  Object {
    "address": "ultron:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ultron",
    "chains": Array [
      "ultron",
    ],
    "cmcId": "21524",
    "config": Object {
      "enabled": true,
      "id": "2032",
    },
    "description": "Ultron Foundation is an EVM-compatible layer-1 blockchain system supported by Solidity Finance, with a fully functioning DeFi ecosystem that includes a native coin and dApps. The project was initiated in 2020 with a multi-cultural team of blockchain professionals developing internal products",
    "disabled": false,
    "displayName": "UltronSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "ultron",
    "id": "2032",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ultronswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/ultronswap",
    "module": "ultronswap",
    "name": "UltronSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ULX",
    "twitter": "ultron_found",
    "url": "https://ultronswap.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/uniclyNFT/Resources/blob/main/Unicly%20Security%20Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1334",
    "config": Object {
      "enabled": true,
      "id": "324",
    },
    "description": "Unicly is a permissionless protocol to combine, fractionalize, and trade NFTs. Users can fractionalize NFT collections into \\"uTokens\\", and trade them on a Sushiswap-fork AMM with liquidity incentives via liquidity mining.",
    "disabled": false,
    "displayName": "Unicly",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "unicly",
    "id": "324",
    "logo": "https://icons.llama.fi/unicly.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/unicly",
    "module": "unicly",
    "name": "Unicly",
    "protocolType": undefined,
    "symbol": "UNIC",
    "twitter": "uniclyNFT",
    "url": "https://app.unic.ly",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://drive.google.com/file/d/10Qdqj-ry4OhsQX3SesSRc1nEsNZ8UFkD/view",
      "https://medium.com/unifiprotocol/unifi-protocol-passes-slowmist-audit-9694b55b77ba",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "avax",
      "bittorrent",
      "tron",
      "ontology_evm",
      "harmony",
      "bsc",
      "ethereum",
      "icon",
      "iotex",
      "polygon",
    ],
    "cmcId": "1412",
    "config": Object {
      "enabled": true,
      "id": "646",
    },
    "description": "Unifi Protocol is a group of non-custodial, interoperable smart contracts linking multiple blockchains together into one large DeFi marketplace.",
    "disabled": false,
    "displayName": "UniFi",
    "enabled": true,
    "gecko_id": "unifi-protocol-dao",
    "id": "646",
    "logo": "https://icons.llama.fi/unifi.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/unifi",
    "module": "unifi",
    "name": "UniFi",
    "protocolType": undefined,
    "symbol": "UNFI",
    "twitter": "unifiprotocol",
    "url": "https://unifiprotocol.com",
  },
  Object {
    "address": "vision:VRbNprwjvBomKhvnhZq7Y33JRcrQJethpc",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Vision",
    "chains": Array [
      "vision",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2066",
    },
    "description": "DEX Built On Vision Network. Uses A “Defi + NFT” Paradigm, Which Isolates The Platform Token's Governance Utility From Its Governance System And Uses NFT To Power It",
    "disabled": false,
    "displayName": "VanSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2066",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/vanswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/vanswap",
    "module": "vanswap",
    "name": "VanSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VAN",
    "twitter": "Van_Swap",
    "url": "https://www.vanswap.org/#/home",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2342",
    },
    "description": "The World  Most Rewarding DEX. Automatically earn rewards that boost your savings just by using VaporDEX.",
    "disabled": false,
    "displayName": "VaporDex",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2342",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/vapordex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/vapordex",
    "module": "vapordex",
    "name": "VaporDex",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "Vapor_DEX",
    "url": "https://www.vapordex.io",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
    ],
    "cmcId": "20435",
    "config": Object {
      "enabled": true,
      "id": "1799",
    },
    "description": "A revolutionary new AMM based on Solidly launched on Optimism.",
    "disabled": false,
    "displayName": "Velodrome",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "velodrome-finance",
    "id": "1799",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/velodrome.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/velodrome",
    "module": "velodrome",
    "name": "Velodrome",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VELO",
    "treasury": "velodrome.js",
    "twitter": "VelodromeFi",
    "url": "https://app.velodrome.finance",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Findora",
    "chains": Array [
      "findora",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2550",
    },
    "description": "Vencieswap, the second DEX on Findora (FRA) delivering great trading experience and earning opportunities",
    "disabled": false,
    "displayName": "VeniceSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2550",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/veniceswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/veniceswap",
    "module": "veniceswap",
    "name": "VeniceSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "venicefinance",
    "url": "https://venice.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/0xGuard-com/audit-reports/blob/master/bitcoin.com/Bitcoin.com_final-audit-report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "SmartBCH",
    "chains": Array [
      "ethereum",
      "smartbch",
    ],
    "cmcId": "22929",
    "config": Object {
      "enabled": true,
      "id": "1732",
    },
    "description": "Verse DEX by Bitcoin.com enables permissionless trading via your Web3 wallet, including the multichain Bitcoin.com Wallet. Trading is currently available on Ethereum and SmartBCH, but more chains are coming soon. Sign up at getverse.com to get notified.",
    "disabled": false,
    "displayName": "Verse",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "verse-bitcoin",
    "governanceID": Array [
      "snapshot:verse.eth",
    ],
    "id": "1732",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/verse.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/verse",
    "module": "verse",
    "name": "Verse",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VERSE",
    "twitter": "BitcoinCom",
    "url": "https://verse.bitcoin.com",
  },
  Object {
    "address": "vechain:-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "VeChain",
    "chains": Array [
      "vechain",
    ],
    "cmcId": "1552",
    "config": Object {
      "enabled": true,
      "id": "963",
    },
    "description": "A protocol for trading and automated liquidity provision on VeChain",
    "disabled": false,
    "displayName": "Vexchange",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "vexchange",
    "id": "963",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/vexchange.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/vexchange",
    "module": "vexchange",
    "name": "Vexchange",
    "protocolType": undefined,
    "symbol": "VEX",
    "twitter": "VexchangeIO",
    "url": "https://vexchange.io/swap",
  },
  Object {
    "address": "harmony:******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/ViperSwap/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Harmony",
    "chains": Array [
      "harmony",
    ],
    "cmcId": "8850",
    "config": Object {
      "enabled": true,
      "id": "313",
    },
    "description": "ViperSwap is Harmony's leading DEX. Building on top of Harmony's 2-second finality and insanely low fees it's one of the fastest and cheapest DEXes to use.",
    "disabled": false,
    "displayName": "ViperSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "viper",
    "id": "313",
    "logo": "https://icons.llama.fi/viperswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/viperswap",
    "module": "viperswap",
    "name": "ViperSwap",
    "protocolType": undefined,
    "symbol": "VIPER",
    "twitter": "VenomDAO",
    "url": "https://viper.exchange",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Fuse",
    "chains": Array [
      "fuse",
    ],
    "cmcId": "12038",
    "config": Object {
      "enabled": true,
      "id": "714",
    },
    "description": "Built atop the powerful Fuse blockchain and ecosystem, Voltage enables anyone to carry the power of DeFi in their pocket.",
    "disabled": false,
    "displayName": "Voltage",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
      "Compound",
    ],
    "gecko_id": "fusefi",
    "id": "714",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/voltage.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/voltage",
    "module": "voltage",
    "name": "Voltage",
    "protocolType": undefined,
    "symbol": "VOLT",
    "twitter": "voltfinance",
    "url": "https://app.voltage.finance/",
  },
  Object {
    "address": "meter:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Meter",
    "chains": Array [
      "meter",
    ],
    "cmcId": "19160",
    "config": Object {
      "enabled": true,
      "id": "1225",
      "protocolsData": Object {
        "v1": Object {
          "disabled": true,
          "displayName": "VoltSwap V1",
          "enabled": true,
          "id": "1225",
        },
        "v2": Object {
          "enabled": true,
          "id": "2133",
        },
      },
    },
    "description": "VoltSwap is the first major DEX in the Meter ecosystem. It is a completely community-driven project and was introduced by the Meter.io team to showcase the capabilities of the Meter blockchain.",
    "disabled": true,
    "displayName": "VoltSwap V1",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "voltswap",
    "id": "1225",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/voltswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/voltswap",
    "module": "voltswap",
    "name": "VoltSwap",
    "oracles": Array [],
    "parentProtocol": "Volt Finance",
    "protocolType": undefined,
    "symbol": "VOLT",
    "twitter": "Meter_IO",
    "url": "https://voltswap.finance",
    "versionKey": "v1",
  },
  Object {
    "address": "meter:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Meter",
    "chains": Array [
      "meter",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1225",
      "protocolsData": Object {
        "v1": Object {
          "disabled": true,
          "displayName": "VoltSwap V1",
          "enabled": true,
          "id": "1225",
        },
        "v2": Object {
          "enabled": true,
          "id": "2133",
        },
      },
    },
    "description": "VoltSwap is the first major DEX in the Meter ecosystem. It is a completely community-driven project and was introduced by the Meter.io team to showcase the capabilities of the Meter blockchain.",
    "disabled": false,
    "displayName": "VoltSwap V2",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1225",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/voltswap-v2.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/voltswap",
    "module": "voltswap",
    "name": "VoltSwap V2",
    "oracles": Array [],
    "parentProtocol": "Volt Finance",
    "protocolType": undefined,
    "symbol": "VOLT",
    "twitter": "Meter_IO",
    "url": "https://voltswap.finance",
    "versionKey": "v2",
  },
  Object {
    "address": "tezos:KT1TwzD6zV3WeJ39ukuqxcfK2fJCnhvrdN1X",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Tezos",
    "chains": Array [
      "tezos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1706",
    },
    "description": "Vortex is an all-in-one decentralized finance protocol built on the Tezos blockchain.",
    "disabled": false,
    "displayName": "Vortex Protocol",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1706",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/vortex-protocol.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/vortex-protocol",
    "module": "vortex-protocol",
    "name": "Vortex Protocol",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SMAK",
    "twitter": "vortexAMM",
    "url": "https://app.vortex.network/",
  },
  Object {
    "address": "cronos:******************************************",
    "audit_links": Array [
      "https://docs.vvs.finance/fundamentals/smart-contracts-and-security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cronos",
    "chains": Array [
      "cronos",
    ],
    "cmcId": "14519",
    "config": Object {
      "enabled": true,
      "id": "831",
    },
    "description": "VVS is designed to be the simplest DeFi platform for users to swap tokens, earn high yields, and most importantly have fun!",
    "disabled": false,
    "displayName": "VVS Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "vvs-finance",
    "id": "831",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/vvs-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/vvs-finance",
    "module": "vvs-finance",
    "name": "VVS Finance",
    "protocolType": undefined,
    "symbol": "VVS",
    "twitter": "VVS_finance",
    "url": "https://vvs.finance",
  },
  Object {
    "address": "velas:******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/Wagyu",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Velas",
    "chains": Array [
      "velas",
    ],
    "cmcId": "11354",
    "config": Object {
      "enabled": true,
      "id": "1003",
    },
    "description": "WagyuSwap is the leading decentralized exchange on the Velas Chain, which has been clocked as the fastest blockchain in the world.",
    "disabled": false,
    "displayName": "WagyuSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "wagyuswap",
    "id": "1003",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wagyuswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wagyuswap",
    "module": "wagyuswap",
    "name": "WagyuSwap",
    "protocolType": undefined,
    "symbol": "WAG",
    "twitter": "WagyuSwap_app",
    "url": "https://www.wagyuswap.app",
  },
  Object {
    "address": "wan:******************************************",
    "audit_links": Array [
      "https://docs.wanswap.finance/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Wanchain",
    "chains": Array [
      "wan",
    ],
    "cmcId": "1267",
    "config": Object {
      "enabled": true,
      "id": "186",
    },
    "description": "WanSwap is an innovative crosschain automatic market making (AMM) decentralized exchange (DEX) based on Wanchain.",
    "disabled": false,
    "displayName": "WanSwap Dex",
    "enabled": true,
    "gecko_id": "wanswap",
    "id": "186",
    "logo": "https://icons.llama.fi/wanswap-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wanswap-dex",
    "module": "wanswap-dex",
    "name": "WanSwap Dex",
    "protocolType": undefined,
    "symbol": "WASP",
    "twitter": "wanswap",
    "url": "https://wanswap.finance/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/wardenswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "optimism",
      "arbitrum",
      "ethereum",
      "polygon",
    ],
    "cmcId": "8981",
    "config": Object {
      "enabled": true,
      "id": "392",
    },
    "description": "Wardenswap is not just another decentralized exchange (DEX). When you trade on Wardenswap, you will receive a much better price than any other DEX. If there is another pool that has a price ready to be arbitraged, Wardenswap will also take the deal for you. Wardenswap is the gateway to all decentralized exchanges on the earth.",
    "disabled": false,
    "displayName": "WardenSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "warden",
    "id": "392",
    "logo": "https://icons.llama.fi/wardenswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wardenswap",
    "module": "wardenswap",
    "name": "WardenSwap",
    "protocolType": undefined,
    "symbol": "WAD",
    "twitter": "WardenSwap",
    "url": "https://www.wardenswap.com",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Velas",
    "chains": Array [
      "velas",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2220",
    },
    "description": "Wavelength is the first of its kind on Velas. At the core of our project is a Decentralized Exchange (DEX) built on top of the Balancer V2 framework, which makes us the first next-generation Automated Market Maker (AMM) protocol on Velas!",
    "disabled": false,
    "displayName": "Wavelength DAO",
    "enabled": true,
    "forkedFrom": Array [
      "Balancer",
    ],
    "gecko_id": null,
    "id": "2220",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wavelength-dao.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wavelength-dao",
    "module": "wavelength-dao",
    "name": "Wavelength DAO",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "Wavelength_DAO",
    "url": "https://wavelength.exchange",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://www.certik.com/projects/wemix",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "WEMIX",
    "chains": Array [
      "wemix",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2674",
    },
    "description": "WEMIX.Fi is the first decentralized exchange on the WEMIX3.0 mainnet. WEMIX.Fi is a fully on-chain DeFi platform supporting storage, exchange, borrowing, settlement and investment of crypto-assets.",
    "disabled": false,
    "displayName": "WEMIX.FI",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2674",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wemix.fi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wemix.fi",
    "module": "wemix.fi",
    "name": "WEMIX.FI",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "WemixNetwork",
    "url": "https://wemix.fi",
  },
  Object {
    "address": "binance:******************************************",
    "audit_links": Array [
      "https://github.com/Whale-Swap/audits/blob/878e4671851d65c4fc9a35a7d2fec94a483e1d72/WhaleSwap_Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1884",
    },
    "description": "A next-generation DEX on BNB Chain to maximize capital efficiency with the innovative Discretized-Liquidity-AMM model",
    "disabled": false,
    "displayName": "WhaleSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "whaleswap",
    "id": "1884",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/whaleswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/whaleswap",
    "module": "whaleswap",
    "name": "WhaleSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "POD",
    "twitter": "WhaleLoans",
    "url": "https://whaleswap.finance",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://certik.com/projects/wigoswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "18148",
    "config": Object {
      "enabled": true,
      "id": "1351",
    },
    "description": "DeFi hub on Fantom network with LIFETIME farming earnings powered by Gamified Burning Mechanism (GBM)",
    "disabled": false,
    "displayName": "WigoSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "wigoswap",
    "id": "1351",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wigoswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wigoswap",
    "module": "wigoswap",
    "name": "WigoSwap",
    "protocolType": undefined,
    "symbol": "WIGO",
    "twitter": "WigoSwap",
    "url": "https://wigoswap.io/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2118",
    },
    "description": "An explosive innovation of all-in-one decentralized exchange",
    "disabled": false,
    "displayName": "WinerySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "cork",
    "id": "2118",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wineryswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wineryswap",
    "module": "wineryswap",
    "name": "WinerySwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CORK",
    "twitter": "WinerySwap",
    "url": "https://winery.finance",
  },
  Object {
    "address": "cardano:asset18v68f0vgv2nc3at8cuuem7f3fztwqcq2usgjyp",
    "audit_links": Array [
      "https://www.certik.com/projects/wingriders",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Cardano",
    "chains": Array [
      "cardano",
    ],
    "cmcId": "20894",
    "config": Object {
      "enabled": true,
      "id": "1601",
    },
    "description": "The DEX on Cardano. Native and fast AMM decentralized exchange platform.",
    "disabled": false,
    "displayName": "WingRiders",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "wingriders",
    "id": "1601",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wingriders.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wingriders",
    "module": "wingriders",
    "name": "WingRiders",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "WRT",
    "twitter": "wingriderscom",
    "url": "https://www.wingriders.com",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://wingswap.gitbook.io/wingswap/getting-started/audit-1",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "15245",
    "config": Object {
      "enabled": true,
      "id": "976",
    },
    "description": "WingSwap is a dedicated Decentralized Exchange (DEX) and a staking platform where users can provide liquidity to the DEX, trade different types of assets in a decentralized way and get rewarded for doing so.",
    "disabled": false,
    "displayName": "WingSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "wingswap",
    "id": "976",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wingswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wingswap",
    "module": "wingswap",
    "name": "WingSwap",
    "protocolType": undefined,
    "symbol": "WIS",
    "twitter": "WingSwapFTM",
    "url": "https://wingswap.io",
  },
  Object {
    "address": "dogechain:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Dogechain",
    "chains": Array [
      "dogechain",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2113",
    },
    "description": "The newest DEX on Doge!",
    "disabled": false,
    "displayName": "Wojak Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2113",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wojak-finance.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wojak-finance",
    "module": "wojak-finance",
    "name": "Wojak Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "WOJK",
    "twitter": "wojakfi",
    "url": "https://wojak.fi",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://hacken.io/audits/#wombat_exchange",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Wombat-v1.0.pdf",
      "https://www.wombat.exchange/zokyo_wombat_audit_report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "19623",
    "config": Object {
      "enabled": true,
      "id": "1700",
    },
    "description": "Hyper efficient multi-chain stableswap. #BNB and beyond.",
    "disabled": false,
    "displayName": "Wombat Exchange",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "wombat-exchange",
    "id": "1700",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wombat-exchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wombat-exchange",
    "module": "wombat-exchange",
    "name": "Wombat Exchange",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "WOM",
    "twitter": "WombatExchange",
    "url": "https://www.wombat.exchange/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/woofiswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "avax",
      "bsc",
      "fantom",
      "polygon",
      "arbitrum",
      "optimism",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1461",
    },
    "description": "WOOFi is a multichain DeFi platform which offers the best trade execution and lowest swap fee, opportunities to earn sustainable yields on crypto, and a high-efficiency solution for on-chain liquidity provision.",
    "disabled": false,
    "displayName": "WOOFi",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1461",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/woofi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/woofi",
    "module": "woofi",
    "name": "WOOFi",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "WOO Network",
    "protocolType": undefined,
    "symbol": "WOO",
    "twitter": "WOOnetwork",
    "url": "https://fi.woo.org",
  },
  Object {
    "address": "waves:Atqv59EYzjFGuitKVnMRk6H8FukjoV3ktPorbEys25on",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Waves",
    "chains": Array [
      "waves",
    ],
    "cmcId": "246",
    "config": Object {
      "enabled": true,
      "id": "614",
    },
    "description": "Launched in 2017 as Waves DEX, WX Network is a robust platform for self-sovereign crypto management with a suite of investment tools in a convenient interface. WX.Network offers high transaction speed and an unprecedented level of security as users remain in sole control of their assets",
    "disabled": false,
    "displayName": "WX Network",
    "enabled": true,
    "gecko_id": "waves-exchange",
    "id": "614",
    "logo": "https://icons.llama.fi/wx-network.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/wx.network",
    "module": "wx.network",
    "name": "WX Network",
    "protocolType": undefined,
    "symbol": "Waves.Exchange",
    "twitter": "WXNetwork",
    "url": "https://wx.network/",
  },
  Object {
    "address": "elrond:MEX-455c57",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Elrond",
    "chains": Array [
      "elrond",
    ],
    "cmcId": "3286",
    "config": Object {
      "enabled": true,
      "id": "854",
    },
    "description": "xExchange is a DEX AMM running on the MultiversX Network. It is built by the same team that has built the MultiversX blockchain.",
    "disabled": false,
    "displayName": "xExchange",
    "enabled": true,
    "gecko_id": "maiar-dex",
    "id": "854",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/ecxchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/xexchange",
    "module": "xexchange",
    "name": "xExchange",
    "protocolType": undefined,
    "symbol": "MEX",
    "twitter": "MaiarExchange",
    "url": "https://xexchange.com/",
  },
  Object {
    "address": "xdc:******************************************",
    "audit_links": Array [
      "https://github.com/XRC20-Swap/DEX-Audit/blob/main/DEX%20Audit%20Techrate.pdf",
      "https://www.certik.com/projects/xswap-protocol",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "XDC",
    "chains": Array [
      "xdc",
    ],
    "cmcId": "14613",
    "config": Object {
      "enabled": true,
      "id": "2145",
    },
    "description": "XSWAP is the first decentralized exchange (DEX) that utilizes an automated market maker (AMM) system on the Xinfin network and is powered by XDC. The vision of XSWAP is to grow and expand the Xinfin network. It allows the swap and exchange of XRC20 tokens and offers staking & yield farming. XSwap is also home to the first launchpad in the XDC network which offers token creation, presale creation, token & liquidity locker and multisender for all XRC20 tokens.",
    "disabled": false,
    "displayName": "XSwap Protocol",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "xswap-protocol",
    "id": "2145",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/xswap-protocol.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/xswap-protocol",
    "module": "xswap-protocol",
    "name": "XSwap Protocol",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "XSP",
    "twitter": "XSwapProtocol",
    "url": "https://app.xspswap.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://rugdoc.io/project/yieldfields/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1347",
    },
    "description": "YieldFields is an Automated Market Maker (AMM) with the Exchange at the heart of YieldFields. ",
    "disabled": false,
    "displayName": "YieldFields",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1347",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/yieldfields.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/yieldfields",
    "module": "yieldfields",
    "name": "YieldFields",
    "protocolType": undefined,
    "symbol": "FIELD",
    "twitter": "yieldfields",
    "url": "https://yieldfields.finance/",
  },
  Object {
    "address": "dogechain:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Dogechain",
    "chains": Array [
      "dogechain",
    ],
    "cmcId": "21477",
    "config": Object {
      "enabled": true,
      "id": "1980",
    },
    "description": "YodeSwap is one of the first automated market-making (AMM), decentralized exchanges (DEX) for the Dogechain Network.",
    "disabled": false,
    "displayName": "Yodeswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "yodeswap",
    "id": "1980",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/yodeswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/yodeswap",
    "module": "yodeswap",
    "name": "Yodeswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "YODE",
    "twitter": "Yodeswap",
    "url": "https://yodeswap.dog",
  },
  Object {
    "address": "godwoken_v1:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Godwoken",
    "chains": Array [
      "godwoken",
      "godwoken_v1",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1002",
    },
    "description": "YokaiSwap is a next generation interoperable, decentralized trading platform and the first to come from the Nervos network.",
    "disabled": false,
    "displayName": "YokaiSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1002",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/yokaiswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/yokaiswap",
    "module": "yokaiswap",
    "name": "YokaiSwap",
    "protocolType": undefined,
    "symbol": "YOK",
    "twitter": "yokaiswap",
    "url": "https://www.yokaiswap.com",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://hacken.io/wp-content/uploads/2021/10/Yoshi_22102021SCAudit_Report_2.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "bsc",
      "ethereum",
    ],
    "cmcId": "13118",
    "config": Object {
      "enabled": true,
      "id": "863",
    },
    "description": "Yoshi.exchange is a DEX aggregator launching at Fantom Opera blockchain. We create the first closed-cycle united Fantom payment system. The main goal is to facilitate entry threshold to Fantom ecosystem for newcomers and to simplify the user experience for the current consumers.",
    "disabled": false,
    "displayName": "Yoshi Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "yoshi-exchange",
    "id": "863",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/yoshi-exchange.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/yoshi-exchange",
    "module": "yoshi-exchange",
    "name": "Yoshi Exchange",
    "protocolType": undefined,
    "symbol": "YOSHI",
    "twitter": "YoshiExchange",
    "url": "https://yoshi.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Zilliqa",
    "chains": Array [
      "zilliqa",
    ],
    "cmcId": "9107",
    "config": Object {
      "enabled": true,
      "id": "303",
    },
    "description": "Decentralised exchange for ZRC2 tokens on Zilliqa.",
    "disabled": false,
    "displayName": "ZilSwap",
    "enabled": true,
    "gecko_id": "zilswap",
    "id": "303",
    "logo": "https://icons.llama.fi/zilswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zilswap",
    "module": "zilswap",
    "name": "ZilSwap",
    "protocolType": undefined,
    "symbol": "ZWAP",
    "twitter": "ZilSwap",
    "url": "https://zilswap.io/swap",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
    ],
    "cmcId": "19917",
    "config": Object {
      "enabled": true,
      "id": "1296",
    },
    "description": "Swap, earn, and build on the first optimistic rollup optimized decentralized crypto trading protocol.",
    "disabled": false,
    "displayName": "ZipSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "zipswap",
    "id": "1296",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zipswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zipswap",
    "module": "zipswap",
    "name": "ZipSwap",
    "protocolType": undefined,
    "symbol": "ZIP",
    "twitter": "Zip_swap",
    "url": "https://zipswap.fi/#/",
  },
  Object {
    "address": "moonriver:******************************************",
    "audit_links": Array [
      "https://github.com/Zircon-Finance/zircon-protocol-2/blob/master/audit/chaintroopers/Zircon_Protocol2_SecAssessment_report_v1.1.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonriver",
    "chains": Array [
      "moonriver",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2143",
    },
    "description": "Zircon is a DEX protocol with a unique mechanism to provide liquidity with one token and with greatly reduced impermanent loss.",
    "disabled": false,
    "displayName": "Zircon Gamma",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "zircon-gamma-token",
    "id": "2143",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zircon-gamma.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zircon-gamma",
    "module": "zircon-gamma",
    "name": "Zircon Gamma",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ZRG",
    "twitter": "Zircon_Finance",
    "url": "https://zircon.finance",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/solidproof/projects/blob/main/ZyberSwap/SmartContract_Audit_Solidproof_Zyberswap.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "Community Driven Exchange on Arbitrum.",
    "disabled": false,
    "displayName": "Zyberswap AMM",
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": null,
    "id": "2467",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zyberswap-amm.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zyberswap",
    "module": "zyberswap",
    "name": "Zyberswap AMM",
    "oracles": Array [
      "TWAP",
    ],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "zyberswap",
    "url": "https://www.zyberswap.io",
    "versionKey": "v2",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "ZyberSwap V3's Concentrated Liquidity feature allows liquidity providers to focus their liquidity within a specific price range, resulting in increased capital efficiency and reduced impermanent loss. This feature is particularly beneficial for those looking to optimize their investment strategy and maximize returns, as it allows for more precise tracking of Total Value Locked (TVL) within a designated price range.",
    "disabled": false,
    "displayName": "Zyberswap V3",
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2467",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zyberswap-v3.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zyberswap",
    "module": "zyberswap",
    "name": "Zyberswap V3",
    "oracles": Array [],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "ZyberSwap",
    "url": "https://www.zyberswap.io/",
    "versionKey": "v3",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "Community Driven Exchange on Arbitrum.",
    "disabled": false,
    "displayName": "ZyberSwap Stableswap",
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2467",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zyberswap-stableswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/zyberswap",
    "module": "zyberswap",
    "name": "ZyberSwap Stableswap",
    "oracles": Array [],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "zyberswap",
    "url": "https://app.zyberswap.io/exchange/swap",
    "versionKey": "stable",
  },
  Object {
    "address": "neo:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Liquid Staking",
    "chain": "NEO",
    "chains": Array [
      "neo",
      "bsc",
      "avax",
      "polygon",
      "ethereum",
      "phantasma",
    ],
    "cmcId": "17454",
    "config": Object {
      "enabled": false,
      "id": "2290",
    },
    "description": "The world’s most powerful cross-chain NFT marketplace. Create, Sell, Discover and Buy BSC, NEO N3, Ethereum, Phantasma, Polygon and Avalanche digital collectibles.",
    "disabled": false,
    "displayName": "GhostMarket",
    "enabled": false,
    "forkedFrom": Array [],
    "gecko_id": "ghostmarket",
    "id": "2290",
    "listedAt": 1668700062,
    "logo": "https://icons.llama.fi/ghostmarket.jpg",
    "methodology": Object {
      "Fees": "Users pay 2% fees on each trade",
      "HoldersRevenue": "20% of user fees goes to GFUND single stake pool",
      "ProtocolRevenue": "Protocol gets 2% of each trade",
      "Revenue": "Revenue is 2% of each trade",
      "SupplySideRevenue": "Revenue earned by stETH holders",
      "UserFees": "Users pay 2% fees on each trade",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/ghostmarket",
    "module": "ghostmarket",
    "name": "GhostMarket",
    "protocolType": undefined,
    "symbol": "GM",
    "twitter": "ghostmarketio",
    "url": "https://ghostmarket.io",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/BUIDLHub/dexible-contracts/blob/master/Audit%20Report%20-%20Dexible%20%5B16%20August%202021%5D.pdf",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "avax",
      "bsc",
      "fantom",
      "optimism",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "2249",
      "protocolsData": Object {
        "Dexible_v2": Object {
          "enabled": false,
          "id": "2249",
        },
      },
    },
    "description": " DeFi's execution management system. Dexible is a DEX aggregator that also provides algo and automated order types normally only seen in CeFi.",
    "disabled": false,
    "displayName": "Dexible V2",
    "enabled": false,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2249",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/dexible.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/aggregators/dexible",
    "module": "dexible",
    "name": "Dexible V2",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "DexibleApp",
    "url": "https://dexible.io",
    "versionKey": "Dexible_v2",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/alitafinance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "11599",
    "config": Object {
      "enabled": true,
      "id": "561",
    },
    "description": "A DEX that is connecting Centralized and Decentralized Trading Platforms Worldwide.",
    "disabled": false,
    "displayName": "Alita Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "alita",
    "id": "561",
    "logo": "https://icons.llama.fi/alita-finance.png",
    "methodology": Object {
      "Fees": "User pays 0.2% fees on each transaction",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Protocol takes no revenue",
      "Revenue": "Revenue generated is 0.2% of total volume",
      "SupplySideRevenue": "0.2% of each transaction goes to LPs",
      "UserFees": "User pays 0.2% fees on each transaction",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/alita-finance",
    "module": "alita-finance",
    "name": "Alita Finance",
    "protocolType": undefined,
    "symbol": "ALI",
    "twitter": "AlitaFinance",
    "url": "https://app.alita.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.pancakeswap.finance/#is-it-safe",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
          "startFrom": 1663718400,
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "The #1 AMM and yield farm on Binance Smart Chain",
    "disabled": true,
    "displayName": "PancakeSwap AMM V1",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "194",
    "logo": "https://icons.llama.fi/pancakeswap-amm-v1.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap AMM V1",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://v1exchange.pancakeswap.finance/",
    "versionKey": "v1",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.pancakeswap.finance/#is-it-safe",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
          "startFrom": 1663718400,
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "The #1 AMM and yield farm on Binance Smart Chain",
    "disabled": false,
    "displayName": "PancakeSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "194",
    "logo": "https://icons.llama.fi/pancakeswap-amm.jpg",
    "methodology": Object {
      "Fees": "All fees comes from the user.",
      "HoldersRevenue": "0.0575% is used to facilitate CAKE buyback and burn.",
      "ProtocolRevenue": "Treasury receives 0.0225% of each swap.",
      "Revenue": "All revenue generated comes from user fees.",
      "SupplySideRevenue": "LPs receive 0.17% of the fees.",
      "UserFees": "User pays 0.25% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap AMM",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://pancakeswap.finance/",
    "versionKey": "v2",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
          "startFrom": 1663718400,
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "StableSwap on PancakeSwap is a feature to trade stable pairs with a lower slippage based on an invariant curve slippage function. It is designed to swap specific assets that are priced closely – such as USD stablecoin.",
    "disabled": false,
    "displayName": "PancakeSwap StableSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "194",
    "listedAt": 1676313502,
    "logo": "https://icons.llama.fi/pancakeswap-stableswap.jpg",
    "methodology": Object {
      "Fees": "All fees comes from the user fees, which is 025% of each trade.",
      "HoldersRevenue": "A 40% of the fees is used to facilitate CAKE buyback and burn.",
      "ProtocolRevenue": "Treasury receives 10% of the fees.",
      "Revenue": "Revenue is 50% of the fees paid by users.",
      "SupplySideRevenue": "LPs receive 50% of the fees.",
      "UserFees": "User pays 0.25% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap StableSwap",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "startFrom": 1663718400,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://pancakeswap.finance/swap",
    "versionKey": "stableswap",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "595",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V1
",
    "disabled": false,
    "displayName": "Uniswap V1",
    "enabled": true,
    "gecko_id": "uniswap",
    "id": "1",
    "listedAt": 1666191149,
    "logo": "https://icons.llama.fi/uniswap-v1.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V1",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1069",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V2
",
    "disabled": false,
    "displayName": "Uniswap V2",
    "enabled": true,
    "gecko_id": null,
    "id": "1",
    "listedAt": 1666191162,
    "logo": "https://icons.llama.fi/uniswap-v2.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V2",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v2",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "polygon",
      "celo",
      "bsc",
    ],
    "cmcId": "1348",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V2
",
    "disabled": false,
    "displayName": "Uniswap V3",
    "enabled": true,
    "gecko_id": null,
    "id": "1",
    "listedAt": 1666191475,
    "logo": "https://icons.llama.fi/uniswap-v3.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.05%, 0.30%, or 1% on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V3",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "treasury": "uniswap.js",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v3",
  },
]
`;
