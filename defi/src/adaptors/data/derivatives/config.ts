import { AdaptorsConfig } from "../types"

export default {
    "emdx": {
        id: "2299"
    },
    "jojo": {
        id: "2320"
    },
    "kperp-exchange": {
        id: "2326"
    },
    "synfutures-v1": {
        id: "2328"
    },
    "vela": {
        id: "2548"
    },
    "hyperliquid-perp": {
        id: "5507",
    },
    "dydx": {
        id: "144",
    },
    "mux-protocol-perps": {
        id: "2254",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1746489600": true,
            }
        }
    },
    "polynomial-trade": {
        id: "2848"
    },
    "pika-protocol": {
        id: "916"
    },
    "urdex": {
        id: "3085",
    },
    "hmx": {
        id: "2296"
    },
    "synthetix": {
        id: "115",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1689292800": true,
                "1689379200": true,
                "1689465600": true,
                "1689638400": true,
                "1689811309": true,
            },
        }
    },
    "pika-protocol-v4": {
        id: "3281"
    },
    "gains-network": {
        id: "1018"
    },
    // "palmswap": {
    //     id: "3279"
    // },
    "aevo": {
        id: "2797",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1722816000": true,
            }
        }
    },
    "rollup-finace-perps": {
        id: "2889",
    },
    // "metavault-derivative": {
    //     id: "1801"
    // },
    "satori": {
        id: "2982",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1691625600": true,
            },
        }
    },
    "aark": {
        id: "3376",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1691971200": true,
                "1691884800": true,
            },
        }
    },
    "apex": {
        id: "1878",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1697414400": true,
                "1697328000": true,
                "1722816000": true,
                "1738540800": true,
            },
        }
    },
    "zeta": {
        id: "1804",
    },
    "thena-perp": {
        id: "3537",
    },
    "panacakeswap-perp": {
        id: "3538",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1736640000": true,
            },
        }
    },
    "apollox": {
        id: "1772"
    },
    "based-markets": {
        id: "3609"
    },
    "xena-finance-derivative": {
        id: "3620"
    },
    "gambit": {
        id: "3325"
    },
    "bluefin": {
        id: "2625",
    },
    "holdstation-defutures": {
        id: "2959"
    },
    "contango": {
        id: "3602",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1697414400": true,
                "1697328000": true
            },
        }
    },
    // "tigris": {
    //     id: "3129"
    // },
    "paradex": {
        id: "3648",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1722816000": true,
            },
        }
    },
    "levana": {
        id: "3474"
    },
    "intent-x": {
        id: "3747"
    },
    "equation": {
        id: "3726",
        cleanRecordsConfig: {
            genuineSpikes: {
                1707782400: false
            }
        }
    },
    "mu-exchange": {
        id: "3763"
    },
    "perennial-v2": {
        id: "3599",
    },
    "rabbitx": {
        id: "2886",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1700524800": true,
                "1700438400": true,
                "1700352000": true,
                "1700265600": true,
                "1700179200": true,
                "1749600000": true,
            },
        }

    },
    "SubstanceX": {
        id: "3835"
    },
    "stormtrade": {
        id: "3883"
    },
    "goosefx": {
        id: "2175"
    },
    "synfutures-v2": {
        id: "3061"
    },
    "metavault-derivatives-v2": {
        id: "3911"
    },
    "kiloex": {
        id: "3329"
    },
    "surfone": {
        id: "3954"
    },
    "merkle-trade": {
        id: "3678"
    },
    "immortalx": {
        id: "3983"
    },
    "fwx": {
        id: "4026"
    },
    "dydx-v4": {
        id: "4067",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1706140800": true,
            },
        }
    },
    "equation-v2": {
        id: "4074",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1706400000": true,
                "1706313600": true,
                "1706227200": true,
            },
        }
    },
    "jupiter-perpetual": {
        id: "4077",
    },
    "hyperionx": {
        id: "4094",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1706832000": true,
            },
        }
    },
    "kinetix-derivatives-v2": {
        id: "4110"
    },
    "pingu": {
        id: "4102",
        cleanRecordsConfig: {
            genuineSpikes: {
                1716422400: true
            }
        }
    },
    "ipor": {
        id: "2147",
        cleanRecordsConfig: {
            genuineSpikes: {
                1715126400: true,
                1716336000: true,
                1717718400: true,
                1722297600: true,
                1724457600: true,
                1676678400: true,
            }
        }
    },
    "sudofinance": {
        id: "4045"
    },
    // "blastfutures": { // duplicate with rabbitx fusion
    //     id: "4221",
    //     cleanRecordsConfig: {
    //         genuineSpikes: {
    //             "1709337600": true,
    //             "1709251200": true,
    //             "1709164800": true,
    //             "1709078400": true,
    //         },
    //     }
    // },
    "synfutures-v3": {
        id: "4215"
    },
    "avantis": {
        id: "4108",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1743552000": true,
            },
        }
    },
    "flashtrade": {
        id: "4107",
    },
    "wefi": {
        id: "2666"
    },
    "anyhedge": {
        id: "2633"
    },
    "myx-finance": {
        id: "4319",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1712620800": true,
                "1712534400": true,
            },
        }
    },
    "logx": {
        id: "5137"
    },
    "swych": {
        id: "4365",
    },
    "ash-perp": {
        id: "4426"
    },
    "synthetix-v3": {
        id: "4446"
    },
    "increment-protocol": {
        id: "4382",
    },
    "lyra": {
        id: "3923",
    },
    "grizzly-trade-derivatives-v2": {
        id: "4506",
    },
    "unidex-perps": {
        id: "1833",
    },
    "bsx": {
        id: "4458",
    },
    "rollie-finance": {
        id: "4636",
    },
    "tlx-finance": {
        id: "4555"
    },
    "linehub-perps": {
        id: "4842"
    },
    "apex-omni": {
        id: "4822",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1722816000": true,
            },
        }
    },
    "edgeX": {
        id: "4954",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1723075200": true,
                "1722988800": true,
            },
        }
    },
    "cyberperp": {
        id: "5016",
    },
    "bmx-freestyle": {
        id: "4903"
    },
    "tradoor": {
        id: "4738",
    },
    "zeno": {
        id: "4642"
    },
    "pear-protocol": {
        id: "5151"
    },
    "filament": {
        id: "5650",
    },
    "kuma-v1": {
        id: "5079"
    },
    "trado": {
        id: "5208",
    },
    "quickswap-perps": {
        id: "2980",
    },
    "capybara-perp": {
        id: "5257",
    },
    "defx": {
        id: "5273",
    },
    "surge-trade": {
        id: "5290"
    },
    "optim-finance": {
        id: "2380",
    },
    "quenta": {
        id: "5314"
    },
    "zkera-finance": {
        id: "3343"
    },
    "adrena": {
        id: "5353"
    },
    "thetis-market": {
        id: "5469"
    },
    "mars-perp": {
        id: "5498",
    },
    "satoshi-perps": {
        id: "5571"
    },
    "lnexchange-perp": {
        id: "5639",
    },
    "cvex": {
        id: "5610"
    },
    "raydium-perps": {
        id: "5729",
    },
    "citrex-markets": {
        id: "5641",
    },
    "flexperp": {
        id: "5843"
    },
    "electra": {
        id: "5817"
    },
    "desk": {
        id: "5813"
    },
    "extended": {
        id: "5887"
    },
    "agdex": {
        id: "5467"
    },
    "vest": {
        id: "4400"
    },
    // "goosefx_v2": { // team asked to have adapter moved to dexs config
    //     id: "5998"
    // },
    "zo": {
        id: "6018"
    },
    "d8x": {
        id: "3924"
    },
    "elfi": {
        id: "5160"
    },
    "tearex": {
        id: "5920"
    },
    "typus-perp": {
        id: "6014",
    },
    "loxodrome-perp": {
        id: "5300",
    },
    "ostium": {
        id: "4932",
    },
    "vanilla-finance-perps": {
        id: "6079",
    },

    "gmx-derivatives": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1692230400": true
            }
        },
        "displayName": "GMX - Derivatives V1",
        id: "337",
    },
    "metavault_trade-metavault-derivative": {
        id: "1801"
    },
    "morphex-derivatives": {
        "displayName": "Morphex - Derivatives",
        id: "2662",
    },
    "covo-v2-derivatives": {
        "displayName": "Covo V2 - Derivatives",
        id: "2730",
        "cleanRecordsConfig": {
            "genuineSpikes": true
        }
    },
    "spacedex-derivatives": {
        id: "2814",
        "displayName": "SpaceDex - Derivatives"
    },
    "level-finance-level-finance-derivative": {
        "displayName": "Level Finance - Derivatives",
        id: "2395",
    },
    "el-dorado-exchange-derivatives": {
        id: "2356",
        "category": "Dexs",
        "displayName": "El Dorado Exchange - Derivatives"
    },
    "fulcrom-finance-derivatives": {
        id: "2641",
        "displayName": "Fulcrom - Derivatives"
    },
    "vertex-protocol-derivatives": {
        id: "2899",
    },
    "voodoo-trade-derivatives": {
        id: "3792",
    },
    "pinnako-derivatives": {
        id: "3209",
        "category": "Dexs"
    },
    "y2k-v1": {
        id: "2375"
    },
    "y2k-v2": {
        id: "3056"
    },
    "drift-protocol-derivatives": {
        id: "970"
    },
    "grizzly-trade-derivatives": {
        id: "3301"
    },
    "ktx-derivatives": {
        id: "3025"
    },
    "gmx-v2-gmx-v2-trade": {
        "displayName": "GMX - Derivatives V2",
        id: "3365"
    },
    // "meridian-trade-derivatives": {
    //     id: "3386"
    // },
    "morphex-old-derivatives": {
        "displayName": "Morphex - Derivatives",
        id: "3483",
    },
    "nether-fi-derivatives": {
        id: "3509"
    },
    "bmx-derivatives": {
        id: "3530"
    },
    "mango-v4-perp": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1695081600": true
            }
        },
        id: "3174"
    },
    "blex-derivatives": {
        id: "3605"
    },
    "swapbased-perps": {
        id: "3373"
    },
    // "derivio-derivatives": {
    //     id: "3759"
    // },
    "beamex-beamex-perps": {
        id: "3251"
    },
    "orderly-network-orderly-network-derivatives": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1722816000": true
            }
        },
        id: "2264"
    },
    "helix-helix-perp": {
        id: "5074"
    },
    "lexer-derivatives": {
        id: "4087"
    },
    "demex-demex-perp": {
        id: "2001"
    },
    "blitz-derivatives": {
        id: "4214"
    },
    "nlx-nlx-trade": {
        id: "4568"
    },
    "amped-derivatives": {
        id: "3833"
    },
    "wavex-derivatives": {
        id: "5737"
    },
    "rfx-rfx-trade": {
        id: "5406"
    },
    "moonlander": {
        id: "5658"
    },
    "kanalabs-perp": {
        id: "6175"
    },
    "grvt-perps": {
        id: "5599"
    },
    "rho-protocol": {
        id: "6226"
    },
    "strike-finance": {
        id: "6173"
    },
    "reya-dex": { id: '4978' },
    "alkimiya": {
        id: "6344"
    },
    "privex": {
        id: "6419"
    },
    "k-bit": {
        id: "6446"
    },
    "elys-perp": {
        id: "6450"
    },
    "bluefin-pro": {
        id: "6451"
    },
    "ostrich": {
        id: "6442"
    },
    "hibachi": {
        id: "6467"
    },
    "axiom-perps": {
        id: "6511"
    },
    "phantom-perps": {
        id: "6512"
    },
    "pvp-trade": {
        id: "6510"
    },
    "okto-wallet": {
        id: "6513"
    },
    "based-app": {
        id: "6521"
    },
    "dexari": {
        id: "6531"
    },
    "insilico": {
        id: "6543"
    },
    "liquid-perps": {
        id: "6544"
    },
    "lootbase": {
        id: "6545"
    },
    "mass-dot-money": {
        id: "6546"
    },
    "dextrabot": {
        id: "6558"
    },
    "hyperdash": {
        id: "6559"
    },
    "kinto-xyz": {
        id: "6560"
    },
    "superX": {
        id: "6561"
    },
    "wallet-v": {
        id: "6562"
    },
    "rollx": {
        id: "5973"
    },
    "aden": {
        id: "6567"
    },
    "bullbit-ai": {
        id: "6584"
    },
    "clob": {
        id: "6607"
    }
} as AdaptorsConfig
