// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Protocol adaptor list is complete DERIVATIVES 1`] = `
Array [
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/emdx-dex/perpetual-protocol/blob/emdx/main/audit/2021-12%20EMDX%20Protocol%20Audit.final.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2299",
    },
    "description": "EMDX is a decentralized derivative protocol with an in-house spot market, expanding risk hedging tools for traditional assets into the DeFi environment and allowing token listing “on demand” with “liquidity as a service” from the start.",
    "disabled": false,
    "displayName": "EMDX",
    "enabled": true,
    "gecko_id": null,
    "id": "2299",
    "listedAt": 1669035629,
    "logo": "https://icons.llama.fi/emdx.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/emdx",
    "module": "emdx",
    "name": "EMDX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "emdx_io",
    "url": "https://emdx.io/",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/xvi10/gambit-contracts/tree/master/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "arbitrum",
      "avax",
    ],
    "cmcId": "11857",
    "config": Object {
      "enabled": true,
      "id": "337",
      "protocolsData": Object {
        "derivatives": Object {
          "displayName": "GMX - Derivatives",
          "enabled": true,
          "id": "337",
        },
      },
    },
    "description": "GMX is a decentralized spot and perpetual exchange that supports low swap fees and zero price impact trades. Trading is supported by a unique multi-asset pool that earns liquidity providers fees from market making, swap fees, leverage trading (spreads, funding fees & liquidations) and asset rebalancing.",
    "disabled": false,
    "displayName": "GMX - Derivatives",
    "enabled": true,
    "gecko_id": "gmx",
    "governanceID": Array [
      "snapshot:gmx.eth",
    ],
    "id": "337",
    "logo": "https://icons.llama.fi/gmx.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/gmx",
    "module": "gmx",
    "name": "GMX",
    "protocolType": undefined,
    "referralUrl": "https://gmx.io/#/?ref=defillama",
    "symbol": "GMX",
    "twitter": "GMX_IO",
    "url": "https://gmx.io/",
    "versionKey": "derivatives",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2320",
    },
    "description": "JOJO is a decentralized perpetual contract exchange based on an off-chain matching system.",
    "disabled": false,
    "displayName": "JOJO",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2320",
    "listedAt": 1669732079,
    "logo": "https://icons.llama.fi/jojo.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/jojo",
    "module": "jojo",
    "name": "JOJO",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "jojo_exchange",
    "url": "https://app.jojo.exchange/trade",
  },
  Object {
    "address": "kava:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2326",
    },
    "description": "KPerp.Exchange is a decentralized exchange created to offer a broad selection of trading options and extremely high levels of liquidity on numerous cryptocurrencies.",
    "disabled": false,
    "displayName": "KPerp Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": null,
    "id": "2326",
    "listedAt": 1669910621,
    "logo": "https://icons.llama.fi/kperp-exchange.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/kperp-exchange",
    "module": "kperp-exchange",
    "name": "KPerp Exchange",
    "oracles": Array [
      "TWAP",
      "Witnet",
    ],
    "protocolType": undefined,
    "symbol": "KPE",
    "twitter": "KPerpExchange",
    "url": "https://kperp.exchange/",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://github.com/metavaultorg/trade-contracts/blob/main/Metavault.Trade_Full_Smart_Contract_Security_Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "19973",
    "config": Object {
      "enabled": true,
      "id": "1801",
    },
    "description": "Metavault.Trade is a new kind of Decentralised Exchange, designed to provide a large range of trading features and very deep liquidity on many large cap crypto assets. Traders can use it in two ways: Spot trading, with swaps and limit orders. Perpetual futures trading with up to 50x leverage on short and long positions. Metavault.Trade aims to become the go-to solution for traders who want to stay in control of their funds at all times without sharing their personal data.",
    "disabled": false,
    "displayName": "Metavault.Trade",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "metavault-trade",
    "id": "1801",
    "listedAt": 1654203519,
    "logo": "https://icons.llama.fi/metavault.trade.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/metavault.trade",
    "module": "metavault.trade",
    "name": "Metavault.Trade",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Metavault",
    "protocolType": undefined,
    "symbol": "MVX",
    "twitter": "MetavaultTRADE",
    "url": "https://metavault.trade",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://www.synfutures.com/peckshield-audit-report-synfutures-v1.1.pdf",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Derivatives",
    "chain": "Ethereum",
    "chains": Array [
      "polygon",
      "arbitrum",
      "ethereum",
      "bsc",
    ],
    "cmcId": "1504",
    "config": Object {
      "enable": true,
      "id": "2328",
    },
    "description": "SynFutures is a next-generation derivatives exchange focused on creating an open and trustless derivatives market by enabling futures trading on anything, anytime, anywhere. By cultivating a free market and maximizing the variety of tradable assets, SynFutures is lowering the barrier to entry in the derivatives market, creating a more equitable derivatives market.",
    "disabled": false,
    "displayName": "SynFutures",
    "enable": true,
    "forkedFrom": Array [],
    "gecko_id": "synfutures",
    "id": "2328",
    "listedAt": 1669979746,
    "logo": "https://icons.llama.fi/synfutures.svg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/synfutures",
    "module": "synfutures",
    "name": "SynFutures",
    "oracles": Array [
      "Chainlink",
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "SynFuturesDefi",
    "url": "https://www.synfutures.com/",
  },
]
`;
