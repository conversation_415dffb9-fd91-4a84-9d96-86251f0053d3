import { AdaptorsConfig } from "../types"



export default {
    "lyra": {
        parentId: "Derive",
        "startFrom": 1656460800,
        id: "5060"
    },
    "thales": {
        id: "534"
    },
    "hegic": {
        id: "128"
    },
    // "opyn": {
    //     id: "285",
    // },
    "aevo": {
        id: "5059"
    },
    "typus": {
        id: "2946"
    },
    "rysk-finance": {
        id: "2605"
    },
    // "tigris": {
    //     id: "3129"
    // },
    "valorem": {
        id: "3501"
    },
    // "derivio": {
    //     parentId: "Deri",
    //     id: "3759",
    // },
    "dopex": {
        parentId: "Dopex",
        id: "3817"
    },
    "lyra-v2": {
        parentId: "Derive",
        id: "3923"
    },
    // "optionBlitz": { //sus behavior
    //     id: "4396"
    // },
    "smilee-finance": {
        id: "4350"
    },
    "moby": {
        id: "4452"
    },
    "ithaca": {
        id: "4418"
    },
    "jaspervault": {
        id: "4630"
    },
    "umoja": {
        id: "4963"
    },
    "pancakeswap-options": {
        parentId: "PancakeSwap",
        id: "4967"
    },
    "arrow-markets": {
        id: "4957"
    },
    "ton-hedge": {
        id: "4950"
    },
    "olab": {
        id: "5648"
    },
    // "paradex": {  // delisted because it is acting more like a perp
    //     id: "3648"
    // },
    "premia-v2": {
        id: "381"
    },
    "premia-v3": {
        id: "3497"
    },
    "ivx": {
        id: "6177"
    },
    "sofa-org": {
        id: "4779"
    },
    "optfun": {
        id: "6337"
    }
} as AdaptorsConfig
