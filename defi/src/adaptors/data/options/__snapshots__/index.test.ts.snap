// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Protocol adaptor list is complete OPTIONS 1`] = `
Array [
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Hegic-v1.0.pdf",
      "https://github.com/hegic/contracts/blob/main/packages/herge/docs/PeckShield-Audit-Report-Hegic-Herge-Protocol-Upgrade-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "6929",
    "config": Object {
      "enabled": true,
      "id": "128",
    },
    "description": "Hegic is an on-chain peer-to-pool options trading protocol on Arbitrum. You can trade ETH and WBTC ATM / OTM Calls / Puts & One-click Option Strategies on Hegic",
    "disabled": false,
    "displayName": "Hegic",
    "enabled": true,
    "gecko_id": "hegic",
    "id": "128",
    "logo": "https://icons.llama.fi/hegic.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/options/hegic",
    "module": "hegic",
    "name": "Hegic",
    "protocolType": undefined,
    "symbol": "HEGIC",
    "twitter": "HegicOptions",
    "url": "https://www.hegic.co/ ",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_links": Array [
      "https://github.com/lyra-finance/lyra-protocol/tree/master/audits/v1",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
      "arbitrum",
    ],
    "cmcId": "15447",
    "config": Object {
      "enabled": true,
      "id": "503",
      "startFrom": 1656460800,
    },
    "description": "The first complete decentralized options protocol built on Ethereum",
    "disabled": false,
    "displayName": "Lyra",
    "enabled": true,
    "gecko_id": "lyra-finance",
    "governanceID": Array [
      "snapshot:lyra.eth",
    ],
    "id": "503",
    "logo": "https://icons.llama.fi/lyra.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/options/lyra",
    "module": "lyra",
    "name": "Lyra",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "startFrom": 1656460800,
    "symbol": "LYRA",
    "twitter": "lyrafinance",
    "url": "https://www.lyra.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/Premia",
      "https://hacken.io/audits/#solidstate",
      "https://hacken.io/audits/#premia_finance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "fantom",
      "optimism",
    ],
    "cmcId": "8476",
    "config": Object {
      "enabled": true,
      "id": "381",
    },
    "description": "Premia's automated options market enables best-in-class pricing based on realtime supply and demand, bringing fully-featured peer-to-pool trading and capital efficiency to DeFi options.",
    "disabled": false,
    "displayName": "Premia",
    "enabled": true,
    "gecko_id": "premia",
    "id": "381",
    "logo": "https://icons.llama.fi/premia.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/options/premia",
    "module": "premia",
    "name": "Premia",
    "protocolType": undefined,
    "symbol": "PREMIA",
    "twitter": "PremiaFinance",
    "url": "https://premia.finance/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://opyn.gitbook.io/opyn/get-started/security#audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": false,
      "id": "285",
      "protocolsData": Object {
        "gamma": Object {
          "enabled": false,
          "id": "285",
        },
      },
    },
    "description": "Options market",
    "disabled": false,
    "displayName": "Opyn Gamma",
    "enabled": false,
    "gecko_id": null,
    "id": "285",
    "logo": "https://icons.llama.fi/opyn-gamma.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/options/opyn",
    "module": "opyn",
    "name": "Opyn Gamma",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Opyn",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "opyn_",
    "url": "https://www.opyn.co/",
    "versionKey": "gamma",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://iosiro.com/audits/thales-airdrop-and-staking-smart-contract-audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Prediction Market",
    "chain": "Ethereum",
    "chains": Array [
      "optimism",
      "polygon",
      "arbitrum",
      "bsc",
    ],
    "cmcId": "11973",
    "config": Object {
      "enabled": true,
      "id": "534",
    },
    "description": "Thales is an Ethereum protocol that allows the creation of peer-to-peer parimutuel markets that anyone can join. This building block is the foundation of novel on-chain initiatives, from a platform for AMM-based positional markets to immersive gamified experiences, and much more.",
    "disabled": false,
    "displayName": "Thales",
    "enabled": true,
    "gecko_id": "thales",
    "id": "534",
    "logo": "https://icons.llama.fi/thales.png",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/options/thales",
    "module": "thales",
    "name": "Thales",
    "protocolType": undefined,
    "symbol": "THALES",
    "twitter": "thalesmarket",
    "url": "https://thalesmarket.io/",
  },
]
`;
