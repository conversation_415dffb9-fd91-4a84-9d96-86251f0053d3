import { AdaptorsConfig } from "../types"

export default {
    "jumper.exchange": {
        id: "3524"
    },
    "xy-finance": {
        id: "1885"
    },
    "okx": {
        id: "5201",
    },
    "swing": {
        id: "5474",
    },
    "bitgetwallet": {
        id: "3207",
    },
    "teleswap": {
        id: "5532",
    },
    "rubic": {
        id: "1282",
    },
    "socket": {
        id: "6097"
    },
    "bungee-bridge": {
        id: "6188"
    },
    "rango": {
        id: "6382"
    },
    "dzap": {
        id: "6435"
    },
    "opensea": {
        id: "2258"
    },
    "orbiter-finance": {
        id: "6520"
    },
    "garden": {
        id: "6615"
    }
} as AdaptorsConfig
