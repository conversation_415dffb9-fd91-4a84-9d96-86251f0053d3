import { AdaptorsConfig } from "../types"

export default {
    "angle": {
        id: "756"
    },
    "biswap": {
        id: "373"
    },
    "bitcoin": {
        id: "1",
        "isChain": true
    },
    "bsc": {
        id: "56"
    },
    "compound": {
        id: "114"
    },
    "convex": {
        id: "319"
    },
    "curve": {
        id: "3"
    },
    "doge": {
        id: "74"
    },
    "ethereum": {
        id: "1027",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1651449600": true
            },
        }
    },
    "frax-swap": {
        id: "2121"
    },
    "gmx": {
        id: "337",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1744070398": true,
                "1743984000": true
            },
        }
    },
    "lido": {
        id: "182"
    },
    "litecoin": {
        id: "2",
        "isChain": true
    },
    "looksrare": {
        id: "1229"
    },
    "makerdao": {
        id: "118"
    },
    "mooniswap": {
        id: "1053"
    },
    "osmosis": {
        id: "383"
    },
    // "pancakeswap": {
    //     parentId: "PancakeSwap",
    //     protocolsData: {
    //         v1: {
    //             id: "2590"
    //         },
    //         v2: {
    //             id: "194"
    //         },
    //         stableswap: {
    //             id: "2529"
    //         },
    //         v3: {
    //             id: "2769"
    //         }
    //     },
    //     id: "194"
    // },
    "pangolin": {
        id: "246"
    },
    "raydium": {
        id: "214"
    },
    "spookyswap": {
        id: "302"
    },
    "synthetix": {
        id: "115"
    },
    "tarot": {
        id: "434"
    },
    // "uniswap": {
    //     id: "1",
    //     parentId: "Uniswap",
    //     "protocolsData": {
    //         "v1": {
    //             id: "2196"
    //         },
    //         "v2": {
    //             id: "2197"
    //         },
    //         "v3": {
    //             id: "2198"
    //         },
    //     },
    // },
    "velodrome": {
        id: "1799"
    },
    "wombat-exchange": {
        id: "1700"
    },
    "woofi": {
        id: "1461"
    },
    "metavault.trade": {
        id: "1801"
    },
    "aurora": {
        id: "1313161554"
    },
    "celo": {
        id: "42220"
    },
    "optimism": {
        "category": "Rollup",
        id: "10"
    },
    "moonbeam": {
        id: "1284"
    },
    "moonriver": {
        id: "1285"
    },
    "tron": {
        id: "1958"
    },
    "arbitrum": {
        "category": "Rollup",
        "startFrom": 1660608000,
        id: "42161"
    },
    "avalanche": {
        id: "43114"
    },
    "canto": {
        id: "21516"
    },
    "cardano": {
        id: "2010"
    },
    "cronos": {
        id: "25"
    },
    // "klaytn": { // wrong id, not related to klaytn
    //     id: "4256"
    // },
    "dodo-fees": {
        id: "146",
    },
    "fantom": {
        id: "250"
    },
    // "mixin": {
    //     id: "2349" // wrond id, not linked to mixin
    // },
    "polygon": {
        id: "137"
    },
    "solana": {
        id: "5426"
    },
    "xdai": {
        id: "100"
    },
    "abracadabra": {
        id: "347"
    },
    "liquity": {
        id: "270"
    },
    "geist-finance": {
        id: "643"
    },
    // "boba": {
    //     id: "14556" // Boba bridge id should be 3935
    // },
    "mojitoswap": {
        id: "1181"
    },
    "mimo": {
        id: "1241"
    },
    "junoswap": {
        id: "2052"
    },
    "honeyswap": {
        id: "271"
    },
    "solarbeam": {
        id: "551"
    },
    "spiritswap": {
        id: "311"
    },
    "apeswap": {
        id: "398"
    },
    // "nomiswap": {
    //     id: "1823"
    // },
    "stellaswap": {
        id: "1274"
    },
    "lifinity": {
        id: "2154"
    },
    "shibaswap": {
        id: "397"
    },
    "perp88": {
        id: "2296"
    },
    "mux": {
        id: "2254"
    },
    "emdx": {
        id: "2299"
    },
    "defi-swap": {
        id: "221"
    },
    "babydogeswap": {
        id: "2169"
    },
    "stargate": {
        id: "1571"
    },
    "mm-stableswap-polygon": {
        id: "2015"
    },
    "elk": {
        id: "420"
    },
    "lyra": {
        id: "503"
    },
    "radioshack": {
        id: "1616"
    },
    "valas-finance": {
        id: "1584"
    },
    "gains-network": {
        id: "1018"
    },
    "ghostmarket": {
        category: "NFT Marketplace",
        allAddresses: [
            "neo:******************************************",
            "ethereum:******************************************",
            "polygon:******************************************",
            "avax:******************************************",
            "bsc:******************************************"
        ],
        id: "2290"
    },
    "moonwell-artemis": {
        id: "1853"
    },
    "moonwell-apollo": {
        id: "1401"
    },
    "kperp-exchange": {
        id: "2326"
    },
    "llamalend": {
        id: "2252"
    },
    "0vix": {
        id: "1614"
    },
    "mummy-finance": {
        id: "2361"
    },
    "bluemove": {
        id: "2396"
    },
    "hegic": {
        id: "128",
        defaultChartView: "weekly",
    },
    "el-dorado-exchange": {
        id: "2356"
    },
    "gearbox": {
        id: "1108"
    },
    "verse": {
        id: "1732"
    },
    "level-finance": {
        id: "2395"
    },
    "blur": {
        id: "2414"
    },
    "solidlydex": {
        id: "2400"
    },
    "archly-finance": {
        id: "2317"
    },
    "stride": {
        id: "2251"
    },
    "plenty": {
        id: "490"
    },
    "firebird-finance": {
        id: "384"
    },
    "x2y2": {
        id: "1431"
    },
    "buffer": {
        id: "1304"
    },
    "betswirl": {
        id: "1911"
    },
    "zonic": {
        id: "2532"
    },
    "covo-finance": {
        id: "2525"
    },
    "nftearth": {
        id: "2546"
    },
    "liquid-bolt": {
        id: "2513"
    },
    "frax-ether": {
        id: "2221"
    },
    "frax-fpi": {
        id: "2607"
    },
    "zora": {
        id: "2610"
    },
    "solidlizard": {
        id: "2528"
    },
    "cow-protocol": {
        id: "2643"
    },
    "maverick": {
        id: "2644"
    },
    "equalizer-exchange": {
        id: "2332"
    },
    "camelot-v2": {
        id: "2307"
    },
    "thena-v1": {
        name: "Thena V1",
        displayName: "Thena V1",
        id: "2417"
    },
    "paraswap": {
        id: "894",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1684800000": true
            },
        }
    },
    "ramses-exchange-v1": {
        id: "2675"
    },
    "blastapi": {
        id: "2734"
    },
    "get-protocol": {
        id: "2735"
    },
    "radiant": {
        id: "2706"
    },
    "chainlink-vrf-v1": {
        displayName: "Chainlink VRF V1",
        id: "3339"
    },
    "chainlink-vrf-v2": {
        displayName: "Chainlink VRF V2",
        id: "3340"
    },
    "chainlink-keepers": {
        displayName: "Chainlink Keepers",
        id: "3338"
    },
    "chainlink-requests": {
        displayName: "Chainlink Requests",
        id: "2623"
    },
    "aura": {
        id: "1918"
    },
    "synapse": {
        id: "657"
    },
    "plexus": {
        id: "2740"
    },
    "vela": {
        id: "2548"
    },
    "equilibre-exchange": {
        id: "2586"
    },
    "waves": {
        id: "1274",
        isChain: true
    },
    "maia-v3": {
        id: "2760"
    },
    "morphex": {
        id: "2662"
    },
    "kyotoswap": {
        id: "2350"
    },
    "sonne-finance": {
        id: "2142"
    },
    "SmarDex": {
        id: "2695"
    },
    "ens": {
        id: "2519"
    },
    "azuro": {
        id: "1892"
    },
    "covo-v2": {
        id: "2730",
        cleanRecordsConfig: {
            genuineSpikes: true
        }
    },
    "camelot-v3": {
        id: "2792"
    },
    "auragi": {
        id: "2773"
    },
    "vesta-finance": {
        id: "1444"
    },
    "thena-v3": {
        id: "2864"
    },
    "merlin": {
        id: "2849"
    },
    "pika-protocol": {
        id: "916"
    },
    "chronos": {
        id: "2907"
    },
    "unidex": {
        id: "1833"
    },
    "joe-v2.1": {
        id: "2906"
    },
    "e3": {
        id: "2926"
    },
    "airswap": {
        id: "2954"
    },
    "across": {
        id: "1207"
    },
    "gnd-protocol": {
        id: "2968"
    },
    "kwenta": {
        id: "2981"
    },
    "gamma": {
        id: "355"
    },
    "fulcrom-finance": {
        id: "2641"
    },
    "veax": {
        id: "2928"
    },
    "maestro": {
        id: "3019"
    },
    "forge": {
        id: "2804"
    },
    "metamask": {
        id: "3031"
    },
    "rainbow-wallet": {
        id: "3038"
    },
    "lybra-finance": {
        id: "2904"
    },
    "houdini-swap": {
        id: "3041"
    },
    "unlimited-network": {
        id: "3055"
    },
    "cryptex-v2": {
        id: "3051"
    },
    "usdo": {
        id: "3098"
    },
    "unibot": {
        id: "3106"
    },
    "ramses-exchange-v2": {
        id: "3096"
    },
    "abcdefx": {
        id: "2376"
    },
    "liondex": {
        id: "2898"
    },
    "stealcam": {
        id: "3123"
    },
    "pearlfi": {
        id: "3121"
    },
    "scatter": {
        id: "3146"
    },
    "alchemix": {
        id: "204"
    },
    "doveswap": {
        id: "2809",
    },
    "foundation": {
        id: "3168"
    },
    "thalaswap": {
        id: "2795"
    },
    "yield-yak-staked-avax": {
        id: "475"
    },
    "voodoo-trade": {
        id: "3792"
    },
    "equity": {
        id: "3173"
    },
    "pendle": {
        id: "382"
    },
    "move-dollar": {
        id: "2789"
    },
    "pinnako": {
        id: "3209"
    },
    "DerpDEX": {
        id: "3234"
    },
    "wigoswap": {
        id: "1351"
    },
    "apollox": {
        id: "1772"
    },
    "concordex-io": {
        id: "3172"
    },
    "vvs-finance": {
        id: "831"
    },
    "agni-fi": {
        id: "3265"
    },
    "benqi-lending": {
        id: "467"
    },
    "pika-protocol-v4": {
        id: "3281"
    },
    "holdstation-defutures": {
        id: "2959"
    },
    "unicrypt": {
        id: "1765"
    },
    "0x0dex": {
        id: "3264"
    },
    "base": {
        "category": "Rollup",
        id: "8453"
    },
    "velodrome-v2": {
        id: "3302"
    },
    "sobal": {
        id: "3246"
    },
    "reserve": {
        id: "626"
    },
    "grizzly-trade": {
        id: "3301"
    },
    "rollup-finace": {
        id: "2889"
    },
    "ktx": {
        id: "3025"
    },
    "zunami": {
        id: "1201"
    },
    "fusionx-v3": {
        id: "3239"
    },
    "ferro": {
        id: "1882"
    },
    "satori": {
        id: "2982"
    },
    "fcon-dex": {
        id: "3299"
    },
    "friend-tech": {
        id: "3377"
    },
    "fusionx-v2": {
        id: "3238"
    },
    "vertex-protocol": {
        id: "2899"
    },
    "edebase": {
        id: "3375"
    },
    "venus-finance": {
        id: "212",
    },
    "none-trading-bot": {
        id: "3337",
    },
    "dackieswap": {
        id: "3345"
    },
    "banana-gun-trading": {
        id: "3336"
    },
    "lynex": {
        id: "3408"
    },
    "op-bnb": {
        id: "204",
        "isChain": true
    },
    "meowl": {
        id: "3418"
    },
    "qidao": {
        id: "449"
    },
    "zksync-era": {
        "category": "Rollup",
        id: "324"
    },
    "meridian-trade": {
        id: "3386"
    },
    "yfx-v3": {
        id: "3429"
    },
    "gmx-v2": {
        id: "3365"
    },
    "swapbased-v2": {
        id: "3328",
    },
    "swapbased-v3": {
        id: "3409",
    },
    "danogo": {
        id: "3454"
    },
    "sharesgram": {
        id: "3464"
    },
    // "tigris": {
    //     id: "3129"
    // },
    "apex": {
        id: "1878"
    },
    "lybra-v2": {
        id: "3468"
    },
    "morphex-old": {
        id: "3483"
    },
    "pact": {
        id: "1468"
    },
    "friend-room": {
        id: "3493",
    },
    "liquis": {
        id: "3498",
    },
    "dackieswap-v2": {
        id: "3515",
    },
    "basepaint": {
        id: "3519"
    },
    "monarchpay": {
        id: "3520"
    },
    "perpetual-protocol": {
        id: "362"
    },
    "nether-fi": {
        id: "3509"
    },
    "extra": {
        id: "2974"
    },
    "blazebot": {
        id: "3527"
    },
    "stakewise": {
        id: "277"
    },
    "bmx": {
        id: "3530"
    },
    "mango-v4": {
        id: "3174"
    },
    "hono": {
        id: "3532"
    },
    "thena-perp": {
        id: "3537",
    },
    "post-tech": {
        id: "3535",
    },
    "ekubo": {
        id: "3499"
    },
    "tangible-rwa": {
        id: "2231"
    },
    "caviar-tangible": {
        id: "3528"
    },
    "solidly-v3": {
        id: "3481"
    },
    "friend3": {
        id: "3566"
    },
    "Scale": {
        id: "3575"
    },
    "stars-arena": {
        id: "3564"
    },
    "based-markets": {
        id: "3609"
    },
    "allbridge-core": {
        id: "3944"
    },
    "cipher": {
        id: "3563"
    },
    "blex": {
        id: "3605"
    },
    "sudoswap-v1": {
        id: "1917"
    },
    "sudoswap-v2": {
        id: "3095"
    },
    "xena-finance": {
        id: "3620"
    },
    "gambit": {
        id: "3325"
    },
    "tangleswap": {
        id: "3585"
    },
    "uniswap-lab": {
        id: "3657"
    },
    "shimmersea": {
        id: "3571"
    },
    "vapordex-v2": {
        id: "3654",
    },
    "chainlink-ccip": {
        id: "3675"
    },
    "crv-usd": {
        id: "2994"
    },
    "shuriken": {
        id: "3687"
    },
    "clipper": {
        id: "622"
    },
    // "morpho-compound": {   // https://discord.com/channels/823822164956151810/1022274454451142800/1166542892999913583
    //     id: "1997"
    // },
    "benqi-staked-avax": {
        id: "1427"
    },
    "prisma-finance": {
        id: "3473"
    },
    "impermax-finance": {
        id: "343"
    },
    "defi-saver": {
        id: "177"
    },
    "zapper-channels": {
        id: "3703"
    },
    "valorem": {
        id: "3501"
    },
    "clever": {
        id: "1707"
    },
    "concentrator": {
        id: "1544"
    },
    "touch.fan": {
        id: "3713"
    },
    "paal-ai": {
        id: "3723"
    },
    "retro": {
        id: "3311"
    },
    "hipo": {
        id: "3722"
    },
    "intent-x": {
        id: "3747"
    },
    "caviarnine-lsu-pool": {
        id: "3666"
    },
    "caviarnine-shape-liquidity": {
        id: "3645"
    },
    // "metavault-v3": {
    //     id: "3750",
    // },
    "xoxno": {
        id: "3753"
    },
    "equation": {
        id: "3726"
    },
    "hopr": {
        id: "3761"
    },
    "solend": {
        id: "458"
    },
    "thorswap": {
        id: "412"
    },
    "amphor": {
        id: "3643"
    },
    "dydx": {
        id: "144",
    },
    "justlend": {
        id: "494"
    },
    "wagmi": {
        id: "2837"
    },
    "chimpexchange": {
        id: "3836"
    },
    "dln": {
        id: "1462"
    },
    "near": {
        id: "6535"
    },
    "substanceX": {
        id: "3835"
    },
    "up-vs-down-game": {
        id: "3872"
    },
    "aimbot": {
        id: "3875"
    },
    "sns": {
        id: "3877"
    },
    "thick": {
        id: "3878"
    },
    "noah-swap": {
        id: "2855"
    },
    "stormtrade": {
        id: "3883"
    },
    "beethoven-x": {
        id: "654"
    },
    "ascent-v2": {
        id: "3867"
    },
    "ascent-v3": {
        id: "3868"
    },
    "xfai": {
        id: "3816"
    },
    "defiplaza": {
        id: "728"
    },
    "butterxyz": {
        id: "3918"
    },
    "pharaoh-exchange": {
        id: "3921"
    },
    "metavault-derivatives-v2": {
        id: "3911"
    },
    "dopex": {
        id: "3817",
    },
    "bluefin": {
        id: "2625"
    },
    "odos": {
        id: "3951"
    },
    "dexter": {
        id: "2737"
    },
    "fvm-exchange": {
        id: "3291"
    },
    "kiloex": {
        id: "3329"
    },
    "railgun": {
        id: "1320"
    },
    "surfone": {
        id: "3954"
    },
    "squa-defi": {
        id: "3977"
    },
    "beamex": {
        id: "3251"
    },
    "beamswap-v3": {
        id: "3092",
    },
    "beamswap": {
        id: "1289"
    },
    "shoebillFinance-v2": {
        id: "3548"
    },
    "pepe-swaves": {
        id: "2351"
    },
    "maple-finance": {
        id: "587"
    },
    "jibswap": {
        id: "3928"
    },
    "cleopatra-exchange": {
        id: "3985"
    },
    "immortalx": {
        id: "3983"
    },
    "goku-money": {
        id: "3758"
    },
    "allbridge-classic": {
        id: "577"
    },
    "monocerus": {
        id: "3622"
    },
    "first-crypto-bank": {
        id: "4017"
    },
    "fwx": {
        id: "4026"
    },
    "keom": {
        id: "3823"
    },
    "squadswap-v2": {
        id: "4009"
    },
    "squadswap-v3": {
        id: "4010"
    },
    "zerion-wallet": {
        id: "4049"
    },
    "goldfinch": {
        id: "703"
    },
    "zkswap-finance": {
        id: "3180"
    },
    "horiza": {
        id: "4041"
    },
    "manta": {
        category: "Rollup",
        id: "169"
    },
    "equation-v2": {
        id: "4074"
    },
    "lexer": {
        id: "4087"
    },
    "garden": {
        id: "4086"
    },
    "hyperionx": {
        id: "4094"
    },
    "kinetix-derivatives-v2": {
        id: "4110"
    },
    "pingu": {
        id: "4102"
    },
    "supswap-v2": {
        id: "4117"
    },
    "supswap-v3": {
        id: "4118"
    },
    "vaultka": {
        id: "2531"
    },
    "Omnidrome": {
        id: "4119"
    },
    "marinade-liquid-staking": {
        id: "484",
        "cleanRecordsConfig": {
            genuineSpikes: {
                "1708387200": true,
                "1708473600": true,
                "1708560000": true,
                "1708646400": true,
            }
        }
    },
    "marinade-native": {
        id: "3672"
    },
    "inverse-finance": {
        id: "2433"
    },
    "furucombo": {
        id: "742"
    },
    "instadapp": {
        id: "4742" // old id: 120
    },
    "summer.fi": {
        id: "4741"  // old id: 3284
    },
    "integral": {
        id: "291"
    },
    "bonk-bot": {
        id: "4227"
    },
    "lens-protocol": {
        id: "4235"
    },
    "ethena": {
        id: "4133",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1754524800": true
            },
        }
    },
    "avantis": {
        id: "4108"
    },
    "cellana-finance": {
        id: "4194",
    },
    "nile-exchange": {
        id: "4072"
    },
    "nile-exchange-v1": {
        id: "4285"
    },
    "primordium": {
        id: "4293",
    },
    "geodnet": {
        id: "4304",
    },
    "econia": {
        id: "4128"
    },
    "sharpe-earn": {
        id: "2756"
    },
    "morpho": {
        id: "4025"
    },
    "blitz": {
        id: "4214",
    },
    "fx-protocol": {
        id: "3344"
    },
    "swop": {
        id: "613"
    },
    "javsphere": {
        id: "4366"
    },
    "frax-amo": {
        id: "359"
    },
    "keller": {
        id: "4388"
    },
    "koi-finance": {
        id: "2727"
    },
    "ash-perp": {
        id: "4426"
    },
    "optionBlitz": {
        id: "4396"
    },
    "pumpdotfun": {
        id: "4449",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1752537600": true,
                "1754956800": true
            },
        }
    },
    "synthetix-v3": {
        id: "4446"
    },
    "beefy": {
        id: "326"
    },
    "etaswap": {
        id: "4475"
    },
    "swych": {
        id: "4365",
    },
    "wbtc": {
        id: "2"
    },
    "yologames": {
        id: "4495"
    },
    "grizzly-trade-derivatives-v2": {
        id: "4506",
    },
    "merchant-moe-dex": {
        id: "4006"
    },
    "hercules-v2": {
        id: "4372",
    },
    "hercules-v3": {
        id: "4373",
    },
    "orby-network": {
        id: "4154"
    },
    "fantasy-top": {
        id: "4570"
    },
    "fluid": {
        id: "4167"
    },
    "bitlayer": {
        id: "200901"
    },
    "nuri-exchange-v1": {
        id: "4564"
    },
    "nuri-exchange-v2": {
        id: "4565"
    },
    "synfutures-v3": {
        id: "4215"
    },
    "jito": {
        id: "6571"
    },
    "vfat": {
        id: "4602"
    },
    "dydx-v4": {
        id: "4067"
    },
    "polter": {
        id: "4152"
    },
    "equation-v3": {
        id: "4586"
    },
    "Viridian": {
        id: "4631"
    },
    "yfx-v4": {
        id: "4674"
    },
    "basecamp": {
        id: "4693"
    },
    "blocxroute": {
        id: "4695"
    },
    "drift-protocol-derivatives": {
        id: "970",
    },
    "keller-cl": {
        id: "4583"
    },
    "colony": {
        id: "1004"
    },
    "flashtrade": {
        id: "4107",
    },
    "pumpup": {
        id: "4736"
    },
    "coinbase-commerce": {
        id: "4737"
    },
    "dragonswap-sei": {
        id: "4720"
    },
    "wen-markets": {
        id: "4733"
    },
    "cellula": {
        id: "4705"
    },
    "clusters": {
        id: "4777"
    },
    "magpie": {
        id: "2271"
    },
    "time-fun": {
        id: "4786"
    },
    "cakepie": {
        id: "4007"
    },
    "milkyway": {
        id: "3953"
    },
    "penpie": {
        id: "3083"
    },
    "radpie": {
        id: "3555"
    },
    "sideshift": {
        id: "1895"
    },
    "tlx-finance": {
        id: "4555"
    },
    "moonshot": {
        id: "4813"
    },
    "jojo": {
        id: "2320"
    },
    "lynex-v1": {
        id: "3908"
    },
    "linehub-perps": {
        id: "4842"
    },
    "scoop": {
        id: "4827"
    },
    "ether-fi": {
        id: "4429"
    },
    "jup-ape": {
        id: "4860"
    },
    "scallop": {
        id: "1961"
    },
    "d2finance": {
        id: "4846"
    },
    "eddyfinance-v2": {
        id: "4120"
    },
    "size-credit": {
        id: "4904"
    },
    // "stbot": {  // duplicate of sol-trading-bot
    //     id: "4909"
    // },
    "zns": {
        id: "4920"
    },
    "liquid-collective": {
        id: "3391"
    },
    "juice-finance": {
        id: "4208"
    },
    "origin-dollar": {
        id: "427"
    },
    // "betmode": { // has negative fees for many dates
    //     id: "4927"
    // },
    "silo-finance": {
        id: "2020"
    },
    "cetus": {
        id: "2289"
    },
    "arrakis-v2": {
        id: "2667"
    },
    "stargate-finance-v2": {
        id: "4831"
    },
    "superstate": {
        id: "4265"
    },
    "apex-omni": {
        id: "4822"
    },
    "dedust": {
        id: "2617"
    },
    "orderly": {
        id: "2264"
    },
    "spacewhale": {
        id: "4930"
    },
    "mevx": {
        id: "4945"
    },
    "metaplex": {
        id: "4959"
    },
    "umoja": {
        id: "4963"
    },
    "goplus": {
        id: "4977"
    },
    "photon": {
        id: "4981"
    },
    "factor": {
        id: "3298"
    },
    "dexscreener": {
        id: "4990"
    },
    "kamino-lending": {
        id: "3770"
    },
    "ston": {
        id: "2337"
    },
    // "moonwell": {
    //     id: "1853"
    // },
    "spiko": {
        id: "4980"
    },
    "helio": {
        id: "5007"
    },
    "sunpump": {
        id: "4979"
    },
    "dextools": {
        id: "5006"
    },
    "manifold": {
        id: "5005"
    },
    "circle": {
        id: "5008"
    },
    "tether": {
        id: "5009"
    },
    "thegraph": {
        id: "5010"
    },
    "demented-games": {
        id: "5013"
    },
    "kyberswap-aggregator": {
        id: "3982",
    },
    "raybot": {
        id: "5022"
    },
    "illuvium": {
        id: "447"
    },
    "4cast": {
        id: "5027"
    },
    "bellumexchange": {
        id: "5029"
    },
    "ribbon": {
        id: "281"
    },
    "velo": {
        id: "4989"
    },
    "openeden-t-bills": {
        id: "3057"
    },
    "bcraft": {
        id: "5036"
    },
    "paxos-gold": {
        id: "4862"
    },
    "chainflip": {
        id: "3853"
    },
    "franklin-templeton": {
        id: "4878"
    },
    "hashnote-usyc": {
        id: "3698"
    },
    "farcaster": {
        id: "5049"
    },
    "lista-slisbnb": {
        id: "3354"
    },
    "lista-lisusd": {
        id: "2038"
    },
    "ethervista": {
        id: "5092"
    },
    "echelon": {
        id: "4367"
    },
    "torch": {
        id: "5096"
    },
    "sunswap-v1": {
        id: "690"
    },
    "sunswap-v2": {
        id: "3005"
    },
    "sunswap-v3": {
        id: "4031"
    },
    "fwx-dex": {
        id: "4962"
    },
    "koi-finance-cl": {
        id: "4678"
    },
    "superchain": {
        id: "5111"
    },
    "gaspump": {
        id: "5094"
    },
    "zeno": {
        id: "4642"
    },
    "dhedge": {
        id: "190"
    },
    "ref-finance": {
        id: "541"
    },
    "bmx-freestyle": {
        id: "4903"
    },
    "pear-protocol": {
        id: "5151"
    },
    "solv-finance": {
        id: "4620"
    },
    "sparkdex-v3": {
        id: "4888"
    },
    "toros": {
        id: "1881"
    },
    "myx-finance": {
        id: "4319"
    },
    "erinaceus": {
        id: "5183"
    },
    "moonshot-money": {
        id: "5188"
    },
    "filament": {
        id: "5650",
    },
    "bifrost-liquid-staking": {
        id: "1738"
    },
    "pocket-universe": {
        id: "5189"
    },
    "memecooking": {
        id: "5185"
    },
    "prerich-app": {
        id: "5196"
    },
    "trado": {
        id: "5208",
    },
    "ntm": {
        id: "5212",
    },
    "jeton": {
        id: "5213",
    },
    "sparkdex-v3-1": {
        id: "5223"
    },
    "sparkdex-v2": {
        id: "4887",
    },
    "arbitrum-nova": {
        id: "42170",
    },
    "kerberos": {
        id: "5233",
    },
    "safe": {
        id: "3320"
    },
    "botfalcon": {
        id: "5237"
    },
    "mint": {
        id: "185"
    },
    "grafun": {
        id: "5195"
    },
    "wise-lending-v2": {
        id: "4494"
    },
    "makenow-meme": {
        id: "5246"
    },
    "linehub-v3": {
        id: "4661"
    },
    "linehub-v2": {
        id: "4660"
    },
    "quickswap-hydra": {
        id: "5187"
    },
    "quickswap-perps": {
        id: "2980",
    },
    "step-finance": {
        id: "4837"
    },
    "assetchain": {
        id: "42420"
    },
    "kinetix-v2": {
        id: "3533"
    },
    "kinetix-v3": {
        id: "3534"
    },
    "metavault-amm-v2": {
        id: "5186"
    },
    "surge-trade": {
        id: "5290"
    },
    "suilend": {
        id: "4274"
    },
    "juicebox": { // adapter not working
        id: "2833"
    },
    "celestia": {
        id: "22861"
    },
    "yamfore": {
        id: "5304"
    },
    "bonzo": {
        id: "5287"
    },
    "quenta": {
        id: "5314"
    },
    "fluid-dex": {
        id: "5317"
    },
    "g8keep": {
        id: "5318"
    },
    "iziswap": {
        id: "1883"
    },
    "dragonswap-sei-v3": {
        id: "5066"
    },
    "morFi": {
        id: "5307"
    },
    "solar-studios": {
        id: "5346"
    },
    "orca": {
        id: "283"
    },
    "stabble": {
        id: "4734"
    },
    "kamino-liquidity": {
        id: "2062"
    },
    "blazingbot": {
        id: "5377"
    },
    "goat-protocol": {
        id: "4162"
    },
    "adrena": {
        id: "5353"
    },
    "phantom": {
        id: "5398"
    },
    "bullx": {
        id: "5407"
    },
    "gmgnai": {
        id: "5408"
    },
    "debank-cloud": {
        id: "5410"
    },
    "navi": {
        id: "3323",
    },
    "hydrometer": {
        id: "5423",
    },
    "bluefin-amm": {
        id: "5427",
    },
    "taraswap": {
        id: "5437",
    },
    "clanker": {
        id: "5446"
    },
    "bouncebit-cedefi": {
        id: "5450"
    },
    "swing": {
        id: "5474"
    },
    "thetis-market": {
        id: "5469"
    },
    "iotex": {
        id: "4689"
    },
    "lyra-v2": {
        id: "3923"
    },
    "balancer-v3": {
        id: "5491"
    },
    "memewe": {
        id: "5501"
    },
    "mars-perp": {
        id: "5498",
    },
    "neby-dex": {
        id: "5512"
    },
    "sudofinance": {
        id: "4045"
    },
    "emojicoin": {
        id: "5454"
    },
    "invariant": {
        id: "1788"
    },
    "memejob": {
        id: "5533"
    },
    "hyperliquid": {
        displayName: "Hyperliquid",
        id: "5761"
    },
    "liquidity-slicing": {
        id: "5297"
    },
    "zivoe": {
        id: "5551"
    },
    "rabbitswap-v3": {
        id: "5298"
    },
    "satoshi-perps": {
        id: "5571",
    },
    "virtual-protocol": {
        id: "5575",
    },
    "trust-wallet": {
        id: "5577",
    },
    "coinbase-wallet": {
        id: "5587",
    },
    "dappos-intentEx": {
        id: "5597",
    },
    "volboost": {
        id: "5598",
    },
    "creator-bid": {
        id: "5600",
    },
    "eisen": {
        id: "4691",
    },
    "vader-ai": {
        id: "5535",
    },
    "maxapy": {
        id: "5306",
    },
    // "zeebu": { // do not trust the numbers, 50k+ rewards go to wallets who immediately send to cexes
    //     id: "5540",
    // },
    "lnexchange-perp": {
        id: "5639",
    },
    "waterneuron": {
        id: "4921",
    },
    "meteora-dlmm": {
        id: "4148",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1755734400": true, // Kanye West token YZY launch
            }
        }
    },
    "meteora": {
        id: "385",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1747180800": true,
            }
        }
    },
    "zarban": {
        id: "5636",
    },
    "vectorfun": {
        id: "5653"
    },
    "soneium": {
        id: "1868"
    },
    "ink": {
        id: "57073"
    },
    "cvex": {
        id: "5610"
    },
    "hfun": {
        id: "5671"
    },
    "tribe-run": {
        id: "5669"
    },
    "zoodotfun": {
        id: "5684"
    },
    "quill-fi": {
        id: "5685"
    },
    "liquity-v2": {
        id: "5656"
    },
    "jupiter": {
        id: "2141"
    },
    "rabby": {
        id: "5714"
    },
    "looter": {
        id: "5728"
    },
    "flaunch": {
        id: "5715"
    },
    // "jumper-exchange": {  // As jumper charges 0 fees and all the fee goes to bridges, so no point in showing double fees
    //     id: "3524",
    // },
    "ocelex": {
        id: "5379"
    },
    "levvy-fi": {
        id: "3163"
    },
    "levvy-fi-tokens": {
        id: "3618"
    },
    "bunni-v2": {
        id: "5734"
    },
    "amped": {
        id: "3833"
    },
    "vinufinance": {
        id: "5717"
    },
    "jupiter-perpetual": {
        id: "4077"
    },
    "wavex": {
        id: "5737"
    },
    "berachain-hub": {
        id: "5742"
    },
    // "polymarket": { // fees value is wrong, protocol does not charge fees
    //     id: "711"
    // },
    "velar": {
        id: "4339"
    },
    "four-meme": {
        id: "5174"
    },
    "pulsex-v1": {
        id: "2995"
    },
    "pulsex-v2": {
        id: "3060"
    },
    "pulsex-stableswap": {
        id: "5795",
    },
    "kodiak-v2": {
        id: "5743",
    },
    "bloom": {
        id: "5806",
    },
    "flashbot": {
        id: "5809",
    },
    "silverswap": {
        id: "5529",
    },
    "ducata": {
        id: "4896",
    },
    "embr": {
        id: "1063",
    },
    "gaming-dex": {
        id: "4263",
    },
    "ginsengswap": {
        id: "5803",
    },
    "polaris-fi": {
        id: "1440",
    },
    "phux": {
        id: "3205",
    },
    "tanukix": {
        id: "5038",
    },
    "magma-finance": {
        id: "5774",
    },
    "jupiter-dca": {
        id: "5841",
    },
    "sablier": {
        id: "199",
    },
    "goplus-locker": {
        id: "5807",
    },
    "caviarnine-simplepool": {
        id: "5064",
    },
    "hedgey": {
        id: "5846"
    },
    "steamm": {
        id: "5824"
    },
    "flexperp": {
        id: "5843"
    },
    "blast": {
        id: "81457"
    },
    "fraxtal": {
        id: "252"
    },
    "linea": {
        id: "59144"
    },
    "mantle": {
        id: "5000"
    },
    "metis": {
        id: "1088"
    },
    "mode": {
        id: "34443"
    },
    "ronin": {
        id: "2020"
    },
    "scroll": {
        id: "534352"
    },
    "sei": {
        id: "23149"
    },
    "sonic": {
        id: "146"
    },
    "unichain": {
        id: "130"
    },
    "berachain": {
        id: "80094"
    },
    "burrbear": {
        id: "5745"
    },
    "fibonacci-dex": {
        id: "5832"
    },
    "starknet": {
        id: "22691"
    },
    "sailor-finance": {
        id: "5647",
    },
    "hyperswap-v2": {
        id: "5836"
    },
    "hyperswap-v3": {
        id: "5837"
    },
    "wasabee": {
        id: "5845"
    },
    "thalaswap-v2": {
        id: "5329",
    },
    "degen-launchpad": {
        id: "5857"
    },
    "tronsave": {
        id: "5864"
    },
    "erc-burner": {
        id: "5859"
    },
    "infrared-finance": {
        id: "5775"
    },
    "desk": {
        id: "5813"
    },
    "beradrome": {
        id: "5798",
        defaultChartView: "weekly",
    },
    "rings": {
        id: "5526"
    },
    "hyperion": {
        id: "5480"
    },
    "ton": {
        id: "11419"
    },
    "sui": {
        id: "20947"
    },
    "aptos": {
        id: "21794"
    },
    "metropolis-amm": {
        id: "5504"
    },
    "metropolis-dlmm": {
        id: "5505"
    },
    "tea-fi": {
        id: "5875"
    },
    "beethoven-x-v3": {
        id: "5680"
    },
    "trade-wiz": {
        id: "5886"
    },
    "rain": {
        id: "3903"
    },
    "SwapX-algebra": {
        id: "5579"
    },
    "SwapX-v2": {
        id: "5578"
    },
    "meridian-amm": {
        id: "5885"
    },
    "merkle-trade": {
        id: "3678"
    },
    "gyroscope": {
        id: "2397"
    },
    "wildcat": {
        id: "3871"
    },
    "napier": {
        id: "4834"
    },
    "yuzu-finance": {
        id: "5906"
    },
    "beets-staked-sonic": {
        id: "4126"
    },
    "stout": {
        id: "5902"
    },
    "rfx": {
        id: "5406"
    },
    "stability": {
        id: "4256"
    },
    "sedge": {
        id: "5930"
    },
    "vicuna-finance": {
        id: "5672"
    },
    "eggs-finance": {
        id: "5789"
    },
    "tonco": {
        id: "5363"
    },
    "witty": {
        id: "5908"
    },
    "logx": {
        id: "5137"
    },
    // "ekubo-evm": { // merged into ekubo https://github.com/DefiLlama/defillama-server/commit/3aba710f2a43514ddd5c64368670df078144361b
    //     id: "5914"
    // },
    "contango": {
        id: "3602"
    },
    "ripple": {
        id: "52"
    },
    "equalizer-cl": {
        id: "5603"
    },
    "balanced": {
        id: "448"
    },
    "xswap-v3": {
        id: "3914"
    },
    "xswap-v2": {
        id: "2145"
    },
    "mayan": {
        id: "5925"
    },
    "unchain-x": {
        id: "5917"
    },
    "aavechan": {
        id: "5926"
    },
    "paint-swap": {
        id: "421"
    },
    "energyweb": {
        id: "246"
    },
    "finder-bot": {
        id: "5934"
    },
    "sol-trading-bot": {
        id: "4909"
    },
    "suite": {
        id: "5933"
    },
    "stakedao": {
        id: "249"
    },

    "getHemiNames": {
        id: "5929"
    },
    "apechain": {
        id: "33139"
    },
    "chiliz": {
        id: "88888"
    },
    "ethereumclassic": {
        id: "61"
    },
    "etherlink": {
        id: "42793"
    },
    "flare": {
        id: "4172"
    },
    "fuse": {
        id: "122"
    },
    "harmony": {
        id: "1666600000"
    },
    "hemi": {
        id: "43111"
    },
    "imx": {
        id: "13371"
    },
    "kardia": {
        id: "5453"
    },
    "kcc": {
        id: "321"
    },
    "kroma": {
        id: "255"
    },
    "lisk": {
        id: "1135"
    },
    "nuls": {
        id: "2092"
    },
    "oasys": {
        id: "16116"
    },
    "redstone": {
        id: "690"
    },
    "rootstock": {
        id: "30"
    },
    "shimmer_evm": {
        id: "148"
    },
    "story": {
        id: "1514"
    },
    "syscoin": {
        id: "57"
    },
    "telos": {
        id: "40"
    },
    "thundercore": {
        id: "108"
    },
    "velas": {
        id: "106"
    },
    "zeta": {
        id: "7000"
    },
    "archerswap": {
        id: "2648"
    },
    "superposition": {
        id: "55244"
    },
    "gacha": {
        id: "5942"
    },
    "pump-swap": {
        id: "5936"
    },
    "agdex": {
        id: "5467"
    },
    "ancient8": {
        id: "888888888"
    },
    "bob": {
        id: "60808"
    },
    "corn": {
        id: "21000000"
    },
    "gravity": {
        id: "1625"
    },
    "iota_evm": {
        id: "8822"
    },
    "lightlink": {
        id: "1890"
    },
    "reya": {
        id: "1729"
    },
    "swellchain": {
        id: "1923"
    },
    "worldchain": {
        id: "480"
    },
    "hop-protocol": {
        id: "435"
    },
    "squadswap-dynamo": {
        id: "5921"
    },
    "squadswap-wow": {
        id: "5922"
    },
    "magnum-trading-bot": {
        id: "5945"
    },
    "canto-lending": {
        id: "1986"
    },
    "deepr-finance": {
        id: "4015"
    },
    "elara": {
        id: "5390"
    },
    "fluxfinance": {
        id: "2537"
    },
    "hover": {
        id: "3822"
    },
    "ironbank": {
        id: "1303"
    },
    "machfi": {
        id: "5573"
    },
    "mendi-finance": {
        id: "3421"
    },
    "strike": {
        id: "589"
    },
    "sumer": {
        id: "3814"
    },
    "traderjoe-lend": {
        id: "2179"
    },
    "vaultcraft": {
        id: "1791"
    },
    "dextoro": {
        id: "5954"
    },
    "move": {
        id: "3073"
    },
    "snakefinance": {
        id: "5760"
    },
    "baker-dao": {
        id: "5964"
    },
    "hedera": {
        id: "295"
    },
    "pinksale": {
        id: "1807"
    },
    "katana": {
        id: "797"
    },
    "saucerswap": {
        id: "1979"
    },
    "saucerswap-v2": {
        id: "5966"
    },
    "syncswap": {
        id: "2728"
    },
    "filecoin": {
        id: "314"
    },
    "paycash": {
        id: "1452"
    },
    "paradex": {
        id: "3648"
    },
    "tornado": {
        id: "148"
    },
    "karak": {
        id: "2410" // chainId
    },
    "saber": {
        id: "419"
    },
    "justbet": {
        id: "5950"
    },
    "katana-v3": {
        id: "5972"
    },
    "privacy-pools": {
        id: "5977"
    },
    "syncswap-v3": {
        id: "5982"
    },
    // "oxfun": {  // it is a cex and fees cant be verified on chain
    //     id: "6699" // chainId  
    // },
    "verus": {
        id: "5601"
    },
    "haedal": {
        id: "5784"
    },
    "haedal-vault": {
        id: "5967"
    },
    "equilibria": {
        id: "3091"
    },
    // "arweave": {
    //     id: "35386", // cmcId // this is arweave fees, should not be linked to AO
    // },
    "usdx": {
        id: "5234"
    },
    "rocketpool": {
        id: "900"
    },
    "winr": {
        id: "777777" // chainId
    },
    "sofa-org": {
        id: "4779"
    },
    "edgex": {
        id: "4954"
    },
    "royco": {
        id: "5337"
    },
    "dolomite": {
        id: "2187"
    },
    "stader": {
        id: "1044"
    },
    "cian-yieldlayer": {
        id: "5376"
    },
    "beraborrow": {
        id: "5746"
    },
    "apestore": {
        id: "4584"
    },
    "euler": { // euler v2
        id: "5044"
    },
    "hardswap": {
        id: "5999"
    },
    "spark": {
        id: "2929"
    },
    "hypurrfi": {
        id: "5838"
    },
    "stakestone-stone": {
        id: "3558"
    },
    "resupply": {
        id: "5963"
    },
    "momentum": {
        id: "6005"
    },
    "sonex": {
        id: "5640"
    },
    "kyo-fi-v3": {
        id: "5622"
    },
    "hashkey": {
        id: "177" // chainId
    },
    "polynomial": {
        id: "8008" // chainId
    },
    "veda": {
        id: "5825"
    },
    "felix": {
        id: "6015"
    },
    "zo": {
        id: "6018"
    },
    "liquidloans-io": {
        id: "4043"
    },
    "powercity-earn-protocols": {
        id: "4376"
    },
    "powercity-flex-protocols": {
        id: "4881"
    },
    "sable-finance": {
        id: "3355"
    },
    "teddy-cash": {
        id: "535"
    },
    "threshold-thusd": {
        id: "4812"
    },
    "jellyverse": {
        id: "4772",
    },
    "usual": {
        id: "4882"
    },
    "ondo": {
        id: "2542"
    },
    "traderjoe-lb-v2-2": {
        id: "4794"
    },
    "kongswap": {
        id: "5528"
    },
    "alpacafinance-gmx": {
        id: "2658"
    },
    "phame-protocol": {
        id: "3569"
    },
    "sobax-io": {
        id: "4143"
    },
    "tsunami-fi": {
        id: "2979"
    },
    "kinetix-v1": {
        id: "3465"
    },
    "axiom": {
        id: "6049"
    },
    "mellow-lrt": {
        id: "4756"
    },
    "liquid-ron": {
        id: "6020"
    },
    "razordex": {
        id: "6054"
    },
    "yei-finance": {
        id: "4721"
    },
    "chedda-finance": {
        id: "5912"
    },
    "arbitrum-timeboost": {
        id: "6065"
    },
    "kairos": {
        id: "6066"
    },
    "ostium": {
        id: "4932"
    },
    "lista-lending": {
        id: "6056"
    },
    "zora-chain": {
        id: "7777777" // chainId
    },
    "zora-sofi": {
        id: "6069"
    },
    "yearn-ether": {
        id: "3973"
    },
    "yearn-finance": {
        id: "113"
    },
    "compound-v3": {
        id: "2088"
    },
    "amnis-finance": {
        id: "3667"
    },
    "kittypunch-v3": {
        id: "6051"
    },
    "sonicxswap": {
        id: "6083"
    },
    "m0": {
        id: "5241"
    },
    "bucket-protocol": {
        id: "3206"
    },
    "oogabooga": {
        id: "5880"
    },
    "typus-dov": {
        id: "2946"
    },
    "typus-perp": {
        id: "6014"
    },
    "typus-safu": {
        id: "5152"
    },
    "echo": {
        id: "6091"
    },
    "sanctum": {
        id: "3388"
    },
    "lifi": {
        id: "6093"
    },
    "humanfi": {
        id: "6094"
    },
    "launchlab": {
        id: "6074"
    },
    "sanctum-infinity": {
        id: "4368"
    },
    "sanctum-validator-lsts": {
        id: "4371"
    },
    "socket": {
        id: "6097"
    },
    "mosaic-amm": {
        id: "6098"
    },
    "lobbyfi": {
        id: "6109"
    },
    "assetchain-swap": {
        id: "5324"
    },
    "1dex": {
        id: "6003"
    },
    "eigenlayer": {
        id: "3107"
    },
    "kittypunch": {
        id: "5203"
    },
    "binance-staked-eth": {
        id: "2914"
    },
    "boop-fun": {
        id: "6129"
    },
    "bancor-v2": { id: "162" },
    "bancor-v3": { id: "1995" },
    "pancakeswap-infinity": {
        id: "6133"
    },
    "catex": {
        id: "6052"
    },
    "voltage-v4": {
        id: "5754"
    },
    "initia-dex": {
        id: "6138"
    },
    "yakafinance": {
        id: "4871"
    },
    "yakafinance-v3": {
        id: "6114"
    },
    "kelp": {
        id: "3946"
    },
    "arena-launch": {
        id: "6155"
    },
    "arena-dex": {
        id: "6154"
    },
    "avalon": {
        id: "4473"
    },
    "hyperlend": {
        id: "5940"
    },
    "hyperyield": {
        id: "5862"
    },
    "kinza-finance": {
        id: "3171"
    },
    "lava": {
        id: "4241"
    },
    "lendle": {
        id: "3300"
    },
    "more-markets": {
        id: "5320"
    },
    "pac-finance": {
        id: "4211"
    },
    "realt-rmm-marketplace-v2": {
        id: "4190"
    },
    "realt-rmm-marketplace": {
        id: "2359"
    },
    "sakefinance": {
        id: "5627"
    },
    "colend-protocol": {
        id: "4518"
    },
    "extra-finance-xlend": {
        id: "5554"
    },
    "seamless-v1": {
        id: "3502"
    },
    "superlend": {
        id: "5230"
    },
    "unleash-protocol": {
        id: "5793"
    },
    "vicuna-lending": {
        id: "5755"
    },
    "zerolend": {
        id: "3243"
    },
    "sosovalue": { // Indexes
        id: "5619"
    },
    "rockswap": {
        id: "4204"
    },
    "aave-v2": {
        "startFrom": 1647648000,
        id: "111"
    },
    "aave-v3": {
        "startFrom": 1647648000,
        id: "1599"
    },
    "balancer-v1": {
        id: "116",
        "displayName": "Balancer V1"
    },
    "balancer-v2": {
        id: "2611",
        "displayName": "Balancer V2"
    },
    "opensea-v1": {
        id: "2630",
        "displayName": "Opensea V1"
    },
    "opensea-v2": {
        id: "2631",
        "displayName": "Opensea V2"
    },
    "opensea-seaport": {
        id: "2258",
        "displayName": "Opensea Seaport"
    },
    "quickswap-v2": {
        id: "306",
        "displayName": "Quickswap V2"
    },
    "quickswap-v3": {
        id: "2239"
    },
    "sushiswap-classic": {
        id: "119"
    },
    "sushiswap-trident": {
        id: "2152"
    },
    "sushiswap-v3": {
        id: "2776"
    },
    "traderjoe-v1": {
        id: "468"
    },
    "traderjoe-v2": {
        id: "2393"
    },
    "premia-v2": {
        id: "381"
    },
    "premia-v3": {
        id: "3497"
    },
    "kyberswap-classic": {
        id: "127",
        "displayName": "KyberSwap - Classic"
    },
    "kyberswap-elastic": {
        id: "2615",
        "displayName": "KyberSwap - Elastic"
    },
    "predy-finance-v320": {
        id: "1657",
    },
    "predy-finance-v5": {
        id: "3324",
    },
    "zyberswap-v2": {
        id: "2467",
    },
    "zyberswap-v3": {
        id: "2602",
    },
    "zyberswap-stable": {
        id: "2530",
    },
    "hydradex-v2": {
        id: "1673",
        "displayName": "Hydradex V2"
    },
    "hydradex-v3": {
        id: "2910",
        "displayName": "Hydradex V3"
    },
    "smbswap-v2": {
        id: "1632"
    },
    "smbswap-v3": {
        id: "2895"
    },
    "ArbitrumExchange-v2": {
        id: "2685",
        "displayName": "Arbitrum Exchange V2"
    },
    "ArbitrumExchange-v3": {
        id: "2962",
        "displayName": "Arbitrum Exchange V3"
    },
    "y2k-v1": {
        id: "2375"
    },
    "y2k-v2": {
        id: "3056"
    },
    "baseswap-v2": {
        id: "3333"
    },
    "baseswap-v3": {
        id: "3507"
    },
    "dragonswap-v2": {
        id: "4138"
    },
    "dragonswap-v3": {
        id: "4139"
    },
    "fjord-foundry-v2": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1713657600": true,
                "1713744000": true
            }
        },
        id: "4505"
    },
    "fjord-foundry-v1": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1713657600": true,
                "1713744000": true
            }
        },
        id: "4557"
    },
    "ociswap-basic": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1715817600": true
            }
        },
        id: "3646"
    },
    "ociswap-precision": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1715817600": true
            }
        },
        id: "4629"
    },
    "uniswap-v1": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1603670400": true,
                "1661990400": true,
                "1665446400": true,
                "1670630400": true,
                "1722816000": true,
                "1725580800": true
            }
        },
        id: "2196"
    },
    "uniswap-v2": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1603670400": true,
                "1661990400": true,
                "1665446400": true,
                "1670630400": true,
                "1722816000": true,
                "1725580800": true
            }
        },
        id: "2197"
    },
    "uniswap-v3": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1603670400": true,
                "1661990400": true,
                "1665446400": true,
                "1670630400": true,
                "1722816000": true,
                "1725580800": true
            }
        },
        id: "2198"
    },
    "pancakeswap-v1": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1660176000": false,
                "1665014400": false,
                "1684713600": false
            }
        },
        id: "2590"
    },
    "pancakeswap-v2": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1660176000": false,
                "1665014400": false,
                "1684713600": false
            }
        },
        id: "194"
    },
    "pancakeswap-stableswap": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1660176000": false,
                "1665014400": false,
                "1684713600": false
            }
        },
        id: "2529"
    },
    "pancakeswap-v3": {
        "cleanRecordsConfig": {
            "genuineSpikes": {
                "1660176000": false,
                "1665014400": false,
                "1684713600": false
            }
        },
        id: "2769"
    },
    "swapmode-v2": {
        id: "4116"
    },
    "swapmode-v3": {
        id: "5362"
    },
    "bulbaswap-v2": {
        id: "5301"
    },
    "bulbaswap-v3": {
        id: "5302"
    },
    "superswap-v2": {
        id: "5372"
    },
    "superswap-v3": {
        id: "5373"
    },
    "jito-mev-tips": {
        id: "6169"
    },
    "meth-protocol": {
        id: "3882"
    },
    "believe": {
        id: "6159"
    },
    "holdstation-agg": {
        id: "6178"
    },
    "thena-integral": {
        id: "6179"
    },
    "fastlane": {
        id: "6182"
    },
    "bungee-bridge": {
        id: "6188"
    },
    "hadouken-amm": {
        id: "2748"
    },
    "ivx": {
        id: "6177"
    },
    "axial": {
        id: "845"
    },
    "mobius-money": {
        id: "588"
    },
    "nerve": {
        id: "301"
    },
    "minmax": {
        id: "826"
    },
    "flowx-v3": {
        id: "4582"
    },
    "flowx-finance": {
        id: "3196"
    },
    "roots": {
        id: "6166"
    },
    "takara-lend": {
        id: "5783"
    },
    "worldle": {
        id: "6192"
    },
    "rhea-lend": {
        id: "1546"
    },

    "arthswap-v3": {
        id: "4272",
    },
    "alienbase-v3": {
        id: "3361",
    },
    "blasterswap": {
        id: "4296",
    },
    "cleopatra-v2": {
        id: "4286",
    },
    "moraswap-v3": {
        id: "4269",
    },
    "infusion": {
        id: "4294",
    },
    "pharaoh-v2": {
        id: "4287"
    },
    "omax-swap": {
        id: "2464",
    },
    "kim-exchange-v2": {
        id: "4038",
    },
    "kim-exchange-v3": {
        id: "4299",
    },
    "merchant-moe-liquidity-book": {
        id: "4427",
    },
    "web3world": {
        id: "4430",
    },
    "glyph-exchange": {
        id: "4347",
    },
    "firefly": {
        id: "4500"
    },
    "velodrome-slipstream": {
        id: "4249",
    },
    "FeeFree": {
        id: "4530",
    },
    "physica-finance": {
        id: "4719",
    },
    "bitgenie-amm": {
        id: "4573",
    },
    "aerodrome-slipstream": {
        id: "4524"
    },
    "capybara-exchange": {
        id: "4747",
    },
    "vanillaswap-v2": {
        id: "4600",
    },
    "vanillaswap-v3": {
        id: "4601",
    },
    "maverick-v2": {
        id: "4752"
    },
    "thruster-v3": {
        id: "4199",
    },
    "thruster-v2": {
        id: "4207",
    },
    "voltage-v3": {
        id: "4188",
    },
    "dusa": {
        id: "4788",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1718841600": true,
                "1718755200": true,
            }
        }
    },
    "carbondefi": {
        id: "2890"
    },
    "glyph-exchange-v4": {
        id: "4880"
    },
    "dexswap": {
        id: "3277",
        cleanRecordsConfig: {
            genuineSpikes: {
                "1722211200": true
            }
        }
    },
    "blasterswap-v3": {
        id: "4728",
    },
    "splash": {
        id: "4712",
    },
    "xtrade": {
        id: "5040"
    },
    "magicsea-lb": {
        id: "4755",
    },
    "apexdefi": {
        id: "5065"
    },
    "dtx-v3": {
        id: "5141",
    },
    "scribe-exchange-v4": {
        id: "4943",
    },
    "mintswap": {
        id: "4811",
    },
    "nabla": {
        id: "5309",
    },
    "aerodrome": {
        id: "3450"
    },
    "shadow-exchange": {
        id: "5570",
    },
    "holdstation-swap": {
        id: "5493",
    },
    "zkswap-stable": {
        id: "5391",
    },
    "zkswap-v3": {
        id: "5392",
    },
    "nova-fi": {
        id: "5677",
    },
    "reservoir-tools-amm": {
        id: "5678",
    },
    "reservoir-tools-clmm": {
        id: "5679",
    },
    "kodiak-v3": {
        id: "5744",
    },
    "beralis-v3": {
        id: "5759",
    },
    "rooster": {
        id: "5704",
    },
    "sailfish": {
        id: "5643",
    },
    "artexswap": {
        id: "5665",
    },
    "mondrain": {
        id: "5765",
    },
    "shadow-legacy": {
        id: "5682"
    },
    "puppyfun": {
        id: "5895"
    },
    "kittenswap": {
        id: "5876"
    },
    "kittenswap-cl": {
        id: "6004"
    },
    "polygon-zkevm": {
        id: "1101"
    },
    "orki-finance": {
        id: "6181"
    },
    "interest-protocol-stable-swap": {
        id: "6034"
    },
    "metronome-synth": {
        id: "2428"
    },
    "vesper": {
        id: "220"
    },
    "gt3": {
        id: "6184"
    },
    "fastjpeg": {
        id: "6229"
    },
    "silent": {
        id: "6217"
    },
    "aftermath-fi-amm": {
        id: "3259"
    },
    "eden": {
        id: "6232"
    },
    "level": {
        id: "5090"
    },
    "rubicon": {
        id: "799"
    },
    "interest-movement-curve": {
        id: "6055"
    },
    "basisos": {
        id: "6167"
    },
    "berachain-bribes": {
        id: "6243"
    },
    "asymmetry-usdaf": {
        id: "6247"
    },
    "saros-dlmm": {
        id: "6250"
    },
    "saros": {
        id: "1262"
    },
    "9summits": {
        id: "6194"
    },
    "alphaping": {
        id: "6195"
    },
    "alterscope": {
        id: "6215"
    },
    "apostro": {
        id: "6196"
    },
    "b-protocol": {
        id: "6197"
    },
    "block-analitica": {
        id: "6198"
    },
    "clearstar": {
        id: "6199"
    },
    "euler-dao": {
        id: "6200"
    },
    "felix-vaults": {
        id: "6240"
    },
    "fence": {
        id: "6201"
    },
    "gauntlet": {
        id: "6202"
    },
    "hakutora": {
        id: "6203"
    },
    "hyperithm": {
        id: "6204"
    },
    "k3": {
        id: "6205"
    },
    "llamarisk": {
        id: "6206"
    },
    "m11c": {
        id: "6207"
    },
    "mev-capital": {
        id: "6208"
    },
    "ouroboros": {
        id: "6209"
    },
    "re7": {
        id: "6210"
    },
    "relend": {
        id: "6211"
    },
    "steakhouse": {
        id: "6212"
    },
    "tulip-capital": {
        id: "6213"
    },
    "yearn-curating": {
        id: "6251"
    },
    "mintpark": {
        id: "6254"
    },
    "bitcoin-bridge": {
        id: "6256"
    },
    "uniderp": {
        id: "6126"
    },
    "reachme": {
        id: "6260"
    },
    "haiku": {
        id: "6266"
    },
    "p2p-lending": {
        id: "6228"
    },
    "strike-finance": {
        id: "6173"
    },
    "haedal-protocol": {
        id: "3489"
    },
    "reya-dex": { 
        id: '4978' 
    },
    "bodega-market": { 
        id: '6012' 
    },
    "tbtc": {
        id: "2535"
    },
    "letsbonk": {
        id: "6282"
    },
    "bookusd": {
        id: "6283"
    },
    "indigo": {
        id: "2309"
    },
    "meteora-damm-v2": {
        id: "6288"
    },
    "moonpiefun": {
        id: "6289"
    },
    "meteora-dbc": {
        id: "6290"
    },
    "renzo": {
        id: "3933"
    },
    "jpg-store": {
        id: "5970"
    },
    "LiquidOps": {
        id: "5980"
    },
    "oxium": {
        id: "6301"
    },
    "gliquid": {
        id: "6294"
    },
    "rank-trading": {
        id: "6084"
    },
    "hypurrfi-isolated": {
        id: "6053"
    },
    "hydradx": {
        id: "6271"
    },
    "yieldnest": {
        id: "4606"
    },
    "rezerve-money": {
        id: "6300"
    },
    "nova": {
        id: "6326"
    },
    "pepeboost": {
        id: "6327"
    },
    "padre": {
        id: "6328"
    },
    "hypercat": {
        id: "6325"
    },
    "kgen": {
        id: "6333"
    },
    "launch-on-bags": {
        id: "6332"
    },
    "gluex-protocol": {
        id: "6334"
    },
    "bullaexchange": {
        id: "5766"
    },
    "aries-markets": {
        id: "2228"
    },
    "yala": {
        id: "6147"
    },
    "alkimiya": {
        id: "6344"
    },
    "smithii": {
        id: "6349"
    },
    "volta-markets": {
        id: "6345"
    },
    "bluefin-alphalend": {
        id: "6153"
    },
    "tapp-exchange": {
        id: "6352"
    },
    "eulerswap": {
        id: "6358"
    },
    "ssv-network": {
        id: "6359"
    },
    "astroport-v2": {
        id: "3117"
    },
    "moonshot-create": {
        id: "6380"
    },
    "byreal": {
        id: "6368"
    },
    "bitflux": {
        id: "5344"
    },
    "marinade-select": {
        id: "6275"
    },
    "unicornx": {
        id: "6385"
    },
    "copump": {
        id: "6070"
    },
    "fanx-protocol": {
        id: "4821"
    },
    "1776meme": {
        id: "6396"
    },
    "avalon-usda": {
        id: "5312"
    },
    "alpha-arcade": {
        id: "6302",
    },
    "moar": {
        id: "6013"
    },
    "kofi-finance": {
        id: "6176"
    },
    "jup-studio": {
        id: "6398"
    },
    "liquidswap": {
        id: "6374"
    },
    "yei-swap": {
        id: "6400"
    },
    "carbon": {
        id: "6409"
    },
    "kanalabs-perp": {
        id: "6175"
    },
    "hyperbloom": {
        id: "6329"
    },
    "fullsail-finance": {
        id: "6413"
    },
    "hyperwave": {
        id: "6407"
    },
    "meso-finance": {
        id: "4938"
    },
    "apebot": {
        id: "6420"
    },
    "ebisu-ebusd": {
        id: "6423"
    },
    "cobaltx": {
        id: "6425"
    },
    "40acres": {
        id: "6061"
    },
    "nerite": {
        id: "6434"
    },
    "pumper": {
        id: "6436",
    },
    "echo-lsd": {
        id: "5464",
    },
    "liquidlaunch": {
        id: "6441"
    },
    "multiswap": {
        id: "1908"
    },
    "prjx": {
        id: "6444"
    },
    "votre": {
        id: "6316"
    },
    "sushiswap-agg": {
        id: "5061"
    },
    "k-bit": {
        id: "6446"
    },
    "privex": {
        id: "6419"
    },
    "echo-strategy": {
        id: "6454"
    },
    "bluefin-pro": {
        id: "6451"
    },
    "blackhole": {
        id: "6430"
    },
    "blackhole-CL": {
        id: "6431"
    },
    "topcut": {
        id: "6448"
    },
    "thevault": {
        id: "4751"
    },
    "jupiter-staked-sol": {
        id: "4996"
    },
    "pangolin-v3": {
        id: "6466"
    },
    "fpex": {
        id: "6458"
    },
    "blazestake": {
        id: "2931"
    },
    "uniswap-v4": {
        id: "5690"
    },
    "echo-lending": {
        id: "5051"
    },
    "llamalend-curve": {
        id: "4321"
    },
    "graphite-protocol": {
        id: "6478"
    },
    "springsui-ecosystem": {
        id: "5424"
    },
    "springsui": {
        id: "5311"
    },
    "margin-zero": {
        id: "5612"
    },
    "hyperbrick": {
        id: "6481"
    },
    "binance-staked-sol": {
        id: "5135"
    },
    "drift-staked-sol": {
        id: "5046"
    },
    "angstrom": {
        id: "6483"
    },
    "apertureSwap": {
        id: "3554"
    },
    "definitive": {
        id: "4257"
    },
    "bybit-staked-sol": {
        id: "5179"
    },
    "pvp-trade": {
        id: "6510"
    },
    "okto-wallet": {
        id: "6513"
    },
    "spookyswap-v3": {
        id: "5272"
    },
    "echodex": {
        id: "3256"
    },
    "echodex-v3": {
        id: "3349"
    },
    "ampleswap": {
        id: "2383"
    },
    "tree-news": {
        id: "6515"
    },
    "archfi": {
        id: "6384"
    },
    "netswap": {
        id: "1140"
    },
    "stellaswap-v4": {
        id: "5802"
    },
    "based-app": {
        id: "6521"
    },
    "imx-seaport": {
        id: "6522"
    },
    "yo-protocol": {
        id: "5915"
    },
    "edge-capital": {
        id: "6528"
    },
    "dexari": {
        id: "6531"
    },
    "axiom-perps": {
        id: "6511"
    },
    "phantom-perps": {
        id: "6512"
    },
    "hyperunit": {
        id: "5788"
    },
    "omni-exchange-flux": {
        id: "6540"
    },
    "omni-exchange-v2": {
        id: "6539"
    },
    "omni-exchange-v3": {
        id: "6506"
    },
    "insilico": {
        id: "6543"
    },
    "liquid-perps": {
        id: "6544"
    },
    "lootbase": {
        id: "6545"
    },
    "mass-dot-money": {
        id: "6546"
    },
    "sendshot": {
        id: "6551"
    },
    "kura-v2": {
        id: "6549"
    },
    "kura-v3": {
        id: "6548"
    },
    "dextrabot": {
        id: "6558"
    },
    "hyperdash": {
        id: "6559"
    },
    "kinto-xyz": {
        id: "6560"
    },
    "superx": {
        id: "6561"
    },
    "wallet-v": {
        id: "6562"
    },
    "kinetiq-staked-hype": {
        id: "6447"
    },
    "resolv": {
        id: "5655"
    },
    "jito-staked-sol": {
        id: "2308"
    },
    "cl-dex": {
        id: "6533"
    },
    "swapr-v3": {
        id: "4057"
    },
    "swapr": {
        id: "292"
    },
    "etherex-legacy": {
        id: "6501"
    },
    "etherex": {
        id: "6502"
    },
    "earnium": {
        id: "6568"
    },
    "teleswap": {
        id: "5350"
    },
    "aquabot": {
        id: "6575"
    },
    "hybra-v2": {
        id: "6255"
    },
    "hybra-v3": {
        id: "6350"
    },
    "superstate-uscc": {
        id: "5120"
    },
    "alienfi": {
        id: "2603"
    },
    "jetswap": {
        id: "659"
    },
    "solarflare": {
        id: "1269"
    },
    "subzero-zswap": {
        id: "2556"
    },
    "swappi": {
        id: "1660"
    },
    "swapsicle": {
        id: "1824"
    },
    "quickswap-v4": {
        id: "6576"
    },
    "odyssey-finance": {
        id: "6529"
    },
    "bullbit-ai": {
        id: "6584"
    },
    "dsx": {
        id: "6585"
    },
    "cometh": {
        id: "261"
    },
    "crodex": {
        id: "828"
    },
    "fenix-finance-v3": {
        id: "4775"
    },
    "fenix-finance": {
        id: "4563"
    },
    "huckleberry": {
        id: "630"
    },
    "ocelex-v1": {
        id: "5378"
    },
    "oolongswap": {
        id: "794"
    },
    "pandora": {
        id: "1777"
    },
    "pandoraswap": {
        id: "1698"
    },
    "titano-swych": {
        id: "2102"
    },
    "zebra-v1": {
        id: "3668"
    },
    "zebra-v2": {
        id: "3901"
    },
    "spectra-v2": {
        id: "4725"
    },
    "noon": {
        id: "5721"
    },
    "bim": {
        id: "6402"
    },
    "fluid-dex-lite": {
        id: "6586"
    },
    "autoshark": {
        id: "1074"
    },
    "babyswap": {
        id: "597"
    },
    "blue-planet": {
        id: "2158"
    },
    "complus-network": {
        id: "471"
    },
    "dinosaur-eggs": {
        id: "695"
    },
    "empiredex": {
        id: "812"
    },
    "gin-finance": {
        id: "1795"
    },
    "gravis": {
        id: "2195"
    },
    "gravity-finance": {
        id: "351"
    },
    "horizondex": {
        id: "3255"
    },
    "zapfi": {
        id: "6588"
    },
    "superfund": {
        id: "6292"
    },
    "walrus": {
        id: "6032"
    },
    "heaven-dex": {
        id: "6592"
    },
    "hx-finance": {
        id: "6591"
    },
    "binance-alpha": {
        id: "6599"
    },
    "peapods-finance": {
        id: "4063"
    },
    "okie-launch": {
        id: "6612"
    },
    "okieswap-v2": {
        id: "6601"
    },
    "okieswap-v3": {
        id: "6602"
    },
    "livepeer": {
        id: "6613"
    },
    "saddle-finance": {
        id: "202"
    },
} as AdaptorsConfig
