// Jest <PERSON>nap<PERSON> v1, https://goo.gl/fbAQLP

exports[`Protocol adaptor list is complete FEES 1`] = `
Array [
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://apeswap.gitbook.io/apeswap-finance/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "ethereum",
      "polygon",
      "telos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "398",
    },
    "description": "ApeSwap is a Decentralized Autonomous Organization (DAO) that offers a full suite of tools to explore and engage with decentralized finance opportunities. Using the products within our DeFi Hub, users and partners can tap into this new wave of financial innovation in a secure, transparent, and globally accessible way.",
    "disabled": false,
    "displayName": "ApeSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "398",
    "logo": "https://icons.llama.fi/apeswap-amm.png",
    "methodology": Object {
      "Fees": "A 0.2% trading fee is collected",
      "HoldersRevenue": "Of all DEX trading fees earned by ApeSwap, 50% are used to buy back and burn BANANA on a quarterly basis",
      "ProtocolRevenue": "A 0.05% (bsc and ethereum) or 0.15% (polygon) or 0.0375% (telos) of the fees goes to treasury",
      "Revenue": "A 0.05% (bsc and ethereum) or 0.15% (polygon and telos) of the fees goes to treasury, 50% of that fee is used to buyback and burn BANANA, on Telos 25% of the collected fees goes to Telos",
      "SupplySideRevenue": "A 0.15% (bsc and ethereum) or 0.05% (polygon and telos) is distributed proportionally to all APE-LP token holders",
      "UserFees": "Users pays 0.2% of each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/apeswap.ts",
    "module": "apeswap",
    "name": "ApeSwap AMM",
    "parentProtocol": "ApeSwap",
    "protocolType": undefined,
    "symbol": "BANANA",
    "twitter": "ape_swap",
    "url": "https://apeswap.finance",
  },
  Object {
    "address": "telos:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Telos",
    "chains": Array [
      "telos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2317",
    },
    "description": "Archly is a permissionless, low slippage, low cost protocol that incentives fees on the Telos EVM.",
    "disabled": false,
    "displayName": "Archly Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": null,
    "id": "2317",
    "listedAt": 1669728918,
    "logo": "https://icons.llama.fi/archly-finance.png",
    "methodology": Object {
      "Fees": "The trading fees are 0.05%, and can be adjusted from 0.01% up to 0.1%.",
      "HoldersRevenue": "veArc voters receive all protocol fees.",
      "ProtocolRevenue": "Treasury does not earn any revenue from trading fees.",
      "Revenue": "All trading fees are paid to veArc voters.",
      "SupplySideRevenue": "LPs do not earn any revenue from trading fees, only Arc emission decided by veArc voters.",
      "UserFees": "Currently users pay a trading fee of 0.05%.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/archly-finance",
    "module": "archly-finance",
    "name": "Archly Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "ARC",
    "twitter": "ArchlyFinance",
    "url": "https://archly.fi",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/baby-doge-coin",
      "https://dessertswap.finance/audits/Baby%20Doge%20Coin%20(BabyDoge)%20BEP-20%20Audit%208332977.pdf",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10407",
    "config": Object {
      "enabled": true,
      "id": "2169",
    },
    "description": "Meme of BSC hold, pet, love, & help save dogs! BSC Farms & Swap",
    "disabled": false,
    "displayName": "BabyDogeSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "baby-doge-coin",
    "governanceID": Array [
      "snapshot:babydogevote.eth",
    ],
    "id": "2169",
    "listedAt": 1665220470,
    "logo": "https://icons.llama.fi/babydogeswap.jpg",
    "methodology": Object {
      "Fees": "Fees collected from user trading fees",
      "HoldersRevenue": "A 0.05% of user fees is used to buy back and burn BabyDoge tokens",
      "ProtocolRevenue": "A 0.05% of user fees goes to treasure",
      "Revenue": "A 0.1% of user fees are distributed 50/50 between treasury and BabyDoge buy back and burn",
      "SupplySideRevenue": "A 0.2% user fees is distributed among LPs",
      "UserFees": "Users pays 0.3% of each swap. Different user fee discounts depening on Baby Doge wallet balance (up to 70% off). Calculation made with base 0.3%",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/babydogeswap.ts",
    "module": "babydogeswap",
    "name": "BabyDogeSwap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "BabyDoge",
    "twitter": "BabyDogeCoin",
    "url": "https://babydogeswap.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.balancer.fi/core-concepts/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "116",
      "protocolsData": Object {
        "v1": Object {
          "displayName": "Balancer V1",
          "enabled": true,
          "id": "116",
        },
        "v2": Object {
          "displayName": "Balancer V2",
          "enabled": true,
          "id": "2611",
        },
      },
    },
    "description": "Balancer is a protocol for programmable liquidity.


",
    "disabled": false,
    "displayName": "Balancer V1",
    "enabled": true,
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:balancer.eth",
    ],
    "id": "116",
    "logo": "https://icons.llama.fi/balancer-v1.png",
    "methodology": Object {
      "Fees": "All trading fees collected",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Balancer V1 protocol fees are set to 0%",
      "Revenue": "Balancer V1 protocol fees are set to 0%",
      "SupplySideRevenue": "Trading fees are distributed among LPs",
      "UserFees": "Trading fees paid by users, ranging from 0.0001% and 10%",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/balancer.ts",
    "module": "balancer",
    "name": "Balancer V1",
    "parentProtocol": "Balancer",
    "protocolType": undefined,
    "symbol": "BAL",
    "treasury": "balancer.js",
    "twitter": "BalancerLabs",
    "url": "https://balancer.finance/",
    "versionKey": "v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.balancer.fi/core-concepts/security/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "polygon",
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "116",
      "protocolsData": Object {
        "v1": Object {
          "displayName": "Balancer V1",
          "enabled": true,
          "id": "116",
        },
        "v2": Object {
          "displayName": "Balancer V2",
          "enabled": true,
          "id": "2611",
        },
      },
    },
    "description": "Balancer is a protocol for programmable liquidity.",
    "disabled": false,
    "displayName": "Balancer V2",
    "enabled": true,
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:balancer.eth",
    ],
    "id": "116",
    "logo": "https://icons.llama.fi/balancer-v2.png",
    "methodology": Object {
      "Fees": "All trading fees collected (doesn't include withdrawal and flash loan fees)",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Set to 10% of collected fees by a governance vote",
      "Revenue": "Protocol revenue from all fees collected",
      "SupplySideRevenue": "A small percentage of the trade paid by traders to pool LPs, set by the pool creator or dynamically optimized by Gauntlet",
      "UserFees": "Trading fees paid by users, ranging from 0.0001% to 10%",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/balancer.ts",
    "module": "balancer",
    "name": "Balancer V2",
    "parentProtocol": "Balancer",
    "protocolType": undefined,
    "symbol": "BAL",
    "twitter": "BalancerLabs",
    "url": "https://balancer.finance/",
    "versionKey": "v2",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/biswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "10746",
    "config": Object {
      "enabled": true,
      "id": "373",
    },
    "description": "Biswap is a trusted DEX platform on the BNB Chain network with a Multi-type Referral Program and low trade fee starting from 0.1%. Biswap is the ecosystem that offers the best service and creates new standards in the DeFi industry",
    "disabled": false,
    "displayName": "BiSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "biswap",
    "governanceID": Array [
      "snapshot:biswap-org.eth",
    ],
    "id": "373",
    "logo": "https://icons.llama.fi/biswap.jpg",
    "methodology": Object {
      "Fees": "Fees collected from user trading fees",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "A 0.05% of each swap goes to treasure",
      "Revenue": "Revenue is 0.05% of each swap which goes to treasury",
      "SupplySideRevenue": "A 0.15% fee of each swap is distributed among LPs",
      "UserFees": "Users pays 0.2% of each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/biswap.ts",
    "module": "biswap",
    "name": "BiSwap",
    "protocolType": undefined,
    "symbol": "BSW",
    "twitter": "Biswap_Dex",
    "url": "https://biswap.org/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://curve.fi/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "optimism",
      "arbitrum",
      "polygon",
      "avax",
      "fantom",
      "xdai",
    ],
    "cmcId": "6538",
    "config": Object {
      "enabled": true,
      "id": "3",
    },
    "description": "Curve is a decentralized exchange liquidity pool on Ethereum designed for extremely efficient stablecoin trading",
    "disabled": false,
    "displayName": "Curve",
    "enabled": true,
    "gecko_id": "curve-dao-token",
    "governanceID": Array [
      "snapshot:curve.eth",
    ],
    "id": "3",
    "language": "Vyper",
    "logo": "https://icons.llama.fi/curve.png",
    "methodology": Object {
      "Fees": "Trading fees paid by users",
      "HoldersRevenue": "A 50% of the trading fee is collected by the users who have vote locked their CRV",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "A 50% of the trading fee is collected by veCRV holders",
      "SupplySideRevenue": "A 50% of all trading fees are distributed among liquidity providers",
      "UserFees": "Users pay a trading fee from 0.04% to 0.4% on each swap (as of July 2022, the fee on all pools was 0.04%)",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/curve.ts",
    "module": "curve",
    "name": "Curve",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "CRV",
    "twitter": "CurveFinance",
    "url": "https://curve.fi",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://crypto.com/defi/swap-protocol-audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1776",
    "config": Object {
      "enabled": true,
      "id": "221",
    },
    "description": "Crypto.com DeFi Swap is a fork of Uniswap V2 designed to be the best place to swap and farm DeFi coins.",
    "disabled": false,
    "displayName": "Defi Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "crypto-com-chain",
    "id": "221",
    "logo": "https://icons.llama.fi/defi-swap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/defi-swap.ts",
    "module": "defi-swap",
    "name": "Defi Swap",
    "protocolType": undefined,
    "symbol": "CRO",
    "twitter": "cryptocom",
    "url": "https://crypto.com/defi/swap",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/DODOEX/docs/blob/master/docs/audit.md",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
      "arbitrum",
      "aurora",
      "boba",
    ],
    "cmcId": "7224",
    "config": Object {
      "enabled": true,
      "id": "146",
    },
    "description": "Trade crypto assets with market-leading liquidity",
    "disabled": false,
    "displayName": "DODO",
    "enabled": true,
    "gecko_id": "dodo",
    "governanceID": Array [
      "snapshot:dodobird.eth",
    ],
    "id": "146",
    "logo": "https://icons.llama.fi/dodo.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/dodo.ts",
    "module": "dodo",
    "name": "DODO",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "DODO",
    "treasury": "dodo.js",
    "twitter": "BreederDodo",
    "url": "https://dodoex.io/",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.ede.finance/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "22810",
    "config": Object {
      "enabled": true,
      "id": "2356",
    },
    "description": "El Dorado Exchange(EDE) is a decentralized spot and perpetual social trading exchange which prioritizes user security and stable investor returns. In EDE, all the interactions will happen on-chain. Trading is supported by 3 unique multi-asset pools",
    "disabled": false,
    "displayName": "El Dorado Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "el-dorado-exchange",
    "governanceID": Array [
      "snapshot:edefinance.eth",
    ],
    "id": "2356",
    "listedAt": 1670855637,
    "logo": "https://icons.llama.fi/el-dorado-exchange.jpg",
    "methodology": Object {
      "Fees": "All mint, burn, margin and liquidation and swap fees are collected",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Revenue is calculated as 30% of the total fee.",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Users pay swap fees and margin and liquidation fees",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/el-dorado-exchange.ts",
    "module": "el-dorado-exchange",
    "name": "El Dorado Exchange",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "EDE",
    "twitter": "ede_finance",
    "url": "https://ede.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://blog.hashex.org/elk-finance-smart-contracts-audit-report-a18deaa5890b",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "arbitrum",
      "avax",
      "bsc",
      "fantom",
      "polygon",
      "xdai",
      "elastos",
      "okexchain",
      "kcc",
      "ethereum",
      "optimism",
      "fuse",
      "iotex",
      "telos",
    ],
    "cmcId": "10095",
    "config": Object {
      "enabled": true,
      "id": "420",
    },
    "description": "Elk Finance is building Web3 infrastructure for cross-chain value and data transfer via ElkNet - our decentralized bridge. Our motto is Any chain, anytime, anywhere.",
    "disabled": false,
    "displayName": "Elk",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "elk-finance",
    "id": "420",
    "logo": "https://icons.llama.fi/elk.jpg",
    "methodology": Object {
      "Fees": "Is collected 0.3% of each swap",
      "HoldersRevenue": "Holders have no revenue from swap fees",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "Treasury have no revenue",
      "SupplySideRevenue": "LP earn a 0.3% of each swap",
      "UserFees": "Users pay a trading fee of 0.3%",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/elk.ts",
    "module": "elk",
    "name": "Elk",
    "protocolType": undefined,
    "symbol": "ELK",
    "twitter": "elk_finance",
    "url": "https://elk.finance",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/emdx-dex/perpetual-protocol/blob/emdx/main/audit/2021-12%20EMDX%20Protocol%20Audit.final.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2299",
    },
    "description": "EMDX is a decentralized derivative protocol with an in-house spot market, expanding risk hedging tools for traditional assets into the DeFi environment and allowing token listing “on demand” with “liquidity as a service” from the start.",
    "disabled": false,
    "displayName": "EMDX",
    "enabled": true,
    "gecko_id": null,
    "id": "2299",
    "listedAt": 1669035629,
    "logo": "https://icons.llama.fi/emdx.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/emdx.ts",
    "module": "emdx",
    "name": "EMDX",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "emdx_io",
    "url": "https://emdx.io/",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": "Equalizer is forked from Velodrome, which had it's improvements over Solidly audited multiple times.",
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2332",
    },
    "description": "Equalizer's twin-AMM design unites StableSwap pools with Standard 'kxy' liquidity pools. All the trading fees go to Vote-Escrowers of emission token $EQUAL which has to be Locked to earn triple 'Bribes' from candidate pools via Trade Fee, Internal Bribes & External Bribes.",
    "disabled": false,
    "displayName": "Equalizer Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "equalizer-dex",
    "id": "2332",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/equalizer-exchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/equalizer-exchange.ts",
    "module": "equalizer-exchange",
    "name": "Equalizer Exchange",
    "oracles": Array [
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "EQUAL",
    "twitter": "Equalizer0x",
    "url": "https://equalizer.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "aurora",
      "avax",
      "boba",
      "bsc",
      "ethereum",
      "fantom",
      "harmony",
      "moonbeam",
      "moonriver",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2121",
    },
    "description": "Fraxswap is the first constant product automated market maker with an embedded time-weighted average market maker (TWAMM) for conducting large trades over long periods of time trustlessly. It is fully permissionless and the core AMM is based on Uniswap V2.",
    "disabled": false,
    "displayName": "Frax Swap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2121",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/frax.jpg",
    "methodology": Object {
      "Fees": "A 0.3% fee is collected from each swap",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Users pay 0.3% swap fees",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/frax-swap.ts",
    "module": "frax-swap",
    "name": "Frax Swap",
    "oracles": Array [],
    "parentProtocol": "Frax Finance",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "fraxfinance",
    "url": "https://app.frax.finance/swap/main",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/xvi10/gambit-contracts/tree/master/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "arbitrum",
      "avax",
    ],
    "cmcId": "11857",
    "config": Object {
      "enabled": true,
      "gmx": Object {
        "enabled": true,
        "protocolsData": Object {
          "derivatives": Object {
            "displayName": "GMX - Derivatives",
            "enabled": true,
            "id": "337",
          },
          "swap": Object {
            "category": "Dexs",
            "displayName": "GMX - SWAP",
            "enabled": true,
            "id": "337",
          },
        },
      },
      "id": "337",
    },
    "description": "GMX is a decentralized spot and perpetual exchange that supports low swap fees and zero price impact trades. Trading is supported by a unique multi-asset pool that earns liquidity providers fees from market making, swap fees, leverage trading (spreads, funding fees & liquidations) and asset rebalancing.",
    "disabled": false,
    "displayName": "GMX",
    "enabled": true,
    "gecko_id": "gmx",
    "gmx": Object {
      "enabled": true,
      "protocolsData": Object {
        "derivatives": Object {
          "displayName": "GMX - Derivatives",
          "enabled": true,
          "id": "337",
        },
        "swap": Object {
          "category": "Dexs",
          "displayName": "GMX - SWAP",
          "enabled": true,
          "id": "337",
        },
      },
    },
    "governanceID": Array [
      "snapshot:gmx.eth",
    ],
    "id": "337",
    "logo": "https://icons.llama.fi/gmx.png",
    "methodology": Object {
      "Fees": "Fees from open/close position (0.1%), swap (0.2% to 0.8%), mint and burn (based on tokens balance in the pool) and borrow fee ((assets borrowed)/(total assets in pool)*0.01%)",
      "HoldersRevenue": "30% of all collected fees goes to GMX stakers",
      "ProtocolRevenue": "Treasury has no revenue",
      "Revenue": "Revenue is 30% of all collected fees, which goes to GMX stakers",
      "SupplySideRevenue": "70% of all collected fees goes to GLP holders",
      "UserFees": "Fees from open/close position (0.1%), swap (0.2% to 0.8%) and borrow fee ((assets borrowed)/(total assets in pool)*0.01%)",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/gmx.ts",
    "module": "gmx",
    "name": "GMX",
    "protocolType": undefined,
    "referralUrl": "https://gmx.io/#/?ref=defillama",
    "symbol": "GMX",
    "twitter": "GMX_IO",
    "url": "https://gmx.io/",
  },
  Object {
    "address": "xdai:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "xDai",
    "chains": Array [
      "polygon",
      "xdai",
    ],
    "cmcId": "7972",
    "config": Object {
      "enabled": true,
      "id": "271",
    },
    "description": "Honeyswap is a decentralized exchange built on the xDai Chain, this enables users to experience fast and secure transactions with incredibly low fees. Multiple tokens are available with which you can swap and add liquidity. It is a fork of the well known Uniswap-V2 protocol adapted to fit for the xDai Chain and the 1hive ecosystem.",
    "disabled": false,
    "displayName": "Honeyswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "honey",
    "id": "271",
    "logo": "https://icons.llama.fi/honeyswap.png",
    "methodology": Object {
      "Fees": "Trading fees are 0.3% of each swap",
      "HoldersRevenue": "There's no revenue from trading fees for token holders",
      "ProtocolRevenue": "A 0.05% goes to the protocol treasury",
      "Revenue": "A 0.05% trading fee goes to treasury",
      "SupplySideRevenue": "A 0.25% of each swap is distributed to liquidity providers",
      "UserFees": "A 0.3% fee is charged for token swaps",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/honeyswap.ts",
    "module": "honeyswap",
    "name": "Honeyswap",
    "protocolType": undefined,
    "symbol": "HNY",
    "twitter": "Honeyswap",
    "url": "https://honeyswap.org/",
  },
  Object {
    "address": "juno:-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Juno",
    "chains": Array [
      "juno",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2052",
    },
    "description": "JunoSwap is an interchain decentralized exchange focussed on CW-20 (CosmWasm) asset adoption. Built on the public permission-less Juno Network",
    "disabled": false,
    "displayName": "Junoswap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "junoswap-raw-dao",
    "id": "2052",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/junoswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/junoswap.ts",
    "module": "junoswap",
    "name": "Junoswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "RAW",
    "twitter": "JunoNetwork",
    "url": "https://www.junoswap.com/",
  },
  Object {
    "address": "kava:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Kava",
    "chains": Array [
      "kava",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2326",
    },
    "description": "KPerp.Exchange is a decentralized exchange created to offer a broad selection of trading options and extremely high levels of liquidity on numerous cryptocurrencies.",
    "disabled": false,
    "displayName": "KPerp Exchange",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": null,
    "id": "2326",
    "listedAt": 1669910621,
    "logo": "https://icons.llama.fi/kperp-exchange.png",
    "methodology": "All mint, burn, marginAndLiquidation and swap fees are collected and the daily fee amount is determined. Daily revenue is calculated as 30% of the total fee.",
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/kperp-exchange.ts",
    "module": "kperp-exchange",
    "name": "KPerp Exchange",
    "oracles": Array [
      "TWAP",
      "Witnet",
    ],
    "protocolType": undefined,
    "symbol": "KPE",
    "twitter": "KPerpExchange",
    "url": "https://kperp.exchange/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://chainsecurity.com/security-audit/kyber-network-dynamic-market-maker-dmm/",
    ],
    "audit_note": null,
    "audits": null,
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "aurora",
      "optimism",
      "arbitrum",
      "fantom",
      "avax",
      "bsc",
      "polygon",
      "ethereum",
      "cronos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "127",
      "protocolsData": Object {
        "classic": Object {
          "displayName": "KyberSwap - Classic",
          "enabled": true,
          "id": "127",
        },
        "elastic": Object {
          "displayName": "KyberSwap - Elastic",
          "enabled": true,
          "id": "2615",
        },
      },
    },
    "description": "KyberSwap is both a decentralized exchange (DEX) aggregator and a liquidity source with capital-efficient liquidity pools that earns fees for liquidity providers",
    "disabled": false,
    "displayName": "KyberSwap - Classic",
    "enabled": true,
    "gecko_id": null,
    "id": "127",
    "logo": "https://icons.llama.fi/kyberswap-classic.png",
    "methodology": Object {
      "Fees": "Kyberswap Classic collects a dynamic fee that increases with market volatility and decreases with stable market conditions",
      "HoldersRevenue": "Holders who stake and participate in the KyberDAO get their share of the fees designated for rewards, currently set at 10% of trading fees",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "Currently 100% of the dao rewards (10% of the collected fees) goes to all voters (KNC stakers)",
      "SupplySideRevenue": "Liquidity providers earn 90% fees of trading routed through their pool and selected price range",
      "UserFees": "Users pay a dynamic fee based on market conditions",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/kyberswap.ts",
    "module": "kyberswap",
    "name": "KyberSwap Classic",
    "oracles": Array [
      "Chainlink",
      "Band",
    ],
    "parentProtocol": "KyberSwap",
    "protocolType": undefined,
    "symbol": "KNC",
    "twitter": "KyberNetwork",
    "url": "https://kyberswap.com/#/swap",
    "versionKey": "classic",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://chainsecurity.com/security-audit/kyber-network-dynamic-market-maker-dmm/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "optimism",
      "arbitrum",
      "fantom",
      "avax",
      "bsc",
      "polygon",
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "127",
      "protocolsData": Object {
        "classic": Object {
          "displayName": "KyberSwap - Classic",
          "enabled": true,
          "id": "127",
        },
        "elastic": Object {
          "displayName": "KyberSwap - Elastic",
          "enabled": true,
          "id": "2615",
        },
      },
    },
    "description": "KyberSwap is both a decentralized exchange (DEX) aggregator and a liquidity source with capital-efficient liquidity pools that earns fees for liquidity providers",
    "disabled": false,
    "displayName": "KyberSwap - Elastic",
    "enabled": true,
    "gecko_id": null,
    "id": "127",
    "logo": "https://icons.llama.fi/kyberswap-elastic.png",
    "methodology": Object {
      "Fees": "Each pool can have different fees set from the following tires: 0.008%, 0.01%, 0.04%, 0.3% and 1%",
      "HoldersRevenue": "Holders who stake and participate in the KyberDAO get their share of the fees designated for rewards, currently set at 10% of trading fees",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "Currently 100% of the dao rewards (10% of the collected fees) goes to all voters (KNC stakers)",
      "SupplySideRevenue": "Liquidity providers earn 90% fees of trading routed through their pool and selected price range",
      "UserFees": "Users pay trading fees based pool fee setting: 0.008%, 0.01%, 0.04%, 0.3% and 1%",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/kyberswap.ts",
    "module": "kyberswap",
    "name": "KyberSwap Elastic",
    "oracles": Array [
      "Chainlink",
      "Band",
    ],
    "parentProtocol": "KyberSwap",
    "protocolType": undefined,
    "symbol": "KNC",
    "twitter": "KyberNetwork",
    "url": "https://kyberswap.com/#/swap",
    "versionKey": "elastic",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://obeliskauditing.com/audits/level-finance",
      "https://obeliskauditing.com/audits/level-finance-core",
      "https://obeliskauditing.com/audits/level-finance-trading",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2395",
    },
    "description": "Level Finance - Decentralized Perpetual Exchange.",
    "disabled": false,
    "displayName": "Level Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "governanceID": Array [
      "snapshot:level-finance.eth",
    ],
    "id": "2395",
    "listedAt": 1672230595,
    "logo": "https://icons.llama.fi/level-finance.png",
    "methodology": Object {
      "Fees": "All mint, burn, margin, liquidation and swap fees are collect",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "50% of the total fee",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "All mint, burn, margin, liquidation and swap fees are collect",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/level-finance.ts",
    "module": "level-finance",
    "name": "Level Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "LVL",
    "twitter": "Level__Finance",
    "url": "https://app.level.finance",
  },
  Object {
    "address": "solana:LFNTYraetVioAPnGJht4yNg2aUZFXR776cMeN9VMjXp",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "19842",
    "config": Object {
      "enabled": true,
      "id": "2154",
    },
    "description": "The first proactive market maker on Solana designed to improve capital efficiency and reduce impermanent loss.",
    "disabled": false,
    "displayName": "Lifinity",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "lifinity",
    "id": "2154",
    "listedAt": 1664977775,
    "logo": "https://icons.llama.fi/lifinity.jpg",
    "methodology": Object {
      "Fees": "All fees generated from trading fees",
      "HoldersRevenue": "Holders have no revenue from trading fees",
      "ProtocolRevenue": "A 15% of trading fees is retained as a protocol fee",
      "Revenue": "A 15% of trading fees is retained as a protocol fee",
      "SupplySideRevenue": "LPs currently receive 85% of trading fees",
      "UserFees": "Base trading fee differs on each pool",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/lifinity.ts",
    "module": "lifinity",
    "name": "Lifinity",
    "oracles": Array [
      "Pyth",
    ],
    "protocolType": undefined,
    "symbol": "LFNTY",
    "twitter": "Lifinity_io",
    "url": "https://lifinity.io/pools",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/maverickprotocol/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2644",
    },
    "description": "The DeFi infrastructure built to bring higher capital efficiency + greater capital control to the liquidity market, powered by Maverick AMM.",
    "disabled": false,
    "displayName": "Maverick Protocol",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2644",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/maverick-protocol.jpg",
    "methodology": Object {
      "Fees": "Fees generated on each swap at a rate set by the pool.",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "TotalFees": "Cumulative all-time Fees",
      "TotalUserFees": "Cumulative all-time Fees",
      "UserFees": "LPs collect 100% of the fee generated in a pool",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/maverick.ts",
    "module": "maverick",
    "name": "Maverick Protocol",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mavprotocol",
    "url": "https://www.mav.xyz",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://github.com/metavaultorg/trade-contracts/blob/main/Metavault.Trade_Full_Smart_Contract_Security_Audit.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "19973",
    "config": Object {
      "enabled": true,
      "id": "1801",
    },
    "description": "Metavault.Trade is a new kind of Decentralised Exchange, designed to provide a large range of trading features and very deep liquidity on many large cap crypto assets. Traders can use it in two ways: Spot trading, with swaps and limit orders. Perpetual futures trading with up to 50x leverage on short and long positions. Metavault.Trade aims to become the go-to solution for traders who want to stay in control of their funds at all times without sharing their personal data.",
    "disabled": false,
    "displayName": "Metavault.Trade",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "metavault-trade",
    "id": "1801",
    "listedAt": 1654203519,
    "logo": "https://icons.llama.fi/metavault.trade.png",
    "methodology": "All mint, burn, marginAndLiquidation and swap fees are collected and the daily fee amount is determined. Daily revenue is calculated as 30% of the total fee.",
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/metavault.trade",
    "module": "metavault.trade",
    "name": "Metavault.Trade",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Metavault",
    "protocolType": undefined,
    "symbol": "MVX",
    "twitter": "MetavaultTRADE",
    "url": "https://metavault.trade",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "IoTeX",
    "chains": Array [
      "iotex",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1241",
    },
    "description": "mimo is a decentralized liquidity protocol that will fuel the next wave of decentralized finance (DeFi) on IoTeX. mimo’s vision is to empower next-gen DeFi products that utilize our state-of-the-art automated liquidity protocol and the IoTeX's lightning-fast speed, low gas fees, and cross-chain capabilities.",
    "disabled": false,
    "displayName": "Mimo",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "1241",
    "listedAt": 1641944808,
    "logo": "https://icons.llama.fi/mimo.jpg",
    "methodology": Object {
      "Fees": "All fees are collected from trading fees",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Protocol have no revenue.",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mimo.ts",
    "module": "mimo",
    "name": "Mimo",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mimoprotocol",
    "url": "https://mimo.exchange",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2015",
    },
    "description": "MM Finance swap on Polygon network",
    "disabled": false,
    "displayName": "MM Stableswap Polygon",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2015",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mm-stableswap-polygon.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mm-stableswap-polygon.ts",
    "module": "mm-stableswap-polygon",
    "name": "MM Stableswap Polygon",
    "oracles": Array [],
    "parentProtocol": "MM Finance",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "MMFcrypto",
    "url": "https://polymm.finance",
  },
  Object {
    "address": "kcc:******************************************",
    "audit_links": Array [
      "https://github.com/MojitoFinance/mojito-swap-farm/blob/main/doc/PeckShield-Audit-Report-Mojito-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Kucoin",
    "chains": Array [
      "kcc",
    ],
    "cmcId": "1521",
    "config": Object {
      "enabled": true,
      "id": "1181",
    },
    "description": "Mojito Finance is a decentralized exchange running on KCC, with lots of features that let you earn and win tokens. It is fast, cheap and easily accessible as it is not difficult to use. It has currently opened Trade ,Bar (Farm) and Wine Pools. Other functions are on the way. And it will continue to launch interesting functions such as Cocktail Tickets and NFT in the future.",
    "disabled": false,
    "displayName": "MojitoSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "mojitoswap",
    "id": "1181",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mojitoswap.png",
    "methodology": Object {
      "Fees": "The transaction fee on MojitoSwap is 0.3%",
      "HoldersRevenue": "A 0.08% fee of each swap is used to buyback and burn",
      "ProtocolRevenue": "A 0.04% of swap fees goes to MJT treasury",
      "Revenue": "Revenue is 0.12% of each swap",
      "SupplySideRevenue": "Liquidity providers earn a 0.18% of each swap",
      "UserFees": "Trading fees are 0.3% of each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mojitoswap.ts",
    "module": "mojitoswap",
    "name": "MojitoSwap",
    "protocolType": undefined,
    "symbol": "MJT",
    "twitter": "MojitoSwap",
    "url": "https://www.mojitoswap.finance",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://dapp.org.uk/reports/mooniswap.html",
      "https://mooniswap.exchange/docs/mooniswap-audit-report-2.pdf",
      "https://mooniswap.exchange/docs/mooniswap-audit-report-3.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1102",
    "config": Object {
      "enabled": true,
      "id": "1053",
    },
    "description": "Next gen. AMM protocol by 1inch! Redistributes earnings to liquidity providers, capitalizes on user slippages and protects traders from front-running attacks.",
    "disabled": false,
    "displayName": "MooniSwap",
    "enabled": true,
    "gecko_id": null,
    "id": "1053",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/mooniswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mooniswap.ts",
    "module": "mooniswap",
    "name": "MooniSwap",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "mooniswap",
    "url": "https://mooniswap.exchange/#/swap",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "optimism",
    ],
    "cmcId": "23038",
    "config": Object {
      "enabled": true,
      "id": "2361",
    },
    "description": "Bring unparalleled Swap and Perpetual trading experience to FantomFDN users",
    "disabled": false,
    "displayName": "Mummy Finance",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": "mummy-finance",
    "governanceID": Array [
      "snapshot:mmyvote.eth",
    ],
    "id": "2361",
    "listedAt": 1671034969,
    "logo": "https://icons.llama.fi/mummy-finance.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mummy-finance.ts",
    "module": "mummy-finance",
    "name": "Mummy Finance",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MMY",
    "twitter": "mummyftm",
    "url": "https://www.mummy.finance",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/nomiswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "834",
    "config": Object {
      "enabled": true,
      "id": "1823",
    },
    "description": "Nomiswap is a decentralized exchange platform with a binary referral system and the lowest platform transaction fees.",
    "disabled": false,
    "displayName": "Nomiswap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "nominex",
    "id": "1823",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/nomiswap.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/nomiswap.ts",
    "module": "nomiswap",
    "name": "Nomiswap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "NMX",
    "twitter": "Nomiswap",
    "url": "https://nomiswap.io",
  },
  Object {
    "address": "-",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Osmosis",
    "chains": Array [
      "cosmos",
    ],
    "cmcId": "12220",
    "config": Object {
      "enabled": true,
      "id": "383",
    },
    "description": "Osmosis DEX is the advanced automated market maker (AMM) protocol at the core of the Osmosis blockchain.",
    "disabled": false,
    "displayName": "Osmosis DEX",
    "enabled": true,
    "gecko_id": "osmosis",
    "id": "383",
    "logo": "https://icons.llama.fi/osmosis-dex.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/osmosis.ts",
    "module": "osmosis",
    "name": "Osmosis DEX",
    "protocolType": undefined,
    "symbol": "OSMO",
    "twitter": "osmosiszone",
    "url": "https://osmosis.zone/",
  },
  Object {
    "address": "avax:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": "8422",
    "config": Object {
      "enabled": true,
      "id": "246",
    },
    "description": "Pangolin is a community-driven DEX that runs on multiple blockchains. ",
    "disabled": false,
    "displayName": "Pangolin",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "pangolin",
    "governanceID": Array [
      "snapshot:pangolindex.eth",
    ],
    "id": "246",
    "logo": "https://icons.llama.fi/pangolin.png",
    "methodology": Object {
      "Fees": "A 0.3% of each swap is collected as trading fees",
      "HoldersRevenue": "A 0.0425% trading fees goes to PNG staking Pool",
      "ProtocolRevenue": "A 0.0075% fees goes to Pangolin DAO’s treasury",
      "Revenue": "Governance revenue is 0.05% trading fees",
      "SupplySideRevenue": "A 0.25% from each swap is distributed to liquidity providers",
      "UserFees": "User pays 0.3% fees on each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/pangolin.ts",
    "module": "pangolin",
    "name": "Pangolin",
    "protocolType": undefined,
    "symbol": "PNG",
    "twitter": "pangolindex",
    "url": "https://pangolin.exchange",
  },
  Object {
    "address": "tezos:KT1JVjgXPMMSaa6FkzeJcgb8q9cUaLmwaJUX",
    "audit_links": Array [
      "https://github.com/Plenty-network/security-audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Tezos",
    "chains": Array [
      "tezos",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "490",
    },
    "description": "Plenty is a decentralized exchange on Tezos that offers a seamless trading experience. Users can trade, earn, govern and build on the platform. Plenty features both stable and volatile liquidity pools, enables near-zero slippage trades and easy bridging from Ethereum and Polygon.",
    "disabled": false,
    "displayName": "Plenty",
    "enabled": true,
    "gecko_id": null,
    "id": "490",
    "logo": "https://icons.llama.fi/plenty.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/plenty.ts",
    "module": "plenty",
    "name": "Plenty",
    "protocolType": undefined,
    "symbol": "PLY",
    "twitter": "plenty_network",
    "url": "https://www.plenty.network",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://contractsecurity.io/quick-audit-report/",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": "19966",
    "config": Object {
      "enabled": true,
      "id": "306",
      "protocolsData": Object {
        "v2": Object {
          "displayName": "Quickswap V2",
          "enabled": true,
          "id": "306",
        },
        "v3": Object {
          "enabled": true,
          "id": "2239",
        },
      },
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "Quickswap V2",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "quickswap",
    "id": "306",
    "logo": "https://icons.llama.fi/quickswap-dex.jpg",
    "methodology": Object {
      "Fees": "A 0.3% of each swap is collected as trading fees",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Protocol have no revenue",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/quickswap.ts",
    "module": "quickswap",
    "name": "Quickswap Dex",
    "parentProtocol": "Quickswap",
    "protocolType": undefined,
    "symbol": "QUICK",
    "twitter": "QuickswapDEX",
    "url": "https://quickswap.exchange/",
    "versionKey": "v2",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://contractsecurity.io/quick-audit-report/",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "306",
      "protocolsData": Object {
        "v2": Object {
          "displayName": "Quickswap V2",
          "enabled": true,
          "id": "306",
        },
        "v3": Object {
          "enabled": true,
          "id": "2239",
        },
      },
    },
    "description": "QuickSwap is a decentralized exchange that runs on Polygon Network to offer cheaper and faster transactions. Its automated market maker integrates upgradeable smart contracts on Ethereum and renders intermediaries obsolete. The exchange is based on open-source software and prioritizes decentralization, censorship resistance and security. It benefits from ultra-low gas prices compared to Ethereum, as well as fast and simple trading execution",
    "disabled": false,
    "displayName": "Quickswap V3",
    "enabled": true,
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": null,
    "id": "306",
    "listedAt": 1667063569,
    "logo": "https://icons.llama.fi/quickswap-dex.jpg",
    "methodology": Object {
      "Fees": "A 0.3% of each swap is collected as trading fees",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Protocol have no revenue",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/quickswap.ts",
    "module": "quickswap",
    "name": "Quickswap V3",
    "oracles": Array [
      "Witnet",
    ],
    "parentProtocol": "Quickswap",
    "protocolType": undefined,
    "symbol": "QUICK",
    "twitter": "QuickswapDEX",
    "url": "https://quickswap.exchange/",
    "versionKey": "v3",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/radioshack",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "fantom",
      "avax",
      "ethereum",
      "bsc",
      "polygon",
    ],
    "cmcId": "19006",
    "config": Object {
      "enabled": true,
      "id": "1616",
    },
    "description": "Uniswap fork",
    "disabled": false,
    "displayName": "RadioShack",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "radioshack",
    "id": "1616",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/radioshack.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/radioshack.ts",
    "module": "radioshack",
    "name": "RadioShack",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "RADIO",
    "twitter": "RadioShack",
    "url": "https://www.radioshack.org/",
  },
  Object {
    "address": "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Solana",
    "chains": Array [
      "solana",
    ],
    "cmcId": "8526",
    "config": Object {
      "enabled": true,
      "id": "214",
    },
    "description": "An on-chain order book AMM powering the evolution of DeFi.",
    "disabled": false,
    "displayName": "Raydium",
    "enabled": true,
    "gecko_id": "raydium",
    "id": "214",
    "logo": "https://icons.llama.fi/raydium.jpg",
    "methodology": Object {
      "Fees": "A 0.25% of each swap is collected as trading fees",
      "HoldersRevenue": "A 0.03% of the trade goes to buying RAY and distributing it to stakers",
      "ProtocolRevenue": "Raydium's AMM earns from the spread it places on the order book and all earnings from market making go back to Raydium liquidity providers",
      "Revenue": "A 0.03% of the trade goes to buying RAY and distributing it to stakers",
      "SupplySideRevenue": "A 0.22% of the trades goes back to the LP pool as fees earned",
      "UserFees": "User pays 0.25% fees on each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/raydium.ts",
    "module": "raydium",
    "name": "Raydium",
    "openSource": false,
    "protocolType": undefined,
    "symbol": "RAY",
    "twitter": "RaydiumProtocol",
    "url": "https://raydium.io",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/shib",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "11865",
    "config": Object {
      "enabled": true,
      "id": "397",
    },
    "description": "SHIB, LEASH, and BONE, come together to create ShibaSwap, the next evolution in DeFi platforms. ShibaSwap gives users the ability to DIG (provide liquidity), BURY (stake), and SWAP tokens to gain WOOF Returns through our sophisticated and innovative passive income reward system.",
    "disabled": false,
    "displayName": "ShibaSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "bone-shibaswap",
    "id": "397",
    "logo": "https://icons.llama.fi/shibaswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/shibaswap.ts",
    "module": "shibaswap",
    "name": "ShibaSwap",
    "protocolType": undefined,
    "symbol": "BONE",
    "twitter": "ShibaSwapDEX",
    "url": "https://shibaswap.com",
  },
  Object {
    "address": "moonriver:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Moonriver",
    "chains": Array [
      "moonriver",
    ],
    "cmcId": "13041",
    "config": Object {
      "enabled": true,
      "id": "551",
    },
    "description": "Solarbeam is a decentralized exchange, providing liquidity and enabling peer-to-peer transactions on the Moonriver Network.",
    "disabled": false,
    "displayName": "Solarbeam",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "solarbeam",
    "id": "551",
    "logo": "https://icons.llama.fi/solarbeam.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/solarbeam.ts",
    "module": "solarbeam",
    "name": "Solarbeam",
    "protocolType": undefined,
    "symbol": "SOLAR",
    "twitter": "Solarbeamio",
    "url": "https://app.solarbeam.io/exchange/swap",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2528",
    },
    "description": "SolidLizard is an Arbitrum One decentralized exchange that allows swaps at a low cost in swap fees and has a governance structure based on the ve(3,3) system",
    "disabled": false,
    "displayName": "SolidLizard",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "solidlizard",
    "id": "2528",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solidlizard.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/solidlizard.ts",
    "module": "solidlizard",
    "name": "SolidLizard",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SLIZ",
    "twitter": "solidlizardfi",
    "url": "https://solidlizard.finance/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/SolidlyLabs/Solidly-Audits/blob/main/audit_solidly.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2400",
    },
    "description": "Self-optimizing DEX combining the best of Curve, Uniswap and ve(3,3)",
    "disabled": false,
    "displayName": "Solidly V2",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "solidlydex",
    "id": "2400",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/solidly-v2.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/solidlydex.ts",
    "module": "solidlydex",
    "name": "Solidly V2",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "SOLID",
    "twitter": "SolidlyDEX",
    "url": "https://solidly.com",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://github.com/Layer3Org/spiritswap-core/blob/main/SpiritSwap-Core%20Security%20Audit%20Report.pdf",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "311",
    },
    "description": "AMM",
    "disabled": false,
    "displayName": "SpiritSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "311",
    "logo": "https://icons.llama.fi/spiritswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/spiritswap.ts",
    "module": "spiritswap",
    "name": "SpiritSwap AMM",
    "parentProtocol": "SpiritSwap",
    "protocolType": undefined,
    "symbol": "SPIRIT",
    "twitter": "Spirit_Swap",
    "url": "https://app.spiritswap.finance/#/",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://www.certik.org/projects/spookyswap",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "9608",
    "config": Object {
      "enabled": true,
      "id": "302",
    },
    "description": "Automated Market Maker.",
    "disabled": false,
    "displayName": "SpookySwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "spookyswap",
    "governanceID": Array [
      "snapshot:spookyswap.eth",
    ],
    "id": "302",
    "logo": "https://icons.llama.fi/spookyswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/spookyswap.ts",
    "module": "spookyswap",
    "name": "SpookySwap",
    "protocolType": undefined,
    "symbol": "BOO",
    "treasury": "spookyswap.js",
    "twitter": "SpookySwap",
    "url": "https://spooky.fi/#/",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/stellaswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": "17358",
    "config": Object {
      "enabled": true,
      "id": "1274",
    },
    "description": "Stellaswap is leading DEX on Moonbeam.",
    "disabled": false,
    "displayName": "StellaSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": "stellaswap",
    "id": "1274",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/stellaswap.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/stellaswap.ts",
    "module": "stellaswap",
    "name": "StellaSwap",
    "protocolType": undefined,
    "symbol": "STELLA",
    "twitter": "StellaSwap",
    "url": "https://stellaswap.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-SushiSwap-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "3",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
      "fantom",
      "arbitrum",
      "celo",
      "avax",
      "harmony",
      "moonriver",
      "xdai",
      "moonbeam",
      "boba",
      "fuse",
    ],
    "cmcId": "6758",
    "config": Object {
      "enabled": true,
      "id": "119",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "119",
        },
        "trident": Object {
          "enabled": true,
          "id": "2152",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum.
",
    "disabled": false,
    "displayName": "SushiSwap",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "sushi",
    "id": "119",
    "logo": "https://icons.llama.fi/sushiswap.png",
    "methodology": Object {
      "Fees": "SushiSwap charges a flat 0.3% fee",
      "HoldersRevenue": "SUSHI token stakers are entitled to share a 0.05% fee from each trade",
      "ProtocolRevenue": "Treasury have no revenue",
      "Revenue": "A 0.05% of each trade goes to token holders",
      "SupplySideRevenue": "Liquidity providers get 0.25% of all trades in their pools",
      "UserFees": "Users pay a 0.3% fee on each trade",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sushiswap",
    "module": "sushiswap",
    "name": "SushiSwap",
    "oracles": Array [],
    "parentProtocol": "Sushi",
    "protocolType": undefined,
    "symbol": "SUSHI",
    "treasury": "sushiswap.js",
    "twitter": "SushiSwap",
    "url": "https://sushi.com/",
    "versionKey": "classic",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
      "optimism",
      "kava",
      "metis",
      "bittorrent",
      "arbitrum",
      "bsc",
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "119",
      "protocolsData": Object {
        "classic": Object {
          "enabled": true,
          "id": "119",
        },
        "trident": Object {
          "enabled": true,
          "id": "2152",
        },
      },
    },
    "description": "TRIDENT 🔱 is a newly developed AMM and routing system from SushiSwap (Sushi).",
    "disabled": false,
    "displayName": "Sushi Trident",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "119",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/sushi-trident.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/dexs/sushiswap",
    "module": "sushiswap",
    "name": "Sushi Trident",
    "oracles": Array [],
    "parentProtocol": "Sushi",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "SushiSwap",
    "url": "https://www.sushi.com/swap",
    "versionKey": "trident",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://docs.traderjoexyz.com/main/security-and-contracts/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "468",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "468",
        },
        "v2": Object {
          "enabled": true,
          "id": "2393",
        },
      },
    },
    "description": "Trader Joe is your one-stop decentralized trading platform on the Avalanche network.",
    "disabled": false,
    "displayName": "Trader Joe DEX",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "468",
    "logo": "https://icons.llama.fi/trader-joe.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/traderjoe.ts",
    "module": "traderjoe",
    "name": "Trader Joe DEX",
    "parentProtocol": "Trader Joe",
    "protocolType": undefined,
    "symbol": "JOE",
    "treasury": "traderjoe.js",
    "twitter": "traderjoe_xyz",
    "url": "https://www.traderjoexyz.com",
    "versionKey": "v1",
  },
  Object {
    "address": "avax:******************************************",
    "audit_links": Array [
      "https://github.com/abdk-consulting/audits/blob/main/traderjoe/ABDK_TraderJoe_TraderJoe_v_2_0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Avalanche",
    "chains": Array [
      "arbitrum",
      "avax",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "468",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "468",
        },
        "v2": Object {
          "enabled": true,
          "id": "2393",
        },
      },
    },
    "description": "Joe V2 is a decentralized exchange based on Liquidity Book, a novel AMM protocol. Zero 0% slippage for swaps between ticks and Dynamic fees to improve liquidity provider profitability.",
    "disabled": false,
    "displayName": "Joe V2",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "468",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/joe-v2.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/traderjoe.ts",
    "module": "traderjoe",
    "name": "Joe V2",
    "oracles": Array [],
    "parentProtocol": "Trader Joe",
    "protocolType": undefined,
    "symbol": "JOE",
    "twitter": "traderjoe_xyz",
    "url": "https://traderjoexyz.com/home",
    "versionKey": "v2",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
    ],
    "cmcId": "20435",
    "config": Object {
      "enabled": true,
      "id": "1799",
    },
    "description": "A revolutionary new AMM based on Solidly launched on Optimism.",
    "disabled": false,
    "displayName": "Velodrome",
    "enabled": true,
    "forkedFrom": Array [
      "Solidly",
    ],
    "gecko_id": "velodrome-finance",
    "id": "1799",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/velodrome.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/velodrome.ts",
    "module": "velodrome",
    "name": "Velodrome",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VELO",
    "treasury": "velodrome.js",
    "twitter": "VelodromeFi",
    "url": "https://app.velodrome.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/0xGuard-com/audit-reports/blob/master/bitcoin.com/Bitcoin.com_final-audit-report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "SmartBCH",
    "chains": Array [
      "ethereum",
      "smartbch",
    ],
    "cmcId": "22929",
    "config": Object {
      "enabled": true,
      "id": "1732",
    },
    "description": "Verse DEX by Bitcoin.com enables permissionless trading via your Web3 wallet, including the multichain Bitcoin.com Wallet. Trading is currently available on Ethereum and SmartBCH, but more chains are coming soon. Sign up at getverse.com to get notified.",
    "disabled": false,
    "displayName": "Verse",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "verse-bitcoin",
    "governanceID": Array [
      "snapshot:verse.eth",
    ],
    "id": "1732",
    "listedAt": 1651771447,
    "logo": "https://icons.llama.fi/verse.png",
    "methodology": Object {
      "Fees": "0.3% trading fee",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees (sbch and ethereum) going to treasury, 0.05% on each swap",
      "SupplySideRevenue": "User fees (sbch and ethereum) distributed among LPs, 0.25% on each swap",
      "UserFees": "Fees paid by traders, 0.3% on each swap",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/verse.ts",
    "module": "verse",
    "name": "Verse",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "VERSE",
    "twitter": "BitcoinCom",
    "url": "https://verse.bitcoin.com",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://hacken.io/audits/#wombat_exchange",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Wombat-v1.0.pdf",
      "https://www.wombat.exchange/zokyo_wombat_audit_report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "19623",
    "config": Object {
      "enabled": true,
      "id": "1700",
    },
    "description": "Hyper efficient multi-chain stableswap. #BNB and beyond.",
    "disabled": false,
    "displayName": "Wombat Exchange",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "wombat-exchange",
    "id": "1700",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/wombat-exchange.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/wombat-exchange.ts",
    "module": "wombat-exchange",
    "name": "Wombat Exchange",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "WOM",
    "twitter": "WombatExchange",
    "url": "https://www.wombat.exchange/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/woofiswap",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "avax",
      "bsc",
      "fantom",
      "polygon",
      "arbitrum",
      "optimism",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1461",
    },
    "description": "WOOFi is a multichain DeFi platform which offers the best trade execution and lowest swap fee, opportunities to earn sustainable yields on crypto, and a high-efficiency solution for on-chain liquidity provision.",
    "disabled": false,
    "displayName": "WOOFi",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1461",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/woofi.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/woofi.ts",
    "module": "woofi",
    "name": "WOOFi",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "WOO Network",
    "protocolType": undefined,
    "symbol": "WOO",
    "twitter": "WOOnetwork",
    "url": "https://fi.woo.org",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/solidproof/projects/blob/main/ZyberSwap/SmartContract_Audit_Solidproof_Zyberswap.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "Community Driven Exchange on Arbitrum.",
    "disabled": false,
    "displayName": "Zyberswap AMM",
    "forkedFrom": Array [
      "Algebra DEX",
    ],
    "gecko_id": null,
    "id": "2467",
    "listedAt": 1674558950,
    "logo": "https://icons.llama.fi/zyberswap-amm.jpg",
    "methodology": Object {
      "Fees": "A 0.25% of each swap is collected as trading fees",
      "HoldersRevenue": "Stakers receive WETH a part of protocol revenue.",
      "ProtocolRevenue": "Protocol receives 0.1% on each swap.",
      "Revenue": "Protocol receives 0.1% on each swap. A part is used to buyback and burn and a part is used to buy WETH and distribute to stakers.",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.25% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/zyberswap.ts",
    "module": "zyberswap",
    "name": "Zyberswap AMM",
    "oracles": Array [
      "TWAP",
    ],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "zyberswap",
    "url": "https://www.zyberswap.io",
    "versionKey": "v2",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "ZyberSwap V3's Concentrated Liquidity feature allows liquidity providers to focus their liquidity within a specific price range, resulting in increased capital efficiency and reduced impermanent loss. This feature is particularly beneficial for those looking to optimize their investment strategy and maximize returns, as it allows for more precise tracking of Total Value Locked (TVL) within a designated price range.",
    "disabled": false,
    "displayName": "Zyberswap V3",
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "2467",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/zyberswap-v3.jpg",
    "methodology": Object {
      "Fees": "A dynamic swap fee is collected as trading fee",
      "HoldersRevenue": "A portion of the protocol fees is used to purchase WETH and distribute to stakers.",
      "ProtocolRevenue": "Protocol receives 10% of the dynamic swap fee",
      "Revenue": "Protocol receives 10% of the dynamic swap fee",
      "SupplySideRevenue": "90% of the dynamic swap fee is distributed to LPs",
      "UserFees": "User pays dynamic swap fee.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/zyberswap.ts",
    "module": "zyberswap",
    "name": "Zyberswap V3",
    "oracles": Array [],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "ZyberSwap",
    "url": "https://www.zyberswap.io/",
    "versionKey": "v3",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2467",
      "protocolsData": Object {
        "stable": Object {
          "id": "2530",
        },
        "v2": Object {
          "id": "2467",
        },
        "v3": Object {
          "id": "2602",
        },
      },
    },
    "description": "Community Driven Exchange on Arbitrum.",
    "disabled": false,
    "displayName": "ZyberSwap Stableswap",
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2467",
    "listedAt": 1676313508,
    "logo": "https://icons.llama.fi/zyberswap-stableswap.jpg",
    "methodology": Object {
      "Fees": "A 0.04% of each swap is collected as trading fees",
      "HoldersRevenue": "A portion of the protocol fees is used to purchase WETH and distribute to stakers.",
      "ProtocolRevenue": "Protocol receives 0.02% of the swap fee",
      "Revenue": "Protocol receives 0.02% of the swap fee",
      "SupplySideRevenue": "0.02% of the swap fee is distributed to LPs",
      "UserFees": "User pays a 0.04% fee on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/zyberswap.ts",
    "module": "zyberswap",
    "name": "ZyberSwap Stableswap",
    "oracles": Array [],
    "parentProtocol": "ZyberSwap",
    "protocolType": undefined,
    "symbol": "ZYB",
    "twitter": "zyberswap",
    "url": "https://app.zyberswap.io/exchange/swap",
    "versionKey": "stable",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Hegic-v1.0.pdf",
      "https://github.com/hegic/contracts/blob/main/packages/herge/docs/PeckShield-Audit-Report-Hegic-Herge-Protocol-Upgrade-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "6929",
    "config": Object {
      "enabled": true,
      "id": "128",
    },
    "description": "Hegic is an on-chain peer-to-pool options trading protocol on Arbitrum. You can trade ETH and WBTC ATM / OTM Calls / Puts & One-click Option Strategies on Hegic",
    "disabled": false,
    "displayName": "Hegic",
    "enabled": true,
    "gecko_id": "hegic",
    "id": "128",
    "logo": "https://icons.llama.fi/hegic.jpg",
    "methodology": "
The only thing anyone pays on Hegic is premiums. This goes into dailyFees.

The paid premiums can be considered Revenue, but at the moment we don't include it in the dashboard, because:

- payout is epoch-based (30 days), not daily
- stakers get all premiums minus profits of traders (!!!), so in fact stakers may get a negative payout (slashed stake)

Hegic Development fund also stakes ~170M Hegic tokens (16% of supply), so that could be considered ProtocolRevenue
  ",
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/hegic.ts",
    "module": "hegic",
    "name": "Hegic",
    "protocolType": undefined,
    "symbol": "HEGIC",
    "twitter": "HegicOptions",
    "url": "https://www.hegic.co/ ",
  },
  Object {
    "address": "optimism:******************************************",
    "audit_links": Array [
      "https://github.com/lyra-finance/lyra-protocol/tree/master/audits/v1",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
      "arbitrum",
    ],
    "cmcId": "15447",
    "config": Object {
      "enabled": true,
      "id": "503",
    },
    "description": "The first complete decentralized options protocol built on Ethereum",
    "disabled": false,
    "displayName": "Lyra",
    "enabled": true,
    "gecko_id": "lyra-finance",
    "governanceID": Array [
      "snapshot:lyra.eth",
    ],
    "id": "503",
    "logo": "https://icons.llama.fi/lyra.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/lyra.ts",
    "module": "lyra",
    "name": "Lyra",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "LYRA",
    "twitter": "lyrafinance",
    "url": "https://www.lyra.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://solidity.finance/audits/Premia",
      "https://hacken.io/audits/#solidstate",
      "https://hacken.io/audits/#premia_finance",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "ethereum",
      "fantom",
      "optimism",
    ],
    "cmcId": "8476",
    "config": Object {
      "enabled": true,
      "id": "381",
    },
    "description": "Premia's automated options market enables best-in-class pricing based on realtime supply and demand, bringing fully-featured peer-to-pool trading and capital efficiency to DeFi options.",
    "disabled": false,
    "displayName": "Premia",
    "enabled": true,
    "gecko_id": "premia",
    "id": "381",
    "logo": "https://icons.llama.fi/premia.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/premia.ts",
    "module": "premia",
    "name": "Premia",
    "protocolType": undefined,
    "symbol": "PREMIA",
    "twitter": "PremiaFinance",
    "url": "https://premia.finance/",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "bitcoin",
    ],
    "cmcId": "1",
    "config": Object {
      "enabled": true,
      "id": "1",
    },
    "disabled": false,
    "displayName": "Bitcoin",
    "enabled": true,
    "geckoId": "bitcoin",
    "gecko_id": "bitcoin",
    "id": "1",
    "logo": "https://icons.llama.fi/chains/rsz_bitcoin.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/bitcoin.ts",
    "module": "bitcoin",
    "name": "Bitcoin",
    "protocolType": "chain",
    "symbol": "BTC",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.pancakeswap.finance/#is-it-safe",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "The #1 AMM and yield farm on Binance Smart Chain",
    "disabled": true,
    "displayName": "PancakeSwap AMM V1",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "194",
    "logo": "https://icons.llama.fi/pancakeswap-amm-v1.jpg",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap AMM V1",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://v1exchange.pancakeswap.finance/",
    "versionKey": "v1",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://docs.pancakeswap.finance/#is-it-safe",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "The #1 AMM and yield farm on Binance Smart Chain",
    "disabled": false,
    "displayName": "PancakeSwap AMM",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": null,
    "id": "194",
    "logo": "https://icons.llama.fi/pancakeswap-amm.jpg",
    "methodology": Object {
      "Fees": "All fees comes from the user.",
      "HoldersRevenue": "0.0575% is used to facilitate CAKE buyback and burn.",
      "ProtocolRevenue": "Treasury receives 0.0225% of each swap.",
      "Revenue": "All revenue generated comes from user fees.",
      "SupplySideRevenue": "LPs receive 0.17% of the fees.",
      "UserFees": "User pays 0.25% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap AMM",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://pancakeswap.finance/",
    "versionKey": "v2",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "194",
      "protocolsData": Object {
        "stableswap": Object {
          "enabled": true,
          "id": "2529",
        },
        "v1": Object {
          "disabled": true,
          "enabled": true,
          "id": "2590",
        },
        "v2": Object {
          "enabled": true,
          "id": "194",
        },
      },
    },
    "description": "StableSwap on PancakeSwap is a feature to trade stable pairs with a lower slippage based on an invariant curve slippage function. It is designed to swap specific assets that are priced closely – such as USD stablecoin.",
    "disabled": false,
    "displayName": "PancakeSwap StableSwap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "194",
    "listedAt": 1676313502,
    "logo": "https://icons.llama.fi/pancakeswap-stableswap.jpg",
    "methodology": Object {
      "Fees": "All fees comes from the user fees, which is 025% of each trade.",
      "HoldersRevenue": "A 40% of the fees is used to facilitate CAKE buyback and burn.",
      "ProtocolRevenue": "Treasury receives 10% of the fees.",
      "Revenue": "Revenue is 50% of the fees paid by users.",
      "SupplySideRevenue": "LPs receive 50% of the fees.",
      "UserFees": "User pays 0.25% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/pancakeswap",
    "module": "pancakeswap",
    "name": "PancakeSwap StableSwap",
    "oracles": Array [],
    "parentProtocol": "PancakeSwap",
    "protocolType": undefined,
    "symbol": "CAKE",
    "twitter": "PancakeSwap",
    "url": "https://pancakeswap.finance/swap",
    "versionKey": "stableswap",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "595",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V1
",
    "disabled": false,
    "displayName": "Uniswap V1",
    "enabled": true,
    "gecko_id": "uniswap",
    "id": "1",
    "listedAt": 1666191149,
    "logo": "https://icons.llama.fi/uniswap-v1.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V1",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1069",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V2
",
    "disabled": false,
    "displayName": "Uniswap V2",
    "enabled": true,
    "gecko_id": null,
    "id": "1",
    "listedAt": 1666191162,
    "logo": "https://icons.llama.fi/uniswap-v2.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.3% fees on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V2",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v2",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Uniswap/uniswap-v3-core/tree/main/audits",
      "https://github.com/Uniswap/uniswap-v3-periphery/tree/main/audits",
      "https://github.com/ConsenSys/Uniswap-audit-report-2018-12",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "arbitrum",
      "polygon",
      "celo",
      "bsc",
    ],
    "cmcId": "1348",
    "config": Object {
      "enabled": true,
      "id": "1",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "2196",
        },
        "v2": Object {
          "enabled": true,
          "id": "2197",
        },
        "v3": Object {
          "enabled": true,
          "id": "2198",
        },
      },
    },
    "description": "A fully decentralized protocol for automated liquidity provision on Ethereum. V2
",
    "disabled": false,
    "displayName": "Uniswap V3",
    "enabled": true,
    "gecko_id": null,
    "id": "1",
    "listedAt": 1666191475,
    "logo": "https://icons.llama.fi/uniswap-v3.png",
    "methodology": Object {
      "Fees": "Swap fees paid by users",
      "HoldersRevenue": "Holders have no revenue.",
      "ProtocolRevenue": "Protocol have no revenue.",
      "Revenue": "Percentage of swap fees going to treasury and/or token holders",
      "SupplySideRevenue": "All user fees are distributed among LPs.",
      "UserFees": "User pays 0.05%, 0.30%, or 1% on each swap.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/protocols/uniswap",
    "module": "uniswap",
    "name": "Uniswap V3",
    "oracles": Array [],
    "parentProtocol": "Uniswap",
    "protocolType": undefined,
    "symbol": "UNI",
    "treasury": "uniswap.js",
    "twitter": "Uniswap",
    "url": "https://uniswap.org/",
    "versionKey": "v3",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://assets.website-files.com/622a09bc809cd190f58b7b15/627cd4450f5eb2df769b4ac1_PeckShield-Audit-Report-0VIX-v1.0.pdf",
      "https://assets.website-files.com/622a09bc809cd190f58b7b15/627a5d6eea14146296b3261b_0VIX_Audit_Report_by_WatchPug.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1614",
    },
    "description": "0VIX is an open-source lending and borrowing protocol enhanced with veTokenomics",
    "disabled": false,
    "displayName": "0vix",
    "enabled": true,
    "forkedFrom": Array [
      "Compound",
    ],
    "gecko_id": null,
    "id": "1614",
    "listedAt": 1649156430,
    "logo": "https://icons.llama.fi/0vix.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/0vix.ts",
    "module": "0vix",
    "name": "0vix",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "0vixProtocol",
    "url": "https://www.0vix.com",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://aave.com/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "111",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "1838",
        },
        "v2": Object {
          "enabled": true,
          "id": "111",
        },
        "v3": Object {
          "enabled": true,
          "id": "1599",
        },
      },
      "startFrom": 1647648000,
    },
    "description": "Earn interest, borrow assets, and build applications",
    "disabled": false,
    "displayName": "AAVE V1",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "111",
    "listedAt": 1655586107,
    "logo": "https://icons.llama.fi/aave-v1.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/aave",
    "module": "aave",
    "name": "AAVE V1",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "AAVE",
    "protocolType": undefined,
    "symbol": "AAVE",
    "twitter": "AaveAave",
    "url": "https://aave.com",
    "versionKey": "v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://aave.com/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Ethereum",
    "chains": Array [
      "avax",
      "ethereum",
      "polygon",
    ],
    "cmcId": "7278",
    "config": Object {
      "enabled": true,
      "id": "111",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "1838",
        },
        "v2": Object {
          "enabled": true,
          "id": "111",
        },
        "v3": Object {
          "enabled": true,
          "id": "1599",
        },
      },
      "startFrom": 1647648000,
    },
    "description": "Aave is an Open Source and Non-Custodial protocol to earn interest on deposits and borrow assets",
    "disabled": false,
    "displayName": "AAVE V2",
    "enabled": true,
    "gecko_id": "aave",
    "id": "111",
    "logo": "https://icons.llama.fi/aave-v2.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/aave",
    "module": "aave",
    "name": "AAVE V2",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "AAVE",
    "protocolType": undefined,
    "symbol": "AAVE",
    "treasury": "aave.js",
    "twitter": "AaveAave",
    "url": "https://aave.com
",
    "versionKey": "v2",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://aave.com/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Optimism",
    "chains": Array [
      "avax",
      "polygon",
      "arbitrum",
      "optimism",
      "fantom",
      "harmony",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "111",
      "protocolsData": Object {
        "v1": Object {
          "enabled": true,
          "id": "1838",
        },
        "v2": Object {
          "enabled": true,
          "id": "111",
        },
        "v3": Object {
          "enabled": true,
          "id": "1599",
        },
      },
      "startFrom": 1647648000,
    },
    "description": "Earn interest, borrow assets, and build applications",
    "disabled": false,
    "displayName": "AAVE V3",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "111",
    "listedAt": 1648776877,
    "logo": "https://icons.llama.fi/aave-v3.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/aave",
    "module": "aave",
    "name": "AAVE V3",
    "oracles": Array [],
    "parentProtocol": "AAVE",
    "protocolType": undefined,
    "symbol": "AAVE",
    "twitter": "AaveAave",
    "url": "https://aave.com",
    "versionKey": "v3",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certora.com/pubs/SushiBentoboxFeb2021.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "CDP",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "fantom",
      "avax",
      "bsc",
      "arbitrum",
    ],
    "cmcId": "11289",
    "config": Object {
      "enabled": true,
      "id": "347",
    },
    "description": "Abracadabra.money is a spell book that allows users to produce magic internet money ($MIM) which is a stable coin that you can swap for any other traditional stable coin.",
    "disabled": false,
    "displayName": "Abracadabra",
    "enabled": true,
    "gecko_id": "spell-token",
    "id": "347",
    "logo": "https://icons.llama.fi/abracadabra.svg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Interest paid by borrowers",
      "Revenue": "Interest paid by borrowers",
      "UserFees": "Interest paid to borrow",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/abracadabra.ts",
    "module": "abracadabra",
    "name": "Abracadabra",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "SPELL",
    "treasury": "abracadabra.js",
    "twitter": "MIM_Spell",
    "url": "https://abracadabra.money/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/AngleProtocol/angle-core/tree/main/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "CDP",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "avax",
      "ethereum",
      "optimism",
      "polygon",
    ],
    "cmcId": "12761",
    "config": Object {
      "enabled": true,
      "id": "756",
    },
    "description": "Angle is an over-collateralized, decentralized and capital-efficient stablecoin protocol. It is based on two smart contract modules. Angle Core module allows to mint agEUR from any token at face value, open perpetuals on collateral/stablecoin pairs, or deposit tokens to earn yield. Angle Borrowing module allows to borrow or get leverage with agEUR from tokens deposited as collateral. Angle is governed by veANGLE holders.",
    "disabled": false,
    "displayName": "Angle",
    "enabled": true,
    "gecko_id": "angle-protocol",
    "governanceID": Array [
      "snapshot:anglegovernance.eth",
    ],
    "id": "756",
    "listedAt": 1635962344,
    "logo": "https://icons.llama.fi/angle.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Interest paid by borrowers",
      "Revenue": "Interest paid by borrowers",
      "UserFees": "Interest paid to borrow",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/angle",
    "module": "angle",
    "name": "Angle",
    "oracles": Array [
      "Chainlink",
      "TWAP",
    ],
    "protocolType": undefined,
    "symbol": "ANGLE",
    "twitter": "AngleProtocol",
    "url": "https://app.angle.money",
  },
  Object {
    "categories": Array [
      "EVM",
      "Rollup",
    ],
    "category": "Rollup",
    "chainId": 42161,
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "category": "Rollup",
      "enabled": true,
      "id": "42161",
      "startFrom": 1660608000,
    },
    "disabled": false,
    "displayName": "Arbitrum",
    "enabled": true,
    "geckoId": null,
    "gecko_id": null,
    "id": "42161",
    "logo": "https://icons.llama.fi/chains/rsz_arbitrum.jpg",
    "methodology": Object {
      "Fees": "Fees collected by sequencer paid by users",
      "ProtocolRevenue": "ETH earned from user fees minus cost to send transactions in L1",
      "Revenue": "ETH earned from user fees minus cost to send transactions in L1",
      "UserFees": "Fees paid by users to sequencer",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/arbitrum",
    "module": "arbitrum",
    "name": "Arbitrum",
    "parent": Object {
      "chain": "Ethereum",
      "types": Array [
        "L2",
        "gas",
      ],
    },
    "protocolType": "chain",
    "startFrom": 1660608000,
    "symbol": null,
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 1313161554,
    "chains": Array [
      "aurora",
    ],
    "cmcId": "14803",
    "config": Object {
      "enabled": true,
      "id": "14803",
    },
    "disabled": false,
    "displayName": "Aurora",
    "enabled": true,
    "geckoId": "aurora-near",
    "gecko_id": "aurora-near",
    "id": "14803",
    "logo": "https://icons.llama.fi/chains/rsz_aurora.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/aurora.ts",
    "module": "aurora",
    "name": "Aurora",
    "parent": Object {
      "chain": "Near",
      "types": Array [
        "emulator",
        "gas",
      ],
    },
    "protocolType": "chain",
    "symbol": "AURORA",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 43114,
    "chains": Array [
      "avax",
    ],
    "cmcId": "5805",
    "config": Object {
      "enabled": true,
      "id": "5805",
    },
    "disabled": false,
    "displayName": "Avalanche",
    "enabled": true,
    "geckoId": "avalanche-2",
    "gecko_id": "avalanche-2",
    "id": "5805",
    "logo": "https://icons.llama.fi/chains/rsz_avalanche.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/avalanche.ts",
    "module": "avalanche",
    "name": "Avalanche",
    "protocolType": "chain",
    "symbol": "AVAX",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://github.com/BetSwirl/Smart-Contracts/tree/v2/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Prediction Market",
    "chain": "Binance",
    "chains": Array [
      "bsc",
      "polygon",
      "avax",
    ],
    "cmcId": "18453",
    "config": Object {
      "enabled": true,
      "id": "1911",
    },
    "description": "BetSwirl aims to be the decentralized gambling platform: provably fair, non-custodial, licensed, trustless and community driven.",
    "disabled": false,
    "displayName": "BetSwirl",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "betswirl",
    "id": "1911",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/betswirl.jpg",
    "methodology": Object {
      "Fees": "All fees (called «house edge» from 2.4% to 3.5% of the payout) comes from the player's bet. The fee has several allocations: Bank, Partner, Dividends, Treasury, and Team.",
      "HoldersRevenue": "Dividends fee allocations.",
      "ProtocolRevenue": "Treasury and Team fee allocations.",
      "Revenue": "Dividends, Treasury and Team fee allocations.",
      "SupplySideRevenue": "Bank and Partner fee allocations",
      "UserFees": "The player is charged of the fee when a bet is won.",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/betswirl",
    "module": "betswirl",
    "name": "BetSwirl",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "BETS",
    "treasury": "betswirl.js",
    "twitter": "BetSwirl",
    "url": "https://www.betswirl.com",
  },
  Object {
    "address": "aptos:0x27fafcc4e39daac97556af8a803dbb52bcb03f0821898dc845ac54225b9793eb::move_coin::MoveCoin",
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Aptos",
    "chains": Array [
      "aptos",
    ],
    "cmcId": "23359",
    "config": Object {
      "enabled": true,
      "id": "2396",
    },
    "description": "The leading Multi-chain NFT Marketplace on Aptos & Sui Blockchain.",
    "disabled": false,
    "displayName": "BlueMove",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "bluemove",
    "id": "2396",
    "listedAt": 1672334345,
    "logo": "https://icons.llama.fi/bluemove.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/bluemove",
    "module": "bluemove",
    "name": "BlueMove",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "MOVE",
    "twitter": "BlueMove_OA",
    "url": "https://bluemove.net",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2414",
    },
    "description": "The NFT marketplace for pro traders",
    "disabled": false,
    "displayName": "Blur",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "blur",
    "id": "2414",
    "listedAt": 1672970801,
    "logo": "https://icons.llama.fi/blur.png",
    "methodology": Object {
      "Fees": "All royalties+marketplace fees paid on sales",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "At the moment blur charges no marketplace fees",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/blur.ts",
    "module": "blur",
    "name": "Blur",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "BLUR",
    "twitter": "blur_io",
    "url": "https://blur.io/",
  },
  Object {
    "categories": Array [
      "EVM",
      "Rollup",
    ],
    "category": "Chain",
    "chainId": 288,
    "chains": Array [
      "boba",
    ],
    "cmcId": "14556",
    "config": Object {
      "enabled": false,
      "id": "14556",
    },
    "disabled": false,
    "displayName": "Boba",
    "enabled": false,
    "geckoId": "boba-network",
    "gecko_id": "boba-network",
    "id": "14556",
    "logo": "https://icons.llama.fi/chains/rsz_boba.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/boba.ts",
    "module": "boba",
    "name": "Boba",
    "parent": Object {
      "chain": "Ethereum",
      "types": Array [
        "L2",
        "gas",
      ],
    },
    "protocolType": "chain",
    "symbol": "BOBA",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 56,
    "chains": Array [
      "bsc",
    ],
    "cmcId": "1839",
    "config": Object {
      "enabled": true,
      "id": "1839",
    },
    "disabled": false,
    "displayName": "Binance",
    "enabled": true,
    "geckoId": "binancecoin",
    "gecko_id": "binancecoin",
    "id": "1839",
    "logo": "https://icons.llama.fi/chains/rsz_binance.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/bsc.ts",
    "module": "bsc",
    "name": "Binance",
    "protocolType": "chain",
    "symbol": "BNB",
  },
  Object {
    "address": "arbitrum:******************************************",
    "audit_links": Array [
      "https://github.com/sherlock-protocol/sherlock-reports/blob/main/audits/2022.12.08%20-%20Final%20-%20Buffer%20Finance%20Audit%20Report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Options",
    "chain": "Binance",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": "12440",
    "config": Object {
      "enabled": true,
      "id": "1304",
    },
    "description": "Buffer Finance is an exotic options trading platform built for trading price volatility and hedge risks while trading high leveraged positions. With Buffer you can get access to diverse markets in completely non-custodial way and all trades condenses to a simple question whether the price of the underlying will go up or down",
    "disabled": false,
    "displayName": "Buffer Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "ibuffer-token",
    "id": "1304",
    "listedAt": 1642780144,
    "logo": "https://icons.llama.fi/buffer-finance.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/buffer",
    "module": "buffer",
    "name": "Buffer Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "BFR",
    "twitter": "Buffer_Finance",
    "url": "https://www.buffer.finance",
  },
  Object {
    "categories": Array [
      "EVM",
      "Cosmos",
    ],
    "category": "Chain",
    "chains": Array [
      "canto",
    ],
    "cmcId": "21516",
    "config": Object {
      "enabled": true,
      "id": "21516",
    },
    "disabled": false,
    "displayName": "Canto",
    "enabled": true,
    "geckoId": "canto",
    "gecko_id": "canto",
    "id": "21516",
    "logo": "https://icons.llama.fi/chains/rsz_canto.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/canto.ts",
    "module": "canto",
    "name": "Canto",
    "protocolType": "chain",
    "symbol": "CANTO",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "cardano",
    ],
    "cmcId": "2010",
    "config": Object {
      "enabled": false,
      "id": "2010",
    },
    "disabled": false,
    "displayName": "Cardano",
    "enabled": false,
    "geckoId": "cardano",
    "gecko_id": "cardano",
    "id": "2010",
    "logo": "https://icons.llama.fi/chains/rsz_cardano.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/cardano.ts",
    "module": "cardano",
    "name": "Cardano",
    "protocolType": "chain",
    "symbol": "ADA",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 42220,
    "chains": Array [
      "celo",
    ],
    "cmcId": "5567",
    "config": Object {
      "enabled": true,
      "id": "5567",
    },
    "disabled": false,
    "displayName": "Celo",
    "enabled": true,
    "geckoId": "celo",
    "gecko_id": "celo",
    "id": "5567",
    "logo": "https://icons.llama.fi/chains/rsz_celo.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/celo.ts",
    "module": "celo",
    "name": "Celo",
    "protocolType": "chain",
    "symbol": "CELO",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Oracle",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
    ],
    "cmcId": "1975",
    "config": Object {
      "enabled": true,
      "id": "2623",
      "protocolsData": Object {
        "keepers": Object {
          "id": "2623",
        },
        "requests": Object {
          "id": "2623",
        },
        "vrf v1": Object {
          "id": "2623",
        },
        "vrf v2": Object {
          "id": "2623",
        },
      },
    },
    "description": "Chainlink decentralized oracle networks provide tamper-proof inputs, outputs, and computations to support advanced smart contracts on any blockchain.",
    "disabled": false,
    "displayName": "Chainlink",
    "forkedFrom": Array [],
    "gecko_id": "chainlink",
    "id": "2623",
    "logo": "https://icons.llama.fi/chainlink.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/chainlink.ts",
    "module": "chainlink",
    "name": "Chainlink",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "LINK",
    "twitter": "chainlink",
    "url": "https://chain.link",
    "versionKey": "vrf v1",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Oracle",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
    ],
    "cmcId": "1975",
    "config": Object {
      "enabled": true,
      "id": "2623",
      "protocolsData": Object {
        "keepers": Object {
          "id": "2623",
        },
        "requests": Object {
          "id": "2623",
        },
        "vrf v1": Object {
          "id": "2623",
        },
        "vrf v2": Object {
          "id": "2623",
        },
      },
    },
    "description": "Chainlink decentralized oracle networks provide tamper-proof inputs, outputs, and computations to support advanced smart contracts on any blockchain.",
    "disabled": false,
    "displayName": "Chainlink",
    "forkedFrom": Array [],
    "gecko_id": "chainlink",
    "id": "2623",
    "logo": "https://icons.llama.fi/chainlink.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/chainlink.ts",
    "module": "chainlink",
    "name": "Chainlink",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "LINK",
    "twitter": "chainlink",
    "url": "https://chain.link",
    "versionKey": "vrf v1",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Oracle",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
    ],
    "cmcId": "1975",
    "config": Object {
      "enabled": true,
      "id": "2623",
      "protocolsData": Object {
        "keepers": Object {
          "id": "2623",
        },
        "requests": Object {
          "id": "2623",
        },
        "vrf v1": Object {
          "id": "2623",
        },
        "vrf v2": Object {
          "id": "2623",
        },
      },
    },
    "description": "Chainlink decentralized oracle networks provide tamper-proof inputs, outputs, and computations to support advanced smart contracts on any blockchain.",
    "disabled": false,
    "displayName": "Chainlink",
    "forkedFrom": Array [],
    "gecko_id": "chainlink",
    "id": "2623",
    "logo": "https://icons.llama.fi/chainlink.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/chainlink.ts",
    "module": "chainlink",
    "name": "Chainlink",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "LINK",
    "twitter": "chainlink",
    "url": "https://chain.link",
    "versionKey": "vrf v1",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Oracle",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "bsc",
      "polygon",
    ],
    "cmcId": "1975",
    "config": Object {
      "enabled": true,
      "id": "2623",
      "protocolsData": Object {
        "keepers": Object {
          "id": "2623",
        },
        "requests": Object {
          "id": "2623",
        },
        "vrf v1": Object {
          "id": "2623",
        },
        "vrf v2": Object {
          "id": "2623",
        },
      },
    },
    "description": "Chainlink decentralized oracle networks provide tamper-proof inputs, outputs, and computations to support advanced smart contracts on any blockchain.",
    "disabled": false,
    "displayName": "Chainlink",
    "forkedFrom": Array [],
    "gecko_id": "chainlink",
    "id": "2623",
    "logo": "https://icons.llama.fi/chainlink.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/chainlink.ts",
    "module": "chainlink",
    "name": "Chainlink",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "LINK",
    "twitter": "chainlink",
    "url": "https://chain.link",
    "versionKey": "vrf v1",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://compound.finance/docs/security",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "114",
    },
    "description": "Compound is an algorithmic, autonomous interest rate protocol built for developers, to unlock a universe of open financial applications.",
    "disabled": false,
    "displayName": "Compound",
    "enabled": true,
    "gecko_id": null,
    "id": "114",
    "logo": "https://icons.llama.fi/compound.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/compound.ts",
    "module": "compound",
    "name": "Compound",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Compound Finance",
    "protocolType": undefined,
    "symbol": "COMP",
    "treasury": "compound.js",
    "twitter": "compoundfinance",
    "url": "https://compound.finance",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/convex-eth/platform/blob/main/audit/Convex%20Platform%20Security%20Audit%20Report.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Yield",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "9903",
    "config": Object {
      "enabled": true,
      "id": "319",
    },
    "description": "Convex simplifies your Curve-boosting experience to maximize your yields.",
    "disabled": false,
    "displayName": "Convex Finance",
    "enabled": true,
    "gecko_id": "convex-finance",
    "id": "319",
    "logo": "https://icons.llama.fi/convex-finance.jpg",
    "methodology": Object {
      "Fees": "Includes all treasury revenue, all revenue to CVX lockers and stakers and all revenue to liquid derivatives (cvxCRV, cvxFXS)",
      "HoldersRevenue": "All revenue going to CVX lockers and stakers, including bribes",
      "ProtocolRevenue": "Share of revenue going to Convex treasury (includes caller incentives on Frax pools)",
      "Revenue": "Sum of protocol revenue and holders' revenue",
      "SupplySideRevenue": "All CRV, CVX and FXS rewards redistributed to liquidity providers staking on Convex.",
      "UserFees": "No user fees",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/convex.ts",
    "module": "convex",
    "name": "Convex Finance",
    "protocolType": undefined,
    "symbol": "CVX",
    "treasury": "convex.js",
    "twitter": "ConvexFinance",
    "url": "https://www.convexfinance.com/",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2525",
    },
    "description": "Covo Finance is a decentralized trading platform that offers spot and perpetual trading with low swap fees and zero market impact. The platform offers a maximum leverage of 50x on major cryptos and is backed by a singular multi-asset pool that generates income for liquidity providers through fees garnered from market making, swap transactions, leveraged trading (including spreads, funding fees, and liquidations), and rebalancing of assets",
    "disabled": false,
    "displayName": "Covo Finance",
    "enabled": true,
    "forkedFrom": Array [
      "GMX",
    ],
    "gecko_id": null,
    "id": "2525",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/covo-finance.png",
    "methodology": Object {
      "Fees": "Fees from open/close position, swap, mint and burn (based on tokens balance in the pool) and borrow fee ((assets borrowed)/(total assets in pool)*0.01%)",
      "HoldersRevenue": "30% of all collected fees goes to COVO stakers",
      "ProtocolRevenue": "Treasury has no revenue",
      "Revenue": "Revenue is 30% of all collected fees, which goes to COVO stakers",
      "SupplySideRevenue": "70% of all collected fees goes to COVOLP holders",
      "UserFees": "Fees from open/close position, swap and borrow fee ((assets borrowed)/(total assets in pool)*0.01%)",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/covo-finance.ts",
    "module": "covo-finance",
    "name": "Covo Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "COVO",
    "twitter": "covofinance",
    "url": "https://covo.finance",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Dexs",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "xdai",
    ],
    "cmcId": "19269",
    "config": Object {
      "enabled": true,
      "id": "2643",
    },
    "description": "CoW Swap finds the lowest prices across all exchanges and aggregators & saves you more by matching Coincidences of Wants (CoWs) and protecting from MEV",
    "disabled": false,
    "displayName": "CoW Swap",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "cow-protocol",
    "id": "2643",
    "logo": "https://icons.llama.fi/cow-swap.jpg",
    "methodology": Object {
      "Fees": "Trading fees",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Trading fees - transation fees",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Trading fees",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/cow-protocol.ts",
    "module": "cow-protocol",
    "name": "CoW Swap",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "COW",
    "twitter": "CoWSwap",
    "url": "https://cow.fi",
  },
  Object {
    "categories": Array [
      "EVM",
      "Cosmos",
    ],
    "category": "Chain",
    "chainId": 25,
    "chains": Array [
      "cronos",
    ],
    "cmcId": "3635",
    "config": Object {
      "enabled": true,
      "id": "3635",
    },
    "disabled": false,
    "displayName": "Cronos",
    "enabled": true,
    "geckoId": "crypto-com-chain",
    "gecko_id": "crypto-com-chain",
    "id": "3635",
    "logo": "https://icons.llama.fi/chains/rsz_cronos.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/cronos.ts",
    "module": "cronos",
    "name": "Cronos",
    "protocolType": "chain",
    "symbol": "CRO",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "doge",
    ],
    "cmcId": "74",
    "config": Object {
      "enabled": true,
      "id": "74",
    },
    "disabled": false,
    "displayName": "Doge",
    "enabled": true,
    "geckoId": "dogecoin",
    "gecko_id": "dogecoin",
    "id": "74",
    "logo": "https://icons.llama.fi/chains/rsz_doge.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/doge.ts",
    "module": "doge",
    "name": "Doge",
    "protocolType": "chain",
    "symbol": "DOGE",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 1,
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1027",
    "config": Object {
      "enabled": true,
      "id": "1027",
    },
    "disabled": false,
    "displayName": "Ethereum",
    "enabled": true,
    "geckoId": "ethereum",
    "gecko_id": "ethereum",
    "id": "1027",
    "logo": "https://icons.llama.fi/chains/rsz_ethereum.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/ethereum",
    "module": "ethereum",
    "name": "Ethereum",
    "protocolType": "chain",
    "symbol": "ETH",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 250,
    "chains": Array [
      "fantom",
    ],
    "cmcId": "3513",
    "config": Object {
      "enabled": true,
      "id": "3513",
    },
    "disabled": false,
    "displayName": "Fantom",
    "enabled": true,
    "geckoId": "fantom",
    "gecko_id": "fantom",
    "id": "3513",
    "logo": "https://icons.llama.fi/chains/rsz_fantom.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/fantom.ts",
    "module": "fantom",
    "name": "Fantom",
    "protocolType": "chain",
    "symbol": "FTM",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://docs.firebird.finance/security-safu/audits",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Firebird-Swap-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Dexs",
    "chain": "Polygon",
    "chains": Array [
      "avax",
      "fantom",
      "polygon",
      "bsc",
      "cronos",
      "ethereum",
    ],
    "cmcId": "12224",
    "config": Object {
      "enabled": true,
      "id": "384",
    },
    "description": "Firebird is a one-stop DeFi platform that aims to revolutionize DeFi services by meeting all your DeFi needs.",
    "disabled": false,
    "displayName": "Firebird",
    "enabled": true,
    "forkedFrom": Array [
      "Uniswap",
    ],
    "gecko_id": "firebird-finance",
    "governanceID": Array [
      "snapshot:firebirdfinance.eth",
    ],
    "id": "384",
    "logo": "https://icons.llama.fi/firebird.jpg",
    "methodology": Object {
      "Fees": "Fees collected from user trading fees",
      "HoldersRevenue": "Money going to governance token holders",
      "ProtocolRevenue": "Percentage of swap fees going to treasury",
      "Revenue": "Revenue is 100% fee of each swap which goes to treasury",
      "SupplySideRevenue": "Liquidity providers revenue",
      "UserFees": "Swap fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/firebird-finance.ts",
    "module": "firebird-finance",
    "name": "Firebird",
    "protocolType": undefined,
    "symbol": "HOPE",
    "twitter": "FinanceFirebird",
    "url": "https://app.firebird.finance/one-pool",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Liquid Staking",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2221",
    },
    "description": "Frax Ether is a liquid ETH staking derivative designed to uniquely leverage the Frax Finance ecosystem to maximize staking yield and smoothen the Ethereum staking process for a simplified, secure, and DeFi-native way to earn interest on ETH. ",
    "disabled": false,
    "displayName": "Frax Ether",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2221",
    "listedAt": 1666641678,
    "logo": "https://icons.llama.fi/frax-ether.jpg",
    "methodology": Object {
      "Fees": "Staking rewards",
      "ProtocolRevenue": "Fee from users rewards",
      "Revenue": "Percentage of user rewards paid to protocol",
      "SupplySideRevenue": "Revenue earned by stETH holders",
      "UserFees": "Percentage of rewards paid to protocol",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/frax-ether.ts",
    "module": "frax-ether",
    "name": "Frax Ether",
    "oracles": Array [],
    "parentProtocol": "Frax Finance",
    "protocolType": undefined,
    "symbol": "frxETH",
    "twitter": "fraxfinance",
    "url": "https://app.frax.finance/frxeth/mint",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "Algo-Stables",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2607",
    },
    "description": "The Frax Price Index (FPI) is the second stablecoin of the Frax Finance ecosystem. FPI is the first stablecoin pegged to a basket of real-world consumer items as defined by the US CPI-U average. The FPI stablecoin is intended to keep its price constant to the price of all items within the CPI basket and thus hold its purchasing power with on-chain stability mechanisms.f locking the asset.",
    "disabled": false,
    "displayName": "Frax FPI",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2607",
    "listedAt": 1677584770,
    "logo": "https://icons.llama.fi/frax-fpi.png",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/frax-fpi.ts",
    "module": "frax-fpi",
    "name": "Frax FPI",
    "oracles": Array [],
    "parentProtocol": "Frax Finance",
    "protocolType": undefined,
    "symbol": "FPI",
    "twitter": "fraxfinance",
    "url": "https://app.frax.finance/fpifpis/fpi",
  },
  Object {
    "address": "polygon:******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/gains-network",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
      "arbitrum",
    ],
    "cmcId": "13663",
    "config": Object {
      "enabled": true,
      "id": "1018",
    },
    "description": "Gains Network is building the decentralized finance ecosystem of the future. Our goal is to build the most advanced and optimised suite of DeFi products in the space.",
    "disabled": false,
    "displayName": "Gains Network",
    "enabled": true,
    "gecko_id": "gains-network",
    "id": "1018",
    "listedAt": 1639418230,
    "logo": "https://icons.llama.fi/gains-network.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/gains-network.ts",
    "module": "gains-network",
    "name": "Gains Network",
    "protocolType": undefined,
    "symbol": "GNS",
    "twitter": "GainsNetwork_io",
    "url": "https://gainsnetwork.io",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/Gearbox-protocol/gearbox-contracts/tree/master/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Leveraged Farming",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1108",
    },
    "description": "Get up to 10x leverage and use it across DeFi protocols. Gearbox Protocol allows anyone to get leverage in a decentralized way and use it across various other protocols in a composable way: margin trading, leverage farming, and more!",
    "disabled": false,
    "displayName": "Gearbox",
    "enabled": true,
    "gecko_id": "gearbox",
    "governanceID": Array [
      "snapshot:gearbox.eth",
    ],
    "id": "1108",
    "listedAt": 1640629561,
    "logo": "https://icons.llama.fi/gearbox.jpg",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/gearbox",
    "module": "gearbox",
    "name": "Gearbox",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "referralUrl": "https://app.gearbox.fi/?referral=1404",
    "symbol": "GEAR",
    "treasury": "gearbox.js",
    "twitter": "GearboxProtocol",
    "url": "https://gearbox.fi",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_links": Array [
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-Geist-v1.0.pdf",
      "https://solidity.finance/audits/GeistProtocol/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
    ],
    "cmcId": "12576",
    "config": Object {
      "enabled": true,
      "id": "643",
    },
    "description": "Geist is a decentralised non-custodial liquidity market protocol where users can participate as depositors or borrowers.",
    "disabled": false,
    "displayName": "Geist Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Aave",
    ],
    "gecko_id": "geist-finance",
    "id": "643",
    "logo": "https://icons.llama.fi/geist-finance.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/geist-finance",
    "module": "geist-finance",
    "name": "Geist Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "GEIST",
    "twitter": "GeistFinance",
    "url": "https://geist.finance",
  },
  Object {
    "address": "neo:******************************************",
    "allAddresses": Array [
      "neo:******************************************",
      "ethereum:******************************************",
      "polygon:******************************************",
      "avax:******************************************",
      "bsc:******************************************",
    ],
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "NEO",
    "chains": Array [
      "neo",
      "bsc",
      "avax",
      "polygon",
      "ethereum",
      "phantasma",
    ],
    "cmcId": "17454",
    "config": Object {
      "allAddresses": Array [
        "neo:******************************************",
        "ethereum:******************************************",
        "polygon:******************************************",
        "avax:******************************************",
        "bsc:******************************************",
      ],
      "category": "NFT Marketplace",
      "enabled": true,
      "id": "2290",
    },
    "description": "The world’s most powerful cross-chain NFT marketplace. Create, Sell, Discover and Buy BSC, NEO N3, Ethereum, Phantasma, Polygon and Avalanche digital collectibles.",
    "disabled": false,
    "displayName": "GhostMarket",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "ghostmarket",
    "id": "2290",
    "listedAt": 1668700062,
    "logo": "https://icons.llama.fi/ghostmarket.jpg",
    "methodology": Object {
      "Fees": "Users pay 2% fees on each trade",
      "HoldersRevenue": "20% of user fees goes to GFUND single stake pool",
      "ProtocolRevenue": "Protocol gets 2% of each trade",
      "Revenue": "Revenue is 2% of each trade",
      "UserFees": "Users pay 2% fees on each trade",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/ghostmarket",
    "module": "ghostmarket",
    "name": "GhostMarket",
    "protocolType": undefined,
    "symbol": "GM",
    "twitter": "ghostmarketio",
    "url": "https://ghostmarket.io",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 8217,
    "chains": Array [
      "klaytn",
    ],
    "cmcId": "4256",
    "config": Object {
      "enabled": false,
      "id": "4256",
    },
    "disabled": false,
    "displayName": "Klaytn",
    "enabled": false,
    "geckoId": "klay-token",
    "gecko_id": "klay-token",
    "id": "4256",
    "logo": "https://icons.llama.fi/chains/rsz_klaytn.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/klaytn.ts",
    "module": "klaytn",
    "name": "Klaytn",
    "protocolType": "chain",
    "symbol": "KLAY",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/lidofinance/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Liquid Staking",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "8000",
    "config": Object {
      "enabled": true,
      "id": "182",
    },
    "description": "Liquidity for staked assets. Daily rewards, no lock ups. Available for Ethereum, Solana, Polygon, Terra, Kusama & Polkadot.",
    "disabled": false,
    "displayName": "Lido",
    "enabled": true,
    "gecko_id": "lido-dao",
    "governanceID": Array [
      "snapshot:lido-snapshot.eth",
    ],
    "id": "182",
    "logo": "https://icons.llama.fi/lido.png",
    "methodology": Object {
      "Fees": "Staking rewards earned by earned by all staked ETH",
      "ProtocolRevenue": "Lido applies a 10% fee on staking rewards that are split between node operators and the DAO Treasury",
      "Revenue": "Staking rewards",
      "SupplySideRevenue": "Staking rewards earned by stETH holders",
      "UserFees": "Lido takes 10% fee on users staking rewards",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/lido.ts",
    "module": "lido",
    "name": "Lido",
    "openSource": true,
    "protocolType": undefined,
    "referralUrl": "https://stake.lido.fi/?ref=******************************************",
    "symbol": "LDO",
    "treasury": "lido.js",
    "twitter": "LidoFinance",
    "url": "https://lido.fi/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://rugdoc.io/project/liquid-bolt-finance",
      "https://solidity.finance/audits/LiquidBolt",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Yield",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "arbitrum",
      "bsc",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2513",
    },
    "description": "V-farm - 1st volatility yield farming dapp on multiple blockchains.",
    "disabled": false,
    "displayName": "Liquid Bolt",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2513",
    "listedAt": **********,
    "logo": "https://icons.llama.fi/liquid-bolt.jpg",
    "methodology": Object {
      "Fees": "The revenue distributed by the arbitrage robots",
      "HoldersRevenue": "No token yet",
      "ProtocolRevenue": "20% performance fees on the arbitrage revenue",
      "Revenue": "20% performance fees on the arbitrage revenue",
      "SupplySideRevenue": "80% of arbitrage revenue to Liquidity Providers",
      "UserFees": "20% performance fees on the arbitrage revenue",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/liquid-bolt.ts",
    "module": "liquid-bolt",
    "name": "Liquid Bolt",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "LiquidBoltFi",
    "url": "https://app.liquidbolt.finance/farms",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/trailofbits/publications/blob/master/reviews/Liquity.pdf",
      "https://www.coinspect.com/liquity-audit/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "CDP",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "7429",
    "config": Object {
      "enabled": true,
      "id": "270",
    },
    "description": "Liquity is a decentralized borrowing protocol that allows you to draw 0% interest loans against Ether used as collateral.",
    "disabled": false,
    "displayName": "Liquity",
    "enabled": true,
    "gecko_id": "liquity",
    "id": "270",
    "logo": "https://icons.llama.fi/liquity.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Interest paid by borrowers",
      "Revenue": "Interest paid by borrowers",
      "UserFees": "Interest paid to borrow",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/liquity.ts",
    "module": "liquity",
    "name": "Liquity",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "LQTY",
    "treasury": "liquity-treasury.js",
    "twitter": "LiquityProtocol",
    "url": "https://www.liquity.org/",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "litecoin",
    ],
    "cmcId": "2",
    "config": Object {
      "enabled": true,
      "id": "2",
    },
    "disabled": false,
    "displayName": "Litecoin",
    "enabled": true,
    "geckoId": "litecoin",
    "gecko_id": "litecoin",
    "id": "2",
    "logo": "https://icons.llama.fi/chains/rsz_litecoin.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/litecoin.ts",
    "module": "litecoin",
    "name": "Litecoin",
    "protocolType": "chain",
    "symbol": "LTC",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "NFT Lending",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2252",
    },
    "description": "Deposit NFTs and borrow ETH for small illiquid NFT collections that can't get into the main NFT lending markets",
    "disabled": false,
    "displayName": "Llamalend",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2252",
    "listedAt": 1667617275,
    "logo": "https://icons.llama.fi/llamalend.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "HoldersRevenue": "Token holders have no revenue",
      "ProtocolRevenue": "Protocol have no revenue",
      "Revenue": "Governance have no revenue",
      "SupplySideRevenue": "Interest paid to NFTs lenders",
      "UserFees": "Interest paid to borrow ETH",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/llamalend.ts",
    "module": "llamalend",
    "name": "Llamalend",
    "oracles": Array [
      "Internal",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "llamalend",
    "url": "https://llamalend.com/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/trailofbits/publications/blob/master/reviews/LooksRare.pdf",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-LooksRare-v1.0.pdf",
      "https://github.com/peckshield/publications/blob/master/audit_reports/PeckShield-Audit-Report-LooksRare-AggregatorFeeSharing-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "17081",
    "config": Object {
      "enabled": true,
      "id": "1229",
    },
    "description": "LooksRare is the community-first NFT marketplace with rewards for participating. Buy NFTs (or sell 'em) to earn rewards.",
    "disabled": false,
    "displayName": "LooksRare",
    "enabled": true,
    "gecko_id": "looksrare",
    "id": "1229",
    "listedAt": 1641874709,
    "logo": "https://icons.llama.fi/looksrare.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/looksrare.ts",
    "module": "looksrare",
    "name": "LooksRare",
    "protocolType": undefined,
    "symbol": "LOOKS",
    "treasury": "looksrare.js",
    "twitter": "LooksRareNFT",
    "url": "https://looksrare.org",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://security.makerdao.com/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "CDP",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "1518",
    "config": Object {
      "enabled": true,
      "id": "118",
    },
    "description": "Builders of Dai, a digital currency that can be used by anyone, anywhere.
",
    "disabled": false,
    "displayName": "MakerDAO",
    "enabled": true,
    "gecko_id": "maker",
    "id": "118",
    "logo": "https://icons.llama.fi/makerdao.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Interest paid by borrowers",
      "Revenue": "Interest paid by borrowers",
      "UserFees": "Interest paid to borrow",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/makerdao.ts",
    "module": "makerdao",
    "name": "MakerDAO",
    "oracles": Array [
      "Chronicle",
    ],
    "protocolType": undefined,
    "referralUrl": "https://oasis.app/?ref=******************************************",
    "symbol": "MKR",
    "treasury": "maker.js",
    "twitter": "MakerDAO",
    "url": "https://makerdao.com/",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "mixin",
    ],
    "cmcId": "2349",
    "config": Object {
      "enabled": false,
      "id": "2349",
    },
    "disabled": false,
    "displayName": "Mixin",
    "enabled": false,
    "geckoId": "mixin",
    "gecko_id": "mixin",
    "id": "2349",
    "logo": "https://icons.llama.fi/chains/rsz_mixin.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mixin.ts",
    "module": "mixin",
    "name": "Mixin",
    "protocolType": "chain",
    "symbol": "XIN",
  },
  Object {
    "categories": Array [
      "EVM",
      "Parachain",
    ],
    "category": "Chain",
    "chainId": 1284,
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": "6836",
    "config": Object {
      "enabled": true,
      "id": "6836",
    },
    "disabled": false,
    "displayName": "Moonbeam",
    "enabled": true,
    "geckoId": "moonbeam",
    "gecko_id": "moonbeam",
    "id": "6836",
    "logo": "https://icons.llama.fi/chains/rsz_moonbeam.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/moonbeam.ts",
    "module": "moonbeam",
    "name": "Moonbeam",
    "parent": Object {
      "chain": "Polkadot",
      "types": Array [
        "parachain",
      ],
    },
    "protocolType": "chain",
    "symbol": "GLMR",
  },
  Object {
    "categories": Array [
      "EVM",
      "Parachain",
    ],
    "category": "Chain",
    "chainId": 1285,
    "chains": Array [
      "moonriver",
    ],
    "cmcId": "9285",
    "config": Object {
      "enabled": true,
      "id": "9285",
    },
    "disabled": false,
    "displayName": "Moonriver",
    "enabled": true,
    "geckoId": "moonriver",
    "gecko_id": "moonriver",
    "id": "9285",
    "logo": "https://icons.llama.fi/chains/rsz_moonriver.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/moonriver.ts",
    "module": "moonriver",
    "name": "Moonriver",
    "parent": Object {
      "chain": "Kusama",
      "types": Array [
        "parachain",
      ],
    },
    "protocolType": "chain",
    "symbol": "MOVR",
  },
  Object {
    "address": "moonriver:******************************************",
    "audit_links": Array [
      "https://github.com/HalbornSecurity/PublicReports/blob/master/Solidity%20Smart%20Contract%20Audits/Moonwell_Finance_Smart_Contract_Security_Audit_Report_Halborn_Final.pdf",
      "https://github.com/HalbornSecurity/PublicReports/blob/master/Solidity%20Smart%20Contract%20Audits/Moonwell_Finance_Safety_Module_Smart_Contract_Security_Audit_Report_Halborn_Final.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Moonriver",
    "chains": Array [
      "moonriver",
    ],
    "cmcId": "18698",
    "config": Object {
      "enabled": true,
      "id": "1401",
    },
    "description": "Moonwell is an open lending and borrowing DeFi protocol on Moonbeam & Moonriver.",
    "disabled": false,
    "displayName": "Moonwell Apollo",
    "enabled": true,
    "forkedFrom": Array [
      "Compound",
    ],
    "gecko_id": "moonwell",
    "id": "1401",
    "language": "Solidity",
    "listedAt": 1644685001,
    "logo": "https://icons.llama.fi/moonwell-apollo.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/moonwell-apollo.ts",
    "module": "moonwell-apollo",
    "name": "Moonwell Apollo",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Moonwell",
    "protocolType": undefined,
    "symbol": "MFAM",
    "twitter": "MoonwellDeFi",
    "url": "https://moonwell.fi",
  },
  Object {
    "address": "moonbeam:******************************************",
    "audit_links": Array [
      "https://github.com/HalbornSecurity/PublicReports/blob/master/Solidity%20Smart%20Contract%20Audits/Moonwell_Finance_Smart_Contract_Security_Audit_Report_Halborn_Final.pdf",
      "https://github.com/HalbornSecurity/PublicReports/blob/master/Solidity%20Smart%20Contract%20Audits/Moonwell_Finance_Safety_Module_Smart_Contract_Security_Audit_Report_Halborn_Final.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Moonbeam",
    "chains": Array [
      "moonbeam",
    ],
    "cmcId": "20734",
    "config": Object {
      "enabled": true,
      "id": "1853",
    },
    "description": "Moonwell is an open lending and borrowing DeFi protocol on Moonbeam & Moonriver.",
    "disabled": false,
    "displayName": "Moonwell Artemis",
    "enabled": true,
    "forkedFrom": Array [
      "Compound",
    ],
    "gecko_id": "moonwell-artemis",
    "id": "1853",
    "language": "Solidity",
    "listedAt": 1655988523,
    "logo": "https://icons.llama.fi/moonwell-artemis.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/moonwell-artemis.ts",
    "module": "moonwell-artemis",
    "name": "Moonwell Artemis",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "Moonwell",
    "protocolType": undefined,
    "symbol": "WELL",
    "twitter": "MoonwellDeFi",
    "url": "https://moonwell.fi",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://www.certik.com/projects/mux-protocol",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "Derivatives",
    "chain": "Ethereum",
    "chains": Array [
      "arbitrum",
      "bsc",
      "avax",
      "fantom",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2254",
    },
    "description": "Trade crypto on the MUX protocol with zero price impact, up to 100x leverage and no counterparty risks",
    "disabled": false,
    "displayName": "MUX Derivatives",
    "enabled": true,
    "gecko_id": "mcdex",
    "id": "2254",
    "listedAt": 1667699921,
    "logo": "https://icons.llama.fi/mux protocol.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/mux.ts",
    "module": "mux",
    "name": "MUX Derivatives",
    "oracles": Array [
      "Chainlink",
    ],
    "parentProtocol": "MUX Protocol",
    "protocolType": undefined,
    "symbol": "MCB",
    "twitter": "muxprotocol",
    "url": "https://mux.network/",
  },
  Object {
    "address": "******************************************",
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "optimism",
      "arbitrum",
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2546",
    },
    "description": "Buy and Sell NFTs on L2.",
    "disabled": false,
    "displayName": "NFTEarth",
    "enabled": true,
    "gecko_id": "nftearth",
    "id": "2546",
    "logo": "https://icons.llama.fi/nftearth.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/nftearth.ts",
    "module": "nftearth",
    "name": "NFTEarth",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "NFTE",
    "twitter": "NFTEarth_L2",
    "url": "https://nftearth.exchange",
  },
  Object {
    "address": null,
    "audit_links": Array [],
    "audit_note": null,
    "audits": "1",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2258",
      "protocolsData": Object {
        "seaport": Object {
          "displayName": "Opensea Seaport",
          "enabled": true,
          "id": "2258",
        },
        "v1": Object {
          "displayName": "Opensea V1",
          "enabled": true,
          "id": "2630",
        },
        "v2": Object {
          "displayName": "Opensea V2",
          "enabled": true,
          "id": "2631",
        },
      },
    },
    "description": "OpenSea is the world's first and largest web3 marketplace for NFTs and crypto collectibles.",
    "disabled": false,
    "displayName": "Opensea V1",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2258",
    "logo": "https://icons.llama.fi/opensea-v1.png",
    "methodology": Object {
      "CreatorRevenue": "Creators can set up to 10% royalty fees of each sale",
      "Fees": "Service fees and royalties collected",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Opensea takes 2.5% on each sale",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/opensea",
    "module": "opensea",
    "name": "OpenSea V1",
    "parentProtocol": "OpenSea",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "opensea",
    "url": "https://opensea.io/",
    "versionKey": "v1",
  },
  Object {
    "address": null,
    "audit_links": Array [],
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2258",
      "protocolsData": Object {
        "seaport": Object {
          "displayName": "Opensea Seaport",
          "enabled": true,
          "id": "2258",
        },
        "v1": Object {
          "displayName": "Opensea V1",
          "enabled": true,
          "id": "2630",
        },
        "v2": Object {
          "displayName": "Opensea V2",
          "enabled": true,
          "id": "2631",
        },
      },
    },
    "description": "OpenSea is the world's first and largest web3 marketplace for NFTs and crypto collectibles.",
    "disabled": false,
    "displayName": "Opensea V2",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2258",
    "logo": "https://icons.llama.fi/opensea-v2.png",
    "methodology": Object {
      "CreatorRevenue": "Creators can set up to 10% royalty fees of each sale",
      "Fees": "Service fees and royalties collected",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Opensea takes 2.5% on each sale",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/opensea",
    "module": "opensea",
    "name": "OpenSea V2",
    "parentProtocol": "OpenSea",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "opensea",
    "url": "https://opensea.io/",
    "versionKey": "v2",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/trailofbits/publications/blob/master/reviews/SeaportProtocol.pdf",
    ],
    "audit_note": "OpenSea engaged Trail of Bits to audit the security of Seaport. From April 18th to May 12th 2022, a team of Trail of Bits consultants conducted a security review of Seaport. The audit did not uncover significant flaws that could result in the compromise of a smart contract, loss of funds, or unexpected behavior in the target system. Their full report is available here.",
    "audits": "1",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2258",
      "protocolsData": Object {
        "seaport": Object {
          "displayName": "Opensea Seaport",
          "enabled": true,
          "id": "2258",
        },
        "v1": Object {
          "displayName": "Opensea V1",
          "enabled": true,
          "id": "2630",
        },
        "v2": Object {
          "displayName": "Opensea V2",
          "enabled": true,
          "id": "2631",
        },
      },
    },
    "description": "OpenSea is the world's first and largest web3 marketplace for NFTs and crypto collectibles.",
    "disabled": false,
    "displayName": "Opensea Seaport",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2258",
    "listedAt": 1667934393,
    "logo": "https://icons.llama.fi/opensea-seaport.png",
    "methodology": Object {
      "CreatorRevenue": "Creators can set up to 10% royalty fees of each sale",
      "Fees": "Service fees and royalties collected",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Opensea takes 2.5% on each sale",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/opensea",
    "module": "opensea",
    "name": "Opensea Seaport",
    "parentProtocol": "OpenSea",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "opensea",
    "url": "https://opensea.io/",
    "versionKey": "seaport",
  },
  Object {
    "categories": Array [
      "EVM",
      "Rollup",
    ],
    "category": "Rollup",
    "chainId": 10,
    "chains": Array [
      "optimism",
    ],
    "cmcId": "11840",
    "config": Object {
      "category": "Rollup",
      "enabled": true,
      "id": "11840",
    },
    "disabled": false,
    "displayName": "Optimism",
    "enabled": true,
    "geckoId": "optimism",
    "gecko_id": "optimism",
    "id": "11840",
    "logo": "https://icons.llama.fi/chains/rsz_optimism.jpg",
    "methodology": Object {
      "Fees": "Fees collected by sequencer paid by users",
      "ProtocolRevenue": "ETH earned from user fees minus cost to send transactions in L1",
      "Revenue": "ETH earned from user fees minus cost to send transactions in L1",
      "UserFees": "Fees paid by users to sequencer",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/optimism.ts",
    "module": "optimism",
    "name": "Optimism",
    "parent": Object {
      "chain": "Ethereum",
      "types": Array [
        "L2",
        "gas",
      ],
    },
    "protocolType": "chain",
    "symbol": "OP",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://docs.perp88.com/perp88-1/about-perp88/audits-and-contracts",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Polygon",
    "chains": Array [
      "polygon",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2296",
    },
    "description": "Perp88 is a Decentralized Perpetual & Spot Exchange operating on Polygon",
    "disabled": false,
    "displayName": "Perp88",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2296",
    "listedAt": 1668805259,
    "logo": "https://icons.llama.fi/perp88.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/perp88.ts",
    "module": "perp88",
    "name": "Perp88",
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "0xPerp88",
    "url": "https://perp88.com/",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 137,
    "chains": Array [
      "polygon",
    ],
    "cmcId": "3890",
    "config": Object {
      "enabled": true,
      "id": "3890",
    },
    "disabled": false,
    "displayName": "Polygon",
    "enabled": true,
    "geckoId": "matic-network",
    "gecko_id": "matic-network",
    "id": "3890",
    "logo": "https://icons.llama.fi/chains/rsz_polygon.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/polygon.ts",
    "module": "polygon",
    "name": "Polygon",
    "protocolType": "chain",
    "symbol": "MATIC",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://www.linkedin.com/posts/zokyo_predy-smart-contract-audit-reportzokyoio-activity-6914551796023627777-8jWC?utm_source=linkedin_share&utm_medium=member_desktop_web",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Derivatives",
    "chain": "Arbitrum",
    "chains": Array [
      "arbitrum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "1657",
    },
    "description": "The core of Predy v3 is the Tripartite Lending Protocol(TLP) as the middle protocol to handle ETH, USDC, and Uniswap LPT (applicable to any Token pair and its Uniswap LPT) to represent the status of lending as an perpetual option (LP fees = option premiums). Uniswap achieved path independence for total K by using only token-pair quantities, which Predy v3 inherits.  The measurement of the position's value against the debt is the key of lending protocol, and it maintains a positive value with the certain volatility of the underlying asset price, where It's verifiability and finality bring extension that other protocols have not been able to achieve due to external dependencies and administrative privileges.",
    "disabled": false,
    "displayName": "Predy Finance",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "1657",
    "listedAt": 1650190517,
    "logo": "https://icons.llama.fi/predy-finance.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "HoldersRevenue": "Fees going to governance token holders",
      "ProtocolRevenue": "Fees going to treasury",
      "Revenue": "Treasury and token holders revenue",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/predy-finance",
    "module": "predy-finance",
    "name": "Predy Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "predyfinance",
    "url": "https://www.predy.finance",
  },
  Object {
    "category": "Chain",
    "chains": Array [
      "solana",
    ],
    "cmcId": "5426",
    "config": Object {
      "enabled": true,
      "id": "5426",
    },
    "disabled": false,
    "displayName": "Solana",
    "enabled": true,
    "geckoId": "solana",
    "gecko_id": "solana",
    "id": "5426",
    "logo": "https://icons.llama.fi/chains/rsz_solana.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/solana.ts",
    "module": "solana",
    "name": "Solana",
    "protocolType": "chain",
    "symbol": "SOL",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://github.com/stargate-protocol/stargate/tree/main/audit",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Cross Chain",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "avax",
      "polygon",
      "arbitrum",
      "fantom",
      "optimism",
    ],
    "cmcId": "18934",
    "config": Object {
      "enabled": true,
      "id": "1571",
    },
    "description": "Stargate is a fully composable liquidity transport protocol that lives at the heart of Omnichain DeFi. With Stargate, users & dApps can transfer native assets cross-chain while accessing the protocol’s unified liquidity pools with instant guaranteed finality.",
    "disabled": false,
    "displayName": "Stargate",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "stargate-finance",
    "id": "1571",
    "listedAt": 1647975344,
    "logo": "https://icons.llama.fi/stargate.png",
    "methodology": Object {},
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/stargate.ts",
    "module": "stargate",
    "name": "Stargate",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "STG",
    "treasury": "stargate.js",
    "twitter": "StargateFinance",
    "url": "https://stargate.finance/",
  },
  Object {
    "address": null,
    "audit_links": Array [
      "https://github.com/Stride-Labs/audits",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Liquid Staking",
    "chain": "Stride",
    "chains": Array [
      "cosmos",
      "osmosis",
      "juno",
      "stargaze",
      "terra",
      "evmos",
    ],
    "cmcId": "21781",
    "config": Object {
      "enabled": true,
      "id": "2251",
    },
    "description": "Stride is a protocol for multichain liquid staking in the Cosmos ecosystem",
    "disabled": false,
    "displayName": "Stride",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "stride",
    "id": "2251",
    "listedAt": 1667603619,
    "logo": "https://icons.llama.fi/stride.png",
    "methodology": Object {
      "Fees": "Fees are staking rewards earned by tokens staked with Stride. They are measured across Stride's LSD tokens' yields and converted to USD terms.",
      "ProtocolRevenue": "Fee from users rewards",
      "Revenue": "Stride collects 10% of liquid staked assets's staking rewards. These fees are measured across Stride's LSD tokens' yields and converted to USD terms.",
      "SupplySideRevenue": "Revenue earned by stETH holders",
      "UserFees": "Percentage of rewards paid to protocol",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/stride.ts",
    "module": "stride",
    "name": "Stride",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "STRD",
    "twitter": "stride_zone",
    "url": "https://stride.zone/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.synthetix.io/contracts/audits/",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Synthetics",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
      "optimism",
    ],
    "cmcId": "2586",
    "config": Object {
      "enabled": true,
      "id": "115",
    },
    "description": "Synthetix is a derivatives liquidity protocol providing the backbone for derivatives trading in DeFi.",
    "disabled": false,
    "displayName": "Synthetix",
    "enabled": true,
    "gecko_id": "havven",
    "governanceID": Array [
      "snapshot:synthetix-stakers-poll.eth",
    ],
    "id": "115",
    "logo": "https://icons.llama.fi/synthetix.png",
    "methodology": Object {
      "Fees": "Fees generated on each synthetic asset exchange, between 0.1% and 1% (usually 0.3%)",
      "HoldersRevenue": "Fees are granted proportionally to SNX stakers by automatically burning outstanding debt (note: rewards not included here can also be claimed by SNX stakers)",
      "ProtocolRevenue": "Percentage of fees going to treasury",
      "Revenue": "Fees paid by users and awarded to SNX stakers",
      "SupplySideRevenue": "LPs revenue",
      "UserFees": "Users pay between 10-100 bps (0.1%-1%), usually 30 bps, whenever they exchange a synthetic asset (Synth)",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/synthetix.ts",
    "module": "synthetix",
    "name": "Synthetix",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "SNX",
    "treasury": "synthetix.js",
    "twitter": "synthetix_io",
    "url": "https://synthetix.io",
  },
  Object {
    "address": "fantom:******************************************",
    "audit_note": null,
    "audits": "3",
    "category": "Lending",
    "chain": "Fantom",
    "chains": Array [
      "fantom",
      "optimism",
    ],
    "cmcId": "11409",
    "config": Object {
      "enabled": true,
      "id": "434",
    },
    "description": "Protocol users can deposit LP tokens in Tarot Vaults and receive Vault Tokens. The deposited LP tokens are then farmed and earned rewards are automatically converted to additional LP tokens and reinvested.",
    "disabled": false,
    "displayName": "Tarot",
    "enabled": true,
    "forkedFrom": Array [
      "Impermax Finance",
    ],
    "gecko_id": "tarot",
    "id": "434",
    "logo": "https://icons.llama.fi/tarot.jpg",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/tarot.ts",
    "module": "tarot",
    "name": "Tarot",
    "protocolType": undefined,
    "symbol": "TAROT",
    "twitter": "TarotFinance",
    "url": "https://www.tarot.to",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chains": Array [
      "tron",
    ],
    "cmcId": "1958",
    "config": Object {
      "enabled": true,
      "id": "1958",
    },
    "disabled": false,
    "displayName": "Tron",
    "enabled": true,
    "geckoId": "tron",
    "gecko_id": "tron",
    "id": "1958",
    "logo": "https://icons.llama.fi/chains/rsz_tron.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/tron.ts",
    "module": "tron",
    "name": "Tron",
    "protocolType": "chain",
    "symbol": "TRON",
  },
  Object {
    "address": "bsc:******************************************",
    "audit_links": Array [
      "https://github.com/valas-finance/valas-protocol/blob/main/audits/PeckShield-Audit-Report-ValasFinance-v1.0.pdf",
    ],
    "audit_note": null,
    "audits": "2",
    "category": "Lending",
    "chain": "Binance",
    "chains": Array [
      "bsc",
    ],
    "cmcId": "19279",
    "config": Object {
      "enabled": true,
      "id": "1584",
    },
    "description": "Valas is a decentralised non-custodial liquidity market protocol where users can participate as depositors or borrowers.",
    "disabled": false,
    "displayName": "Valas Finance",
    "enabled": true,
    "forkedFrom": Array [
      "Aave",
    ],
    "gecko_id": "valas-finance",
    "id": "1584",
    "listedAt": 1648568438,
    "logo": "https://icons.llama.fi/valas-finance.png",
    "methodology": Object {
      "Fees": "Interest paid by borrowers",
      "ProtocolRevenue": "Percentage of interest going to treasury",
      "Revenue": "Percentage of interest going to treasury",
      "SupplySideRevenue": "Interest paid to lenders",
      "UserFees": "Interest paid by borrowers",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/valas-finance.ts",
    "module": "valas-finance",
    "name": "Valas Finance",
    "oracles": Array [
      "Chainlink",
    ],
    "protocolType": undefined,
    "symbol": "VALAS",
    "twitter": "ValasFinance",
    "url": "https://valasfinance.com/",
  },
  Object {
    "address": "******************************************",
    "audit_links": Array [
      "https://docs.x2y2.io/assets/files/X2Y2_NFT_Lending_Report-7dfe991023b2fbd18c647d384fc333e1.pdf",
    ],
    "audit_note": null,
    "audits": "1",
    "category": "NFT Lending",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": "18106",
    "config": Object {
      "enabled": true,
      "id": "1431",
    },
    "description": "The decentralized NFT marketplace. By the people, for the people.",
    "disabled": false,
    "displayName": "X2Y2",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": "x2y2",
    "id": "1431",
    "listedAt": 1645139307,
    "logo": "https://icons.llama.fi/x2y2.png",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/x2y2.ts",
    "module": "x2y2",
    "name": "X2Y2",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "X2Y2",
    "twitter": "the_x2y2",
    "url": "https://x2y2.io",
  },
  Object {
    "categories": Array [
      "EVM",
    ],
    "category": "Chain",
    "chainId": 100,
    "chains": Array [
      "xdai",
    ],
    "cmcId": "1659",
    "config": Object {
      "enabled": true,
      "id": "1659",
    },
    "disabled": false,
    "displayName": "xDai",
    "enabled": true,
    "geckoId": "gnosis",
    "gecko_id": "gnosis",
    "id": "1659",
    "logo": "https://icons.llama.fi/chains/rsz_xdai.jpg",
    "methodology": Object {
      "Fees": "Gas fees paid by users",
      "Revenue": "Burned coins",
      "UserFees": "Gas fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/xdai.ts",
    "module": "xdai",
    "name": "xDai",
    "protocolType": "chain",
    "symbol": "GNO",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Optimism",
    "chains": Array [
      "optimism",
      "arbitrum",
      "arbitrum_nova",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2532",
    },
    "description": "NFT Marketplace for Optimism and Arbitrum by the team of Apetimism",
    "disabled": false,
    "displayName": "Zonic",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2532",
    "logo": "https://icons.llama.fi/zonic.jpg",
    "methodology": Object {
      "Fees": "Fees paid by users",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace revenue",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/zonic.ts",
    "module": "zonic",
    "name": "Zonic",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ZonicApp",
    "url": "https://zonic.app",
  },
  Object {
    "address": null,
    "audit_note": null,
    "audits": "0",
    "category": "NFT Marketplace",
    "chain": "Ethereum",
    "chains": Array [
      "ethereum",
    ],
    "cmcId": null,
    "config": Object {
      "enabled": true,
      "id": "2610",
    },
    "description": "The NFT Marketplace Protocol",
    "disabled": false,
    "displayName": "ZORA",
    "enabled": true,
    "forkedFrom": Array [],
    "gecko_id": null,
    "id": "2610",
    "listedAt": 1677604484,
    "logo": "https://icons.llama.fi/zora.jpg",
    "methodology": Object {
      "Fees": "All royalties + marketplace + mint fees",
      "ProtocolRevenue": "Marketplace revenue",
      "Revenue": "Marketplace fees + mint fees",
      "UserFees": "Fees paid by users",
    },
    "methodologyURL": "https://github.com/DefiLlama/dimension-adapters/blob/master/fees/zora.ts",
    "module": "zora",
    "name": "ZORA",
    "oracles": Array [],
    "protocolType": undefined,
    "symbol": "-",
    "twitter": "ourZORA",
    "url": "https://market.zora.co",
  },
]
`;
