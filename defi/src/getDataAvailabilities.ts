import { IProtocol, processProtocols, TvlItem } from "./storeGetCharts";
import { successResponse, wrap, IResponse } from "./utils/shared";
import { getChainDisplayName } from "./utils/normalizeChain";
import { chainCoingeckoIds } from "./utils/normalizeChain";
import { _InternalProtocolMetadata } from "./protocols/data";
import { APIGatewayEvent } from "aws-lambda";

interface SumDailyTvls {
  [timestamp: number]: {
    [daProvider: string]: {
      [key: string]: number;
    };
  };
}

interface DAProtocols {
  [timestamp: number]: {
    [daProvider: string]: {
      [protocol: string]: {
        [chain: string]: number;
      };
    };
  };
}

interface Item {
  [key: string]: number;
}

interface IChainByDA {
  [daProvider: string]: Record<string, number>;
}

function sum(
  totalByChain: SumDailyTvls,
  total: SumDailyTvls,
  daProvider: string,
  time: number,
  item: Item = {},
  daProtocolsHistory: DAProtocols,
  protocol: IProtocol,
  targetChain: string
) {
  if (!totalByChain[time]) totalByChain[time] = {};
  if (!total[time]) total[time] = {};
  if (!daProtocolsHistory[time]) daProtocolsHistory[time] = {};

  const dataByChain = totalByChain[time][daProvider] ?? {};
  const data = total[time][daProvider] ?? {};

  if (!daProtocolsHistory[time][daProvider]) {
    daProtocolsHistory[time][daProvider] = {};
  }
  if (!daProtocolsHistory[time][daProvider][protocol.name]) {
    daProtocolsHistory[time][daProvider][protocol.name] = {};
  }

  const isOldTvlRecord = Object.keys(item).filter((item) => !["PK", "SK", "tvl"].includes(item)).length === 0;

  for (const section in item) {
    const sectionSplit = (isOldTvlRecord && section === "tvl" ? protocol.chain : section).split("-");

    if (
      ![
        "SK",
        "PK",
        "tvl",
        "tvlPrev1Week",
        "tvlPrev1Day",
        "tvlPrev1Hour",
        "Stake",
        "oec",
        "treasury_bsc",
        "Earn",
        "eth",
        "WooPP",
        "bscStaking",
        "avaxStaking",
        "pool3",
        "masterchef",
        "staking_eth",
        "staking_bsc",
      ].includes(sectionSplit[0]) &&
      sectionSplit[0] === targetChain
    ) {
      const sectionKey = `${getChainDisplayName(sectionSplit[0], true)}${sectionSplit[1] ? `-${sectionSplit[1]}` : ""}`;
      dataByChain[sectionKey] = (dataByChain[sectionKey] ?? 0) + item[section];

      if (!sectionSplit[1]) {
        data.tvl = (data.tvl ?? 0) + item[section];
      }

      daProtocolsHistory[time][daProvider][protocol.name][sectionKey] =
          (daProtocolsHistory[time][daProvider][protocol.name][sectionKey] ?? 0) + item[section];
    }
  }

  totalByChain[time][daProvider] = dataByChain;
  total[time][daProvider] = data;
}

export async function getDataAvailabilitiesInternal({ ...options }: any = {}) {
  const sumDailyTvls = {} as SumDailyTvls;
  const sumDailyTvlsByChain = {} as SumDailyTvls;
  const daProtocols = {} as DAProtocols;

  // Get all chains that have DA providers
  const chainsByDA: { [daProvider: string]: string[] } = {};
  Object.entries(chainCoingeckoIds).forEach(([chainName, chainData]) => {
    if (chainData.parent && chainData.parent.da) {
      const daProvider = chainData.parent.da;
      if (!chainsByDA[daProvider]) {
        chainsByDA[daProvider] = [];
      }
      chainsByDA[daProvider].push(chainName);
    }
  });

  await processProtocols(
    async (timestamp: number, item: TvlItem, protocol: IProtocol, _protocolMetadata: _InternalProtocolMetadata) => {
      try {
        // Check each chain in the protocol's TVL data
        for (const [section, value] of Object.entries(item)) {
          if (typeof value !== 'number' || isNaN(value)) continue;

          const chainName = section.split('-')[0];
          const normalizedChain = getChainDisplayName(chainName, true);

          // Find which DA provider this chain uses
          for (const [daProvider, chains] of Object.entries(chainsByDA)) {
            const matchingChain = chains.find(chain =>
              getChainDisplayName(chain, true) === normalizedChain
            );

            if (matchingChain) {
              sum(sumDailyTvlsByChain, sumDailyTvls, daProvider, timestamp, item, daProtocols, protocol, chainName);
              break; // Chain can only belong to one DA provider
            }
          }
        }
      } catch (error) {
        console.log(protocol.name, error);
      }
    },
    { includeBridge: false, ...options }
  );

  const timestamps = Object.keys(daProtocols);
  const latestTimestamp = timestamps[timestamps.length - 1];

  const daTVS = latestTimestamp ? daProtocols[parseInt(latestTimestamp)] : {};

  const daTvlByChain = {} as IChainByDA;
  const latestTvlByChainByDA = Object.entries(sumDailyTvlsByChain).slice(-1)[0]?.[1] || {};

  for (const daProvider in latestTvlByChainByDA) {
    const chains = Object.fromEntries(
      Object.entries(latestTvlByChainByDA[daProvider] as [string, number])
        .filter((c) => !c[0].includes("-"))
        .sort((a, b) => (b[1] as number) - (a[1] as number))
    );

    daTvlByChain[daProvider] = chains as Record<string, number>;
  }

  const finalChainsByDA: Record<string, Array<string>> = {};
  for (const daProvider in daTvlByChain) {
    const allChainsWithTvl = Object.entries(daTvlByChain[daProvider])
      .sort((a, b) => b[1] - a[1])
      .map((item) => item[0]);

    finalChainsByDA[daProvider] = allChainsWithTvl;
  }

  return {
    chart: sumDailyTvls,
    chainChart: sumDailyTvlsByChain,
    daTVS: daTVS,
    dataAvailabilities: Object.fromEntries(
      Object.entries(daTVS).map(([daProvider, protocols]) => [
        daProvider,
        Object.keys(protocols)
      ])
    ),
    chainsByDA: finalChainsByDA,
  };
}

const handler = async (_event: APIGatewayEvent): Promise<IResponse> => {
  return successResponse(await getDataAvailabilitiesInternal(), 10 * 60); // 10 mins cache
};

export default wrap(handler);