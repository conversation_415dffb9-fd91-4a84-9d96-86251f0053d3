import { wrapScheduled<PERSON>ambda } from "./utils/shared/wrap";
import invoke<PERSON><PERSON>b<PERSON> from "./utils/shared/invokeLambda";

export const standaloneProtocols: string[] = ["venus"];

const handler = async () => {
  for (let protocol of standaloneProtocols) {
    const event = {
      protocol,
    };
    await invokeLambda(`defillama-prod-fetchLiquidations`, event);
  }
};

export default wrapScheduledLambda(handler);
