import {
  client,
  dateToUNIXTimestamp,
  dailyPrefix,
  TableName,
  getDailyTxs,
} from "./dynamodb";
import { getProtocol } from "./utils";
import { getClosestDayStartTimestamp } from "../utils/date";

const data = [
  [1998754293.55132, "2021-04-16"],
  [2031386975.62554, "2021-04-15"],
  [2056264767.26855, "2021-04-14"],
  [1961219218.94631, "2021-04-13"],
  [1855662105.22551, "2021-04-12"],
  [1770861986.10855, "2021-04-11"],
  [1832078170.53225, "2021-04-10"],
  [1524746052.33952, "2021-04-09"],
  [1482915835.04332, "2021-04-08"],
  [1491952287.36473, "2021-04-07"],
  [1548525052.80919, "2021-04-06"],
  [1530002579.98738, "2021-04-05"],
  [1491548537.25496, "2021-04-04"],
  [1544011427.56639, "2021-04-03"],
  [1611201810.77463, "2021-04-02"],
  [1529933328.67692, "2021-04-01"],
  [1510084657.99022, "2021-03-31"],
  [1546117753.8839, "2021-03-30"],
  [1533313800.89239, "2021-03-29"],
  [1542723914.02454, "2021-03-28"],
  [1503905143.36154, "2021-03-27"],
  [1344957724.4385, "2021-03-26"],
  [1316697741.74678, "2021-03-25"],
  [1368425223.40768, "2021-03-24"],
  [1337040149.50842, "2021-03-23"],
  [1389586754.54817, "2021-03-22"],
  [1367030706.30043, "2021-03-21"],
  [1372646534.37881, "2021-03-20"],
  [1351200944.31219, "2021-03-19"],
  [1370121650.14369, "2021-03-18"],
  [1251355643.19658, "2021-03-17"],
  [1190496699.35431, "2021-03-16"],
  [1270601877.8107, "2021-03-15"],
  [1288258337.74229, "2021-03-14"],
  [1190695076.68849, "2021-03-13"],
  [1102084277.24878, "2021-03-12"],
  [1128105560.3728, "2021-03-11"],
  [1186553401.18073, "2021-03-10"],
  [1161838611.88758, "2021-03-09"],
  [1060652551.3096, "2021-03-08"],
  [1104475363.90991, "2021-03-07"],
  [1160748309.71835, "2021-03-06"],
  [1166310701.74205, "2021-03-05"],
  [1264281913.30007, "2021-03-04"],
  [1286605025.52112, "2021-03-03"],
  [1394440710.74739, "2021-03-02"],
  [1443387385.4843, "2021-03-01"],
  [1294499444.49366, "2021-02-28"],
  [1364589474.54488, "2021-02-27"],
  [1435002753.11122, "2021-02-26"],
  [1563949675.80212, "2021-02-25"],
  [1637605704.57226, "2021-02-24"],
  [1628024051.74952, "2021-02-23"],
  [1745117773.13969, "2021-02-22"],
  [1775149195.05874, "2021-02-21"],
];

async function importData() {
  const id = getProtocol("autofarm").id;
  const PK = `${dailyPrefix}#${id}`;
  const dailyTxs = await getDailyTxs(id);
  for (const d of data) {
    const date = getClosestDayStartTimestamp(
      dateToUNIXTimestamp(d[1] as string)
    );
    console.log(d[1], date);
    for (const tx of dailyTxs || []) {
      if (getClosestDayStartTimestamp(tx.SK) === date) {
        await client
          .delete({
            TableName,
            Key: {
              PK: tx.PK,
              SK: tx.SK,
            },
          })
          .promise();
      }
    }
    await client
      .put({
        TableName,
        Item: {
          PK,
          SK: date,
          tvl: d[0],
        },
      })
      .promise();
  }
}

importData();
