import { batchGet } from "./dynamodb";

test("empty batchGet array", async () => {
    const res = await batchGet([]);
    expect(res).toMatchInlineSnapshot(`Array []`);
});

test("basic batchGet usage", async () => {
    const res = await batchGet([{ PK: "hourlyTvl#1", SK: 1661846366 }, { PK: "hourlyTvl#1", SK: 1661814040 }, { PK: "hourlyRawTokensTvl#266", SK: 1661615940 }]);
    expect(res.length).toEqual(3);
});

jest.setTimeout(20e3)
test("force unprocessed keys in batchGet", async () => {
    const res = await batchGet(setProtocolSKs.map((t) => ({ PK: "hourlyRawTokensTvl#266", SK: t })), 10);
    expect(res.length).toEqual(300);
});

const setProtocolSKs = [
    1661885986, 1661882282, 1661878773, 1661875180, 1661871544, 1661867951, 1661864298, 1661860799, 1661857131,
    1661853587, 1661850008, 1661846366, 1661842741, 1661839213, 1661835554, 1661831975, 1661828367, 1661824783,
    1661821149, 1661817579, 1661813940, 1661810395, 1661806748, 1661803162, 1661799573, 1661795968, 1661792392,
    1661788756, 1661785179, 1661781573, 1661777908, 1661774366, 1661770716, 1661767187, 1661763597, 1661759977,
    1661756269, 1661752756, 1661749158, 1661745572, 1661742001, 1661738401, 1661734700, 1661731135, 1661727570,
    1661723949, 1661720364, 1661716717, 1661713155, 1661709532, 1661705927, 1661702374, 1661698758, 1661695149,
    1661691597, 1661687953, 1661684406, 1661680763, 1661677137, 1661673608, 1661669931, 1661666361, 1661659102,
    1661655575, 1661651985, 1661648339, 1661644811, 1661641149, 1661637597, 1661633978, 1661630376, 1661626751,
    1661623171, 1661619541, 1661615940, 1661612412, 1661608729, 1661605181, 1661601593, 1661597941, 1661594351,
    1661590746, 1661587148, 1661583615, 1661579998, 1661576424, 1661572707, 1661569185, 1661565574, 1661561941,
    1661558426, 1661554730, 1661551132, 1661547558, 1661543981, 1661540358, 1661536809, 1661533147, 1661529528,
    1661525930, 1661522382, 1661518745, 1661515172, 1661511585, 1661508004, 1661504364, 1661500738, 1661497187,
    1661493478, 1661490012, 1661486365, 1661482792, 1661479143, 1661475608, 1661471989, 1661468320, 1661464793,
    1661461155, 1661457570, 1661453998, 1661450346, 1661446778, 1661443086, 1661439515, 1661435960, 1661432346,
    1661428765, 1661425176, 1661421581, 1661417986, 1661414334, 1661410725, 1661407079, 1661403576, 1661399921,
    1661396394, 1661392814, 1661389162, 1661385457, 1661381909, 1661378388, 1661374797, 1661371200, 1661367609,
    1661363973, 1661360395, 1661356741, 1661353098, 1661349646, 1661345928, 1661342393, 1661338805, 1661335151,
    1661331493, 1661327971, 1661324298, 1661320752, 1661317183, 1661313603, 1661309979, 1661306385, 1661302791,
    1661299192, 1661295511, 1661291932, 1661288359, 1661284771, 1661281197, 1661277585, 1661273920, 1661270331,
    1661266784, 1661263203, 1661259577, 1661255966, 1661252171, 1661248781, 1661245139, 1661241519, 1661238002,
    1661234331, 1661230774, 1661227162, 1661223518, 1661219978, 1661216373, 1661212704, 1661209094, 1661205507,
    1661201932, 1661198358, 1661194769, 1661191185, 1661187613, 1661184009, 1661180385, 1661176726, 1661173156,
    1661169603, 1661166006, 1661162347, 1661158726, 1661155133, 1661151525, 1661147957, 1661144383, 1661140757,
    1661137153, 1661133542, 1661129948, 1661126285, 1661122756, 1661119183, 1661115547, 1661111939, 1661108329,
    1661104753, 1661101187, 1661097561, 1661093942, 1661090304, 1661086802, 1661083153, 1661079565, 1661075949,
    1661072328, 1661068735, 1661065220, 1661061581, 1661057880, 1661054358, 1661050728, 1661047157, 1661043510,
    1661039971, 1661036300, 1661032779, 1661029174, 1661025533, 1661021991, 1661018339, 1661014724, 1661011187,
    1661007562, 1661003991, 1661000377, 1660996788, 1660993132, 1660989587, 1660985969, 1660982375, 1660978764,
    1660975179, 1660971498, 1660967964, 1660964379, 1660961097, 1660960718, 1660957152, 1660953563, 1660950006,
    1660946393, 1660942772, 1660939194, 1660935543, 1660931950, 1660928380, 1660924790, 1660921170, 1660917585,
    1660913980, 1660910397, 1660906759, 1660903132, 1660899564, 1660895951, 1660892315, 1660888764, 1660885163,
    1660881607, 1660877994, 1660874311, 1660870759, 1660867112, 1660863552, 1660859936, 1660856316, 1660852764,
    1660849137, 1660845551, 1660841981, 1660838323, 1660834746, 1660831150, 1660827576, 1660823910, 1660820349,
    1660816723, 1660813194, 1660809562,
];

/*
// Test unprocessed keys
await client
      .batchGet({
        RequestItems: {
          ["prod-table"]: {
            Keys: 
              [1661885986,1661882282,1661878773,1661875180,1661871544,1661867951,1661864298,1661860799,1661857131,1661853587,1661850008,1661846366,1661842741,1661839213,1661835554,1661831975,1661828367,1661824783,1661821149,1661817579,1661813940,1661810395,1661806748,1661803162,1661799573,1661795968,1661792392,1661788756,1661785179,1661781573,1661777908,1661774366,1661770716,1661767187,1661763597,1661759977,1661756269,1661752756,1661749158,1661745572,1661742001,1661738401,1661734700,1661731135,1661727570,1661723949,1661720364,1661716717,1661713155,1661709532,1661705927,1661702374,1661698758,1661695149,1661691597,1661687953,1661684406,1661680763,1661677137,1661673608,1661669931,1661666361,1661659102,1661655575,1661651985,1661648339,1661644811,1661641149,1661637597,1661633978,1661630376,1661626751,1661623171,1661619541,1661615940,1661612412,1661608729,1661605181,1661601593,1661597941,1661594351,1661590746,1661587148,1661583615,1661579998,1661576424,1661572707,1661569185,1661565574,1661561941,1661558426,1661554730,1661551132,1661547558,1661543981,1661540358,1661536809,1661533147,1661529528,1661525930,1661522382,1661518745,1661515172,1661511585,1661508004,1661504364,1661500738,1661497187,1661493478,1661490012,1661486365,1661482792,1661479143,1661475608,1661471989,1661468320,1661464793,1661461155,1661457570,1661453998,1661450346,1661446778,1661443086,1661439515,1661435960,1661432346,1661428765,1661425176,1661421581,1661417986,1661414334,1661410725,1661407079,1661403576,1661399921,1661396394,1661392814,1661389162,1661385457,1661381909,1661378388,1661374797,1661371200,1661367609,1661363973,1661360395,1661356741,1661353098,1661349646,1661345928,1661342393,1661338805,1661335151,1661331493,1661327971,1661324298,1661320752,1661317183,1661313603,1661309979,1661306385,1661302791,1661299192,1661295511,1661291932,1661288359,1661284771,1661281197,1661277585,1661273920,1661270331,1661266784,1661263203,1661259577,1661255966,1661252171,1661248781,1661245139,1661241519,1661238002,1661234331,1661230774,1661227162,1661223518,1661219978,1661216373,1661212704,1661209094,1661205507,1661201932,1661198358,1661194769,1661191185,1661187613,1661184009,1661180385,1661176726,1661173156,1661169603,1661166006,1661162347,1661158726,1661155133,1661151525,1661147957,1661144383,1661140757,1661137153,1661133542,1661129948,1661126285,1661122756,1661119183,1661115547,1661111939,1661108329,1661104753,1661101187,1661097561,1661093942,1661090304,1661086802,1661083153,1661079565,1661075949,1661072328,1661068735,1661065220,1661061581,1661057880,1661054358,1661050728,1661047157,1661043510,1661039971,1661036300,1661032779,1661029174,1661025533,1661021991,1661018339,1661014724,1661011187,1661007562,1661003991,1661000377,1660996788,1660993132,1660989587,1660985969,1660982375,1660978764,1660975179,1660971498,1660967964,1660964379,1660961097,1660960718,1660957152,1660953563,1660950006,1660946393,1660942772,1660939194,1660935543,1660931950,1660928380,1660924790,1660921170,1660917585,1660913980,1660910397,1660906759,1660903132,1660899564,1660895951,1660892315,1660888764,1660885163,1660881607,1660877994,1660874311,1660870759,1660867112,1660863552,1660859936,1660856316,1660852764,1660849137,1660845551,1660841981,1660838323,1660834746,1660831150,1660827576,1660823910,1660820349,1660816723,1660813194,1660809562]
              .slice(0,100).map(t=>({PK:"hourlyRawTokensTvl#266", SK:t}))
          }
        }
      })
      .promise().then(k=>console.log(k.UnprocessedKeys!['prod-table']))
*/
