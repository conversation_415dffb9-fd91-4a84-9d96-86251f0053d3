// Simple compilation test for getDataAvailabilities
import { chainCoingeckoIds } from "./utils/normalizeChain";

describe("getDataAvailabilities compilation", () => {
  test("should compile and have basic structure", () => {
    // Test that we can import the chainCoingeckoIds
    expect(chainCoingeckoIds).toBeDefined();

    // Test that we can find chains with DA attributes
    const chainsWithDA = Object.entries(chainCoingeckoIds).filter(
      ([_, chainData]) => chainData.parent && chainData.parent.da
    );

    expect(chainsWithDA.length).toBeGreaterThan(0);

    // Group by DA provider
    const daProviders = new Set();
    chainsWithDA.forEach(([_, chainData]) => {
      if (chainData.parent && chainData.parent.da) {
        daProviders.add(chainData.parent.da);
      }
    });

    console.log("DA Providers found:", Array.from(daProviders).sort());
    console.log("Total chains with DA:", chainsWithDA.length);

    // Check that we have expected DA providers
    expect(daProviders.has("Ethereum")).toBe(true);
    expect(daProviders.has("Celestia")).toBe(true);
  });
});