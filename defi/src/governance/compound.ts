import { getCompound, setCompound, getCompoundOverview, setCompoundOverview, } from './cache'
import { CompoundProposal, } from './types'
import { getLogs, } from './getLogs'
import { updateStats, toHex, getGovernanceSources, getChainNameFromId } from './utils'
import * as sdk from '@defillama/sdk'
import { sliceIntoChunks } from '@defillama/sdk/build/util/index'
import { getProvider } from '@defillama/sdk/build/general'
import { NNS_GOV_ID, addICPProposals } from './icp/nns'
import { SNS_GOV_ID, addSNSProposals } from './icp/sns'
import { addTaggrProposals, TAGGR_ID } from './icp/taggr'

const PROPOSAL_STATES = ['Pending', 'Active', 'Canceled', 'Defeated', 'Succeeded', 'Queued', 'Expired', 'Executed']

// these are moved to tally
const blacklisted = [
  'icp',
  'rrkah-fqaaa-aaaaa-aaaaq-cai',
  'ethereum:******************************************',
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
  "ethereum:******************************************",
]

const missing = [
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:10:******************************************',
  'eip155:1:******************************************',
  'eip155:137:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:137:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:137:******************************************',
  'eip155:137:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
  'eip155:1:******************************************',
]

missing.forEach((i: any) => blacklisted.push('ethereum:' + i.split(':')[2].toLowerCase()))

export function getCompoundIds(existingIds: string[]) {
  let compoundIds = new Set(existingIds.map(i => i.toLowerCase()))
  const addCompound = (i: any) => i.governanceID?.filter((j: any) => j.startsWith('compound:')).forEach((j: any) => compoundIds.add(j.toLowerCase()))
  getGovernanceSources().map(addCompound)
  return [...compoundIds].map(i => i.replace('compound:', ''))
}

export async function updateCompounds() {
  const overview: any = await getCompoundOverview()

  blacklisted.forEach((i: any) => delete overview[i])
  const compoundIds = getCompoundIds(Object.keys(overview))
  // const compoundIds = ['ethereum:******************************************']
  const idChunks = sliceIntoChunks(compoundIds, 8)

  for (const ids of idChunks) {
    await Promise.all(ids.map(updateCache))
  }

  const fns = [addICPProposals, addSNSProposals, addTaggrProposals, setCompoundOverview]

  for (const fn of fns) {
    try {
      await fn(overview)
    } catch (e) {
      console.error(e)
    }
  }

  async function updateCache(id: string) {
    if (id === NNS_GOV_ID || id === TAGGR_ID) return;
    if (id.startsWith(SNS_GOV_ID)) return;

    const [chain, address] = id.split(':')
    const cache = await getCompound(id)
    if (!cache.metadata) cache.metadata = { id, chain, address }
    if (!cache.proposals) cache.proposals = {}
    const timestamp = Math.floor(Date.now() / 1e3)
    const api = new sdk.ChainApi({ chain, timestamp, })
    let currentBlock = 0
    try {
      currentBlock = await api.getBlock()
    } catch (e) {
      console.error('failed to get current block', e, chain)
    }
    cache.id = id
    const provider = getProvider(chain)

    try {
      await updateMetadata()
      cache.metadata.network = '' + api.chainId
      cache.metadata.chainName = getChainNameFromId(cache.metadata.network)
      const logMap = await getProposalLogs()

      const missingIds: string[] = []

      Object.keys(logMap).forEach((id: string) => {
        const proposal = cache.proposals[id]
        if (!proposal) {
          missingIds.push(id)
        } else {
          const isProposalClosed = ['Canceled', 'Defeated', 'Expired', 'Executed'].includes(proposal.state)
          if (!isProposalClosed)
            missingIds.push(id)
        }
      })

      await updateProposals(missingIds)

      cache.metadata.proposalsCount = Object.values(cache.proposals).length
      fixDecimals()
      updateStats(cache, overview, cache.id)
      return setCompound(cache.id, cache)

      async function updateMetadata() {
        const metadata = cache.metadata
        const target = address
        let name = ''
        try {
          name = await api.call({ target, abi: 'string:name' })
        } catch (error) { }

        metadata.name = name
        metadata.strategies = [
          {
            "name": "erc20-balance-of",
            "network": api.chainId,
          }
        ]
        metadata.network = api.chainId
        metadata.quorumVotes = 0
      }

      async function updateProposals(ids: string[]) {
        let abi = proposalAbi

        if (address.toLowerCase() === '0x690e775361ad66d1c4a25d89da9fcd639f5198ed') {
          abi = radicleAbi
          cache.name = 'Radicle Governor'
          cache.metadata.name = cache.name
        }

        if (ids.length) {

          // If not V2, maybe it is V1
          try {
            const quorumVotes = await api.call({ target: address, abi: 'uint256:quorumVotes' })
            cache.metadata.quorumVotes = +quorumVotes
          } catch (error) {
            abi = proposalAbiV1
          }


          // Maybe it is Nouns
          try {
            await api.call({ abi, target: address, params: ids[0] })
          } catch (error) {
            abi = proposalAbiNouns
          }

          // Maybe it is quirky V2
          try {
            await api.call({ abi, target: address, params: ids[0] })
          } catch (e) {
            sdk.log('Failed to get proposal data, trying abiV2 alternative for:', cache.metadata.name)
            abi = proposalAbi2
          }

          try {
            await api.call({ abi, target: address, params: ids[0] })
          } catch (error) {
            sdk.log('I give up', id, cache.metadata.name)
            throw error
          }
        }

        let proposalData = await api.multiCall({ abi, target: address, calls: ids })
        let states = await api.multiCall({ abi: 'function state(uint256) view returns (uint8)', target: address, calls: ids })
        states.forEach((state, i) => proposalData[i].state = PROPOSAL_STATES[state])
        return Promise.all(ids.map((v, i) => updateProposal(v, proposalData[i])))
      }

      async function updateProposal(id: string, data: any) {
        const {
          startBlock = Number(logMap[id].startBlock),
          endBlock = Number(logMap[id].endBlock),
          proposer = logMap[id].proposer,
          forVotes,
          againstVotes,
          abstainVotes = 0,
          canceled,
          executed,
          eta = 0,
          state,
        } = data
        let start = data.start ?? 0
        let end = data.end ?? 0

        try {
          if (!start && startBlock !== 0 && !isNaN(startBlock) && api.chain !== 'rsk')
            start = (await provider.getBlock(startBlock))?.timestamp ?? 0

          if (!end && endBlock !== 0 && !isNaN(endBlock) && (currentBlock > 0 && endBlock < currentBlock) && api.chain !== 'rsk')
            end = (await provider.getBlock(endBlock))?.timestamp ?? 0
        } catch (e) {
          console.error('error while fetching timestamp info', startBlock, endBlock, api.chain, e)
        }

        const scores = [+forVotes, +againstVotes, +abstainVotes,]
        const scores_total = scores.reduce((acc, i) => acc + i, 0)

        const description = (logMap[id].description ?? '').trim()
        let title = description.includes('\n') ? description.split('\n')[0] : description
        if (title.length > 200) title = title.slice(0, 197) + '...'
        title = title.replace(/^#/, '').trim()


        let proposal: CompoundProposal = {
          id, start, end, startBlock, endBlock, canceled, executed, eta,
          title, state, scores, scores_total, description,
          author: proposer,
          choices: ['Yes', 'No', 'Abstain'],
          network: api.chainId + '',
          app: 'compound',
          // votes,
          space: {
            id: cache.metadata.id,
          },
          quorum: cache.metadata.quorumVotes ?? 0,
          votes: 0,
          score_skew: 0,
          score_curve: 0,
          score_curve2: 0,
        }

        cache.proposals[id] = proposal
      }

      async function getProposalLogs() {
        const fromBlocks: any = {
          ethereum: 12006099, // the deployment block of compound
          rsk: 3100000, // the deployment block of sovryn
        }
        const logs = await getLogs({
          api,
          onlyArgs: true,
          target: address,
          fromBlock: fromBlocks[chain],
          topics: ['0x7d84a6263ae0d98d3329bd7b46bb4e8d6f98cd35a7adb45c274c8b7fd5ebd5e0'],
          eventAbi: 'event ProposalCreated(uint256 id, address proposer, address[] targets, uint256[] values, string[] signatures, bytes[] calldatas, uint256 startBlock, uint256 endBlock, string description)',
        })
        const logMap: any = {}
        // For v1, take start block and end block from HeadBucketRequestFilterSensitiveLog, use the getVotes and getState call to fetch the rest: https://etherscan.io/address/******************************************#readProxyContract 
        logs.forEach(({ id, description, startBlock, endBlock, proposer, }: any) => {
          logMap['' + id] = { description, startBlock, endBlock, proposer, }
        })
        sdk.log(cache.metadata.name, 'fetched logs#', logs.length)
        return logMap
      }

      function fixDecimals() {
        let divider = 1e18

        if (["ethereum:******************************************",
          "ethereum:******************************************",
          "ethereum:******************************************",
          "ethereum:******************************************",
          "ethereum:******************************************",
          "ethereum:******************************************",
          "ethereum:******************************************",].includes(cache.id)) {
          divider = 1
        } else if (["ethereum:******************************************"].includes(cache.id)) {
          divider = 1e8
        }

        Object.values(cache.proposals).forEach((i: any) => {
          if (i.scores_total > divider) {
            i.scores = i.scores.map((j: any) => j / divider)
            i.scores_total /= divider
            i.quorum /= divider
          }
        })
      }
    } catch (e) {
      console.log(`
        ---------------
        ######### ERROR: failed for Id: ${id} Name: ${cache.metadata.name}
      `)
      console.error(e)
      console.log(`      ---------------      `)
    }
  }
}

const proposalAbiV1 = "function proposalVotes(uint256) view returns (uint256 againstVotes, uint256 forVotes, uint256 abstainVotes)"
const proposalAbi = "function proposals(uint256) view returns (uint256 id, address proposer, uint256 eta, uint256 startBlock, uint256 endBlock, uint256 forVotes, uint256 againstVotes, uint256 abstainVotes, bool canceled, bool executed)"
const proposalAbi2 = "function proposals(uint256) view returns (uint256 id, address proposer, uint256 eta, uint256 startBlock, uint256 endBlock, uint256 forVotes, uint256 againstVotes, bool canceled, bool executed)"
const radicleAbi = "function proposals(uint256) view returns (address proposer, uint256 eta, uint256 startBlock, uint256 endBlock, uint256 forVotes, uint256 againstVotes, bool canceled, bool executed)"
const proposalAbiNouns = "function proposals(uint256 proposalId) view returns (tuple(uint256 id, address proposer, uint256 proposalThreshold, uint256 quorumVotes, uint256 eta, uint256 startBlock, uint256 endBlock, uint256 forVotes, uint256 againstVotes, uint256 abstainVotes, bool canceled, bool vetoed, bool executed, uint256 totalSupply, uint256 creationBlock))"
