import { call } from "@defillama/sdk/build/abi/abi2";
import { Chain } from "@defillama/sdk/build/general";
import { Address } from "@defillama/sdk/build/types";
import axios from "axios";
import nodefetch from "node-fetch";
import sleep from "../../src/utils/shared/sleep";

async function fetch(url: string, i: number = 3) {
  if (i == 0) return await nodefetch(url).then((r) => r.json());
  try {
    return await nodefetch(url).then((r) => r.json());
  } catch {
    await sleep(Math.random() * 5000 + 3000);
    return fetch(url, i - 1);
  }
}

let addresses: { [chain: Chain]: Address[] } = {};
export const arbitrum = async (): Promise<Address[]> => {
  if (addresses.arbitrum) return addresses.arbitrum;
  const data = await fetch("https://bridge.arbitrum.io/token-list-42161.json");
  addresses.arbitrum = data.tokens.map((token: any) => token.address.toLowerCase());
  return addresses.arbitrum;
};
export const nova = async (): Promise<Address[]> => {
  if (addresses.nova) return addresses.nova;
  const data = await fetch("https://tokenlist.arbitrum.io/ArbTokenLists/42170_arbed_uniswap_labs_default.json");
  addresses.nova = data.tokens.map((token: any) => token.address.toLowerCase());
  return addresses.nova;
};
export const mantle = async (): Promise<Address[]> => {
  if (addresses.mantle) return addresses.mantle;
  const data = await fetch(
    "https://raw.githubusercontent.com/mantlenetworkio/mantle-token-lists/main/mantle.tokenlist.json"
  );
  addresses.mantle = data.tokens.filter((t: any) => t.chainId == 5000).map((t: any) => t.address.toLowerCase());
  return addresses.mantle;
};
export const avax = async (): Promise<Address[]> => {
  if (addresses.avax) return addresses.avax;
  const oldData = await fetch("https://raw.githubusercontent.com/0xngmi/bridge-tokens/main/data/penultimate.json");
  const oldTokens = oldData.map((t: any) => t["Avalanche Token Address"].toLowerCase());
  const newData = await fetch(
    "https://raw.githubusercontent.com/ava-labs/avalanche-bridge-resources/main/avalanche_contract_address.json"
  );
  const newTokens = Object.values(newData).map((t: any) => t.toLowerCase());
  addresses.avax = [...oldTokens, ...newTokens];
  return addresses.avax;
};
export const base = async (): Promise<Address[]> => {
  if (addresses.base) return addresses.base;
  const data = await fetch("https://static.optimism.io/optimism.tokenlist.json");
  const baseData = data.tokens.filter((t: any) => t.chainId === 8453);
  addresses.base = baseData.map((t: any) => t.address.toLowerCase());
  return addresses.base;
};
export const linea = async (): Promise<Address[]> => {
  if (addresses.linea) return addresses.linea;
  const data = await fetch(
    "https://raw.githubusercontent.com/Consensys/linea-token-list/main/json/linea-mainnet-token-shortlist.json"
  );
  addresses.linea = data.tokens.map((t: any) => t.address.toLowerCase());
  return addresses.linea;
};
export const metis = async (): Promise<Address[]> => {
  return [
    "******************************************", // METIS
    "******************************************", // ETH
    "******************************************", // WBTC
    "******************************************", // USDC
    "******************************************", // USDT
    "0x0x4c078361FC9BbB78DF910800A991C7c3DD2F6ce0", // DAI
    "******************************************", // BUSD
    "******************************************", // AAVE
    "******************************************", // LINK
    "******************************************", // SUSHI
    "******************************************", // SUSHI
    "******************************************", // UNI
    "******************************************", // CRV
    "******************************************", // WOW
  ];
};
export const optimism = async (): Promise<Address[]> => {
  if (addresses.optimism) return addresses.optimism;
  const data = await fetch("https://static.optimism.io/optimism.tokenlist.json");
  const baseData = data.tokens.filter((t: any) => t.chainId === 10);
  addresses.optimism = baseData.map((t: any) => t.address.toLowerCase());
  return addresses.optimism;
};
export const polygon_zkevm = async (): Promise<Address[]> => {
  if (addresses.polygon_zkevm) return addresses.polygon_zkevm;

  const tokens = [
    "******************************************", // matic
    "******************************************", // usdt
    "******************************************", // usdc
    "******************************************", // dai
    "******************************************", // wbtc
  ];

  addresses.polygon_zkevm = [];
  await Promise.all(
    tokens.map(async (token) => {
      const [name, symbol, decimals] = await Promise.all(
        ["string:name", "string:symbol", "uint8:decimals"].map((abi) => call({ abi, target: token }))
      );
      const wrapperAddress = await call({
        target: "******************************************",
        abi: "function precalculatedWrapperAddress(uint32 originNetwork,address originTokenAddress,string calldata name,string calldata symbol,uint8 decimals) external view returns (address)",
        params: [0, token, name, symbol, decimals],
        chain: "polygon_zkevm",
      });
      if (wrapperAddress) addresses.polygon_zkevm.push(wrapperAddress.toLowerCase());
    })
  );

  return addresses.polygon_zkevm;
};
export const scroll = async (): Promise<Address[]> => {
  if (addresses.scroll) return addresses.scroll;
  const data = await fetch("https://scroll-tech.github.io/token-list/scroll.tokenlist.json");
  const baseData = data.tokens.filter((t: any) => t.chainId === 534352);
  addresses.scroll = baseData.map((t: any) => t.address.toLowerCase());
  return addresses.scroll;
};
export const starknet = async (): Promise<Address[]> => {
  if (addresses.starknet) return addresses.starknet;
  const data = await fetch(
    "https://raw.githubusercontent.com/starknet-io/starknet-addresses/master/bridged_tokens/mainnet.json"
  );
  addresses.starknet = data.map((t: any) => t.l2_token_address?.toLowerCase()).filter((t: any) => t != null);
  return addresses.starknet;
};
export const era = async (): Promise<Address[]> => {
  if (addresses.era) return addresses.era;
  const {
    data: { result: data },
  } = await axios.post("https://mainnet.era.zksync.io", {
    method: "zks_getConfirmedTokens",
    params: [0, 255],
    id: 1,
    jsonrpc: "2.0",
  });
  addresses.era = data.map((d: any) => d.l2Address.toLowerCase());
  return addresses.era;
};
export const tron = async (): Promise<Address[]> => {
  if (!("tron" in addresses)) addresses.tron = [];
  addresses.tron.push(
    ...[
      "TN3W4H6rK2ce4vX9YnFQHwKENnHjoxb3m9", // BTC
      "THb4CqiFdwNHsWsQCs4JhzwjMWys4aqCbF", // ETHold
      "TR3DLthpnDdCGabhVDbD3VMsiJoCXY3bZd", // LTC
      "THbVQp8kMjStKNnf2iCY6NEzThKMK5aBHg", // DOGE
    ]
  );
  return addresses.tron;
};
export const blast = async (): Promise<Address[]> => {
  if (!("blast" in addresses)) addresses.blast = [];
  addresses.blast.push(
    ...[
      "******************************************", // WETH
    ]
  );
  return addresses.blast;
};
export const mode = async (): Promise<Address[]> => {
  if (!("mode" in addresses)) addresses.mode = [];
  addresses.mode.push(
    ...[
      "******************************************", // WETH
      "******************************************", // WBTC
      "******************************************", // USDC
      "******************************************", // USDT
    ]
  );
  return addresses.mode;
};
export const zklink = async (): Promise<Address[]> => {
  if (addresses.zklink) return addresses.zklink;
  const allTokens = [];
  let page = 1;
  do {
    const { items, meta } = await fetch(`https://explorer-api.zklink.io/tokens?limit=200&page=${page}&key=`);
    allTokens.push(...items);
    page++;
    if (page >= meta.totalPages) break;
  } while (page < 100);
  addresses.zklink = allTokens.map((d: any) => d.l2Address.toLowerCase());
  return addresses.zklink;
};
export const manta = async (): Promise<Address[]> => {
  if (addresses.manta) return addresses.manta;
  const bridge = (
    await fetch(
      "https://raw.githubusercontent.com/Manta-Network/manta-pacific-token-list/main/json/manta-pacific-mainnet-token-list.json"
    )
  ).tokens as any[];

  addresses.manta = bridge
    .filter((token) => token.chainId === 169 && token.tokenType.includes("canonical-bridge"))
    .map((optToken) => optToken.address.toLowerCase());
  return addresses.manta;
};
export const osmosis = async (): Promise<Address[]> => {
  if (addresses.osmosis) return addresses.osmosis;
  const res = await fetch(
    `https://raw.githubusercontent.com/osmosis-labs/assetlists/main/osmosis-1/generated/chain_registry/assetlist.json`
  );

  addresses.osmosis = [];
  res.assets.map((c: any) => {
    const { base: address } = c;
    if (!address.startsWith("ibc/")) return;
    // const decimals = c.denom_units.find((d: any) => d.denom == display)?.exponent;
    addresses.osmosis.push(address);
  });

  return addresses.osmosis;
};
export const aptos = async (): Promise<Address[]> => {
  if (addresses.aptos) return addresses.aptos;
  const res: { tokenAddress: string; bridge: string }[] = await fetch(
    "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/refs/heads/main/token-list.json"
  );

  addresses.aptos = [];
  res.map(({ tokenAddress, bridge }) => {
    if (!bridge) return;
    addresses.aptos.push(tokenAddress);
  });

  return addresses.aptos;
};
export const eclipse = async (): Promise<Address[]> => {
  if (addresses.eclipse) return addresses.eclipse;
  const res: { tokenAddress: string; bridge: string }[] = await fetch(
    "https://raw.githubusercontent.com/Eclipse-Laboratories-Inc/gist/refs/heads/main/hyperlane-assets.json"
  );

  addresses.eclipse = [];
  res.map(({ address }: any) => addresses.eclipse.push(address));
  return addresses.eclipse;
};
export const flow = async (): Promise<Address[]> => {
  if (addresses.flow) return addresses.flow;
  const res: { tokens: { address: string; tags: string[] }[] } = await fetch(
    "https://raw.githubusercontent.com/onflow/assets/refs/heads/main/tokens/outputs/mainnet/token-list.json"
  );

  addresses.flow = [];
  res.tokens.map(({ address, tags }: any) => {
    if (!tags.includes("bridged-coin")) addresses.flow.push(address);
  });

  return addresses.flow;
};
