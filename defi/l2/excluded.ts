import { getPrices } from "./utils";
import { excludedTvlId } from "./constants";
import { multiCall } from "@defillama/sdk/build/abi/abi2";
import { getBlock } from "@defillama/sdk/build/util/blocks";

const excludedTokensAndOwners: { [chain: string]: [string, string][] } = {
  base: [
    ["******************************************", "******************************************"], // DEGEN
    ["******************************************", "******************************************"], // DEGEN
    ["******************************************", "******************************************"], // DEGEN
    ["******************************************", "******************************************"], // DEGEN
  ],
  hyperliquid: [
    ["******************************************", "******************************************"], // UBTC
    ["******************************************", "******************************************"], // UETH
    ['******************************************', '******************************************'], // USOL
  ],
};

export async function getExcludedTvl(timestamp: number) {
  const TvlRecord: any = {
    SK: timestamp,
    tvl: {},
    PK: `hourlyUsdTokensTvl#${excludedTvlId}`,
  };

  await Promise.all(
    Object.keys(excludedTokensAndOwners).map(async (chain: string) => {
      const uniqueTokens = [...new Set(excludedTokensAndOwners[chain].map(([token]) => token))];

      const block = await getBlock(chain, timestamp);

      const [balances, symbols, decimals, prices] = await Promise.all([
        multiCall({
          abi: "erc20:balanceOf",
          calls: excludedTokensAndOwners[chain].map(([token, owner]) => ({ target: token, params: [owner] })),
          chain,
          block: block.number,
        }),
        multiCall({
          abi: "erc20:symbol",
          calls: uniqueTokens.map((target) => ({ target })),
          chain,
          block: block.number,
          withMetadata: true,
        }),
        multiCall({
          abi: "erc20:decimals",
          calls: uniqueTokens.map((target) => ({ target })),
          chain,
          block: block.number,
          withMetadata: true,
        }),
        getPrices(
          uniqueTokens.map((token) => `${chain}:${token}`),
          timestamp
        ),
      ]);

      if (!TvlRecord[chain]) TvlRecord[chain] = {};

      excludedTokensAndOwners[chain].map(([token], i) => {
        const symbol = symbols.find((s: any) => s.input.target == token)?.output;
        const decimal = decimals.find((s: any) => s.input.target == token)?.output;
        const price = prices[`${chain}:${token}`];
        if (!symbol || !decimal || !price) return;

        const usdValue = (balances[i] / 10 ** decimal) * price.price;

        if (!TvlRecord[chain][symbol]) TvlRecord[chain][symbol] = 0;
        TvlRecord[chain][symbol] += usdValue;
      });
    })
  );

  return TvlRecord;
}
