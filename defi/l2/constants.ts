import { Chain } from "@defillama/sdk/build/general";
import BigNumber from "bignumber.js";
import { ChainData } from "./types";
import cgSymbols from "../src/utils/symbols/symbols.json";

export const zero = BigNumber(0);
export const excludedTvlKeys = ["PK", "SK", "tvl"];
export const excludedTvlId = "5897";

let notifs: number = 0;
export const fetchNotifsSent = () => (notifs += 1);

export const geckoSymbols = cgSymbols as { [key: string]: string };

export const chainsWithoutCanonicalBridges: string[] = [
  "cronos",
  "kava",
  "bsc",
  "tron",
  "ethereum",
  "solana",
  // "sui",
  "aptos",
  // "stacks",
  "bitcoin",
  // "fantom",
  // "filecoin",
  // "near",
  // "aurora",
  "berachain",
  "flow",
];

export const canonicalBridgeIds: { [id: string]: Chain } = {
  "129": "xdai",
  "240": "polygon",
  // "295": "Stacks",
  "412": "thorchain",
  "349": "injective",
  // "801": "celo",
  "1272": "iotex",
  // "1501": "everscale",
  // "2081": "wanchain",
  // "2214": "kekchain",
  "2316": "meter",
  "3699": "elysium",
  "3777": "arbitrum",
  "3778": "nova",
  "3779": "avax",
  "3780": "base",
  "3781": "linea",
  "3782": "mantle",
  "3783": "metis",
  "3784": "optimism",
  "3785": "polygon_zkevm",
  "3786": "scroll",
  "3787": "starknet",
  "3788": "era",
  "3813": "alephium",
  "3861": "rsk",
  // "3866": "near",
  // "3866": "aurora",
  "3935": "boba",
  "3936": "zksync",
  "4032": "manta",
  "4141": "BSquared",
  "4236": "blast",
  "4237": "mode",
  "4335": "zklink",
  "4336": "kinto",
  "4384": "rss3_vsl",
  "4438": "degen",
  "4439": "pulsechain",
  "4440": "ronin",
  "4690": "lorenzo",
  "4692": "taiko",
  "4558": "BOB",
  "4796": "sanko",
  "4797": "xai",
  "4124": "merlin",
  "4937": "reya",
  // "5011": "lisk l2",
  "5055": "osmosis",
  "5011": "lisk",
  "5232": "sui",
  "5218": "mint",
  "5248": "fuel",
  "5303": "morph",
  "4481": "hyperliquid",
  "5375": "zircuit",
  "5382": "core",
  "5461": "apechain",
  "5513": "ink",
  "5538": "swellchain",
  "5552": "cronos_zkevm",
  "5564": "eclipse",
  "5565": "shape",
  "5566": "zora",
  "5609": "sophon",
  "5624": "soneium",
  "5683": "sonic",
  "5691": "abstract",
  "5692": "ancient8",
  "5693": "cyber",
  "5694": "fraxtal",
  "5695": "gravity",
  "5696": "karak",
  "5697": "kroma",
  "5698": "orderly",
  "5699": "rari",
  "5700": "redstone",
  "5701": "wc",
  "5702": "zero_network",
  // "5703": "zkfair",
  "5732": "sxr",
  "5735": "sorare",
  "5772": "unichain",
  // "5833": "formnetwork",
  "5854": "hemi-l2",
  "6063": "mxczkevm",
  "6148": "lens",
  "6149": "openzk",
  "6150": "treasure",
  "6151": "zkcandy",
  "6284": "ao",
  "6424": "soon",
  "6468": "btnx",
  "6498": "eventum",
  "6581": "eni",
};

export const protocolBridgeIds: { [chain: string]: Chain } = {
  "144": "dydx",
  "3139": "immutablex",
  "126": "loopring",
  "1878": "apex",
  "344": "zkswap",
  "5130": "polynomial",
  // "5323": "exSat",
  "4947": "ignition-fbtc",
  "4702": "immutable zkevm",
  "6401": "embr",
  "6414": "xion",
  "6438": "echelon_initia",
  "6439": "inertia",
  "6440": "milkyway_rollup",
};

export const allChainKeys: string[] = [
  ...chainsWithoutCanonicalBridges,
  ...Object.values(canonicalBridgeIds),
  ...Object.values(protocolBridgeIds),
];

export const tokenFlowCategories: (keyof ChainData)[] = ["outgoing", "canonical", "incoming", "native"];

export const ownTokens: { [chain: Chain]: { ticker: string; address: string } } = {
  "bitcoin": { ticker: "BTC", address: "coingecko:bitcoin" },
  "ethereum": { ticker: "ETH", address: "coingecko:ethereum" },
  "mantle": { ticker: "MNT", address: "******************************************" },
  "arbitrum": { ticker: "ARB", address: "******************************************" },
  "nova": { ticker: "ARB", address: "******************************************" },
  "optimism": { ticker: "OP", address: "******************************************" },
  "polygon_zkevm": { ticker: "MATIC", address: "******************************************" },
  "starknet": { ticker: "STRK", address: "0x4718f5a0fc34cc1af16a1cdee98ffb20c31f5cd61d6ab07201858f4287c938d" },
  "everscale": { ticker: "EVER", address: "coingecko:everscale" },
  "celo": { ticker: "CELO", address: "coingecko:celo" },
  "iotex": { ticker: "IOTX", address: "coingecko:iotex" },
  "wanchain": { ticker: "WAN", address: "coingecko:wanchain" },
  "xdai": { ticker: "GNO", address: "******************************************" },
  "polygon": { ticker: "MATIC", address: "******************************************" },
  "avax": { ticker: "AVAX", address: "coingecko:avalanche-2" },
  "aurora": { ticker: "AURORA", address: "0x8bec47865ade3b172a928df8f990bc7f2a3b9f79" },
  "loopring": { ticker: "LRC", address: "0xbbbbca6a901c926f240b89eacb641d8aec7aeafd" },
  "immutablex": { ticker: "IMX", address: "0xf57e7e7c23978c3caec3c3548e3d615c346e79ff" },
  "immutable zkevm": { ticker: "IMX", address: "0xf57e7e7c23978c3caec3c3548e3d615c346e79ff" },
  "metis": { ticker: "METIS", address: "0xdeaddeaddeaddeaddeaddeaddeaddeaddead0000" },
  "meter": { ticker: "MTRG", address: "coingecko:meter" },
  "boba": { ticker: "BOBA", address: "0xa18bf3994c0cc6e3b63ac420308e5383f53120d7" },
  "solana": { ticker: "SOL", address: "coingecko:solana" },
  "manta": { ticker: "MANTA", address: "0x95cef13441be50d20ca4558cc0a27b601ac544e5" },
  "zklink": { ticker: "ZKL", address: "coingecko:zklink" },
  "rss3_vsl": { ticker: "RSS3", address: "rss3" },
  "blast": { ticker: "BLAST", address: "******************************************" },
  "mode": { ticker: "MODE", address: "******************************************" },
  "ronin": { ticker: "RON", address: "coingecko:ronin" },
  "near": { ticker: "NEAR", address: "coingecko:near" },
  "pulsechain": { ticker: "PLS", address: "coingecko:pulsechain" },
  "rootstock": { ticker: "RBTC", address: "coingecko:rootstock" },
  "stacks": { ticker: "STX", address: "coingecko:blockstack" },
  "fantom": { ticker: "FTM", address: "coingecko:fantom" },
  "thorchain": { ticker: "RUNE", address: "coingecko:thorchain" },
  "filecoin": { ticker: "FIL", address: "coingecko:filecoin" },
  "degen": { ticker: "DEGEN", address: "coingecko:degen-base" },
  "injective": { ticker: "INJ", address: "coingecko:injective-protocol" },
  // kekchain: { ticker: "" },
  "elysium": { ticker: "LAVA", address: "coingecko:lava" },
  "alephium": { ticker: "ALPH", address: "" },
  "rsk": { ticker: "RBTC", address: "coingecko:rootstock" },
  "cronos": { ticker: "CRO", address: "coingecko:crypto-com-chain" },
  "kava": { ticker: "KAVA", address: "coingecko:kava" },
  "bsc": { ticker: "BNB", address: "coingecko:binancecoin" },
  "tron": { ticker: "TRX", address: "coingecko:tron" },
  "kinto": { ticker: "KINTO", address: "" },
  "taiko": { ticker: "TAIKO", address: "******************************************" },
  "sanko": { ticker: "DMT", address: "******************************************" },
  "xai": { ticker: "XAI", address: "******************************************" },
  "osmosis": { ticker: "OSMO", address: "uosmo" },
  "lisk": { ticker: "LSK", address: "******************************************" },
  "sui": { ticker: "SUI", address: "coingecko:sui" },
  "zkswap": { ticker: "ZF", address: "******************************************" },
  "merlin": { ticker: "MERL", address: "******************************************" },
  "zksync": { ticker: "ZK", address: "******************************************" },
  "aptos": { ticker: "APT", address: "coingecko:aptos" },
  "hyperliquid": { ticker: "HYPE", address: "coingecko:hyperliquid" },
  "apechain": { ticker: "APE", address: "coingecko:apechain" },
  "scroll": { ticker: "SCR", address: "******************************************" },
  "era": { ticker: "ZK", address: "******************************************" },
  "fuel": { ticker: "FUEL", address: "coingecko:fuel-network" },
  "zircuit": { ticker: "ZRC", address: "******************************************" },
  "core": { ticker: "CORE", address: "******************************************" },
  "swellchain": { ticker: "SWELL", address: "coingecko:swell-network" },
  "cronos_zkevm": { ticker: "CRO", address: "coingecko:crypto-com-chain" },
  "sophon": { ticker: "SOPH", address: "coingecko:sophon" },
  "dydx": { ticker: "DYDX", address: "coingecko:dydx-chain" },
  "apex": { ticker: "APEX", address: "coingecko:apex-token-2" },
  "sonic": { ticker: "FTM", address: "coingecko:fantom" },
  //  "abstract": { ticker: "", address: "coingecko:" },
  "ancient8": { ticker: "A8", address: "coingecko:ancient8" },
  "cyber": { ticker: "CYBER", address: "coingecko:cyberconnect" },
  "fraxtal": { ticker: "FXS", address: "coingecko:frax-share" },
  "gravity": { ticker: "G", address: "coingecko:g-token" },
  // "5696": "karak",
  "kroma": { ticker: "KRO", address: "coingecko:kroma" },
  "orderly": { ticker: "ORDER", address: "coingecko:orderly-network" },
  //  "rari": { ticker: "", }
  // "5700": "redstone",
  "wc": { ticker: "WLD", address: "coingecko:worldcoin-wld" },
  // "5702": "zero_network",
  "zkfair": { ticker: "ZKF", address: "coingecko:zkfair" },
  "sxr": { ticker: "SX", address: "coingecko:sx-network-2" },
  "unichain": { ticker: "UNI", address: "coingecko:uniswap" },
  "berachain": { ticker: "BERA", address: "coingecko:berachain-bera" },
  "flow": { ticker: "FLOW", address: "coingecko:flow" },
  "mxczkevm": { ticker: "MXC", address: "coingecko:mxc" },
  // "lens": { ticker: "gho", address: "coingecko:gho" },
  "openzk": { ticker: "OZK", address: "coingecko:openzk-network" },
  "treasure": { ticker: "MAGIC", address: "coingecko:magic" },
  // "zkcandy": {}
  "ao": { ticker: "AO", address: "coingecko:ao-computers" },
  // embr: {}
  "xion": { ticker: "XION", address: "coingecko:xion-2" },
};
